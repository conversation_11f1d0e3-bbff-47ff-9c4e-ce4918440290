<?php

declare(strict_types=1);

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;
use Throwable;

final class SendEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 60;

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(10);
    }

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly string $toEmail,
        private readonly string $subject,
        private readonly string $content,
        private readonly ?string $toName = null,
        private readonly ?string $templateCode = null,
        private readonly array $variables = []
    ) {
        // Set queue name from config
        $this->onQueue(config('mail.queue.name', 'emails'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);
        
        try {
            // Log email sending attempt
            $this->logEmailAttempt();

            // Send the email
            Mail::html($this->content, function (Message $message) {
                $message->to($this->toEmail, $this->toName)
                       ->subject($this->subject)
                       ->from(
                           config('mail.from.address'),
                           config('mail.from.name')
                       );
            });

            $processingTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds

            // Log successful email sending
            $this->logEmailSuccess($processingTime);

        } catch (Throwable $exception) {
            $processingTime = (microtime(true) - $startTime) * 1000;
            
            // Log email failure
            $this->logEmailFailure($exception, $processingTime);
            
            // Re-throw to trigger retry mechanism
            throw $exception;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        // Log final failure after all retries exhausted
        Log::channel('email')->error('Email sending failed permanently after all retries', [
            'timestamp' => now()->toISOString(),
            'template_code' => $this->templateCode,
            'to_email' => $this->toEmail,
            'to_name' => $this->toName,
            'subject' => $this->subject,
            'status' => 'failed_permanently',
            'variables' => $this->variables,
            'error_message' => $exception->getMessage(),
            'attempts' => $this->attempts(),
            'max_tries' => $this->tries,
        ]);
    }

    /**
     * Log email sending attempt.
     */
    private function logEmailAttempt(): void
    {
        Log::channel('email')->info('Email sending attempt', [
            'timestamp' => now()->toISOString(),
            'template_code' => $this->templateCode,
            'to_email' => $this->toEmail,
            'to_name' => $this->toName,
            'subject' => $this->subject,
            'status' => 'sending',
            'variables' => $this->variables,
            'attempt' => $this->attempts(),
            'max_tries' => $this->tries,
        ]);
    }

    /**
     * Log successful email sending.
     */
    private function logEmailSuccess(float $processingTime): void
    {
        Log::channel('email')->info('Email sent successfully', [
            'timestamp' => now()->toISOString(),
            'template_code' => $this->templateCode,
            'to_email' => $this->toEmail,
            'to_name' => $this->toName,
            'subject' => $this->subject,
            'status' => 'sent',
            'variables' => $this->variables,
            'processing_time_ms' => round($processingTime, 2),
            'queue_delay_ms' => $this->getQueueDelay(),
        ]);
    }

    /**
     * Log email sending failure.
     */
    private function logEmailFailure(Throwable $exception, float $processingTime): void
    {
        Log::channel('email')->error('Email sending failed', [
            'timestamp' => now()->toISOString(),
            'template_code' => $this->templateCode,
            'to_email' => $this->toEmail,
            'to_name' => $this->toName,
            'subject' => $this->subject,
            'status' => 'failed',
            'variables' => $this->variables,
            'error_message' => $exception->getMessage(),
            'processing_time_ms' => round($processingTime, 2),
            'queue_delay_ms' => $this->getQueueDelay(),
            'attempt' => $this->attempts(),
            'max_tries' => $this->tries,
            'will_retry' => $this->attempts() < $this->tries,
        ]);
    }

    /**
     * Calculate queue delay in milliseconds.
     */
    private function getQueueDelay(): float
    {
        if ($this->job && method_exists($this->job, 'pushedAt')) {
            $pushedAt = $this->job->pushedAt();
            if ($pushedAt) {
                return (microtime(true) - $pushedAt) * 1000;
            }
        }
        
        return 0.0;
    }

    /**
     * Get the recipient email address (for testing purposes).
     */
    public function getToEmail(): string
    {
        return $this->toEmail;
    }

    /**
     * Get the recipient name (for testing purposes).
     */
    public function getToName(): ?string
    {
        return $this->toName;
    }

    /**
     * Get the email subject (for testing purposes).
     */
    public function getSubject(): string
    {
        return $this->subject;
    }

    /**
     * Get the email content (for testing purposes).
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * Get the template code (for testing purposes).
     */
    public function getTemplateCode(): ?string
    {
        return $this->templateCode;
    }

    /**
     * Get the template variables (for testing purposes).
     */
    public function getVariables(): array
    {
        return $this->variables;
    }
}
