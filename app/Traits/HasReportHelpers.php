<?php

declare(strict_types=1);

namespace App\Traits;

/**
 * Report Helpers Trait
 *
 * Provides common utility methods for report services to avoid code duplication.
 * Contains shared functionality used across different report service classes.
 */
trait HasReportHelpers
{
    /**
     * Get date format for MySQL based on grouping type
     *
     * Returns the appropriate MySQL DATE_FORMAT string for different time groupings.
     * Used in report queries to format dates according to the requested grouping period.
     *
     * @param string $groupBy The grouping type (hour, day, week, month, quarter, year)
     * @return string MySQL DATE_FORMAT string
     */
    protected function getDateFormat(string $groupBy): string
    {
        return match ($groupBy) {
            'hour' => '%Y-%m-%d %H:00',
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'quarter' => '%Y-Q%q',
            'year' => '%Y',
            default => '%Y-%m-%d',
        };
    }

    /**
     * Fill missing periods with zero values in chart data
     *
     * This method ensures that all periods within the specified date range are included
     * in the chart data, even if there's no actual data for those periods. Missing periods
     * are filled with zero values to provide a complete timeline for frontend charts.
     *
     * @param \Illuminate\Support\Collection $data The original data collection
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param string $groupBy The grouping type (hour, day, week, month, quarter, year)
     * @param string $timezone Timezone for date calculations
     * @param array $zeroTemplate Template array for zero values
     * @return \Illuminate\Support\Collection
     */
    protected function fillMissingPeriods(
        \Illuminate\Support\Collection $data,
        string $startDate,
        string $endDate,
        string $groupBy,
        string $timezone,
        array $zeroTemplate
    ): \Illuminate\Support\Collection {
        // Generate all periods within the date range
        $allPeriods = $this->generatePeriodRange($startDate, $endDate, $groupBy, $timezone);

        // Create a lookup array for existing data
        $existingData = $data->keyBy('period');

        // Fill missing periods with zero values
        $filledData = collect();
        foreach ($allPeriods as $period) {
            if ($existingData->has($period)) {
                $filledData->push($existingData->get($period));
            } else {
                $filledData->push(array_merge($zeroTemplate, ['period' => $period]));
            }
        }

        return $filledData;
    }

    /**
     * Generate all periods within a date range based on grouping type
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param string $groupBy The grouping type (hour, day, week, month, quarter, year)
     * @param string $timezone Timezone for date calculations
     * @return array Array of period strings
     */
    protected function generatePeriodRange(string $startDate, string $endDate, string $groupBy, string $timezone): array
    {
        $periods = [];
        $current = \Carbon\Carbon::parse($startDate, $timezone);
        $end = \Carbon\Carbon::parse($endDate, $timezone);

        // Adjust current date based on grouping type
        switch ($groupBy) {
            case 'hour':
                $current = $current->startOfHour();
                $end = $end->endOfDay()->endOfHour(); // Include all hours of the end date
                break;
            case 'day':
                $current = $current->startOfDay();
                $end = $end->endOfDay();
                break;
            case 'week':
                $current = $current->startOfWeek();
                $end = $end->endOfWeek();
                break;
            case 'month':
                $current = $current->startOfMonth();
                $end = $end->endOfMonth();
                break;
            case 'quarter':
                $current = $current->startOfQuarter();
                $end = $end->endOfQuarter();
                break;
            case 'year':
                $current = $current->startOfYear();
                $end = $end->endOfYear();
                break;
        }

        while ($current <= $end) {
            $periods[] = $this->formatPeriodForDatabase($current, $groupBy);

            // Increment based on grouping type
            switch ($groupBy) {
                case 'hour':
                    $current->addHour();
                    break;
                case 'day':
                    $current->addDay();
                    break;
                case 'week':
                    $current->addWeek();
                    break;
                case 'month':
                    $current->addMonth();
                    break;
                case 'quarter':
                    $current->addQuarter();
                    break;
                case 'year':
                    $current->addYear();
                    break;
            }
        }

        return $periods;
    }

    /**
     * Format a Carbon date instance to match database period format
     *
     * @param \Carbon\Carbon $date The date to format
     * @param string $groupBy The grouping type
     * @return string Formatted period string
     */
    protected function formatPeriodForDatabase(\Carbon\Carbon $date, string $groupBy): string
    {
        return match ($groupBy) {
            'hour' => $date->format('Y-m-d H:00'),
            'day' => $date->format('Y-m-d'),
            'week' => $date->format('Y-W'), // MySQL uses %Y-%u format which is similar to ISO week
            'month' => $date->format('Y-m'),
            'quarter' => $date->format('Y') . '-Q' . $date->quarter, // MySQL uses %Y-Q%q format
            'year' => $date->format('Y'),
            default => $date->format('Y-m-d'),
        };
    }
}
