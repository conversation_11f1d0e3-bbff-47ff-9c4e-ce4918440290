<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Base API Controller for version 1
 *
 * This controller provides common functionality for all API endpoints
 * including standardized response formats, error handling, and authorization.
 *
 * Uses <PERSON>vel's built-in authorizeResource method from AuthorizesRequests trait.
 */
abstract class ApiController extends Controller
{



    /**
     * Return a standardized success response
     */
    protected function successResponse(
        mixed $data = null,
        string $message = 'api.success',
        int $statusCode = 200
    ): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => __($message),
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ], $statusCode);
    }

    /**
     * Return a standardized error response
     */
    protected function errorResponse(
        string $message = 'errors.generic',
        mixed $errors = null,
        int $statusCode = 400,
        ?string $errorCode = null
    ): JsonResponse {
        return \App\Services\ApiExceptionService::createErrorResponse(__($message), $statusCode, $errors, $errorCode);
    }

    /**
     * Return a standardized validation error response
     */
    protected function validationErrorResponse(
        array $errors,
        string $message = 'errors.validation'
    ): JsonResponse {
        return \App\Services\ApiExceptionService::validationError($errors, __($message));
    }

    /**
     * Return a standardized not found response
     */
    protected function notFoundResponse(string $message = 'errors.not_found'): JsonResponse
    {
        return \App\Services\ApiExceptionService::notFoundError(__($message));
    }

    /**
     * Return a standardized unauthorized response
     */
    protected function unauthorizedResponse(string $message = 'errors.authentication'): JsonResponse
    {
        return \App\Services\ApiExceptionService::authenticationError(__($message));
    }

    /**
     * Return a standardized forbidden response
     */
    protected function forbiddenResponse(string $message = 'Forbidden'): JsonResponse
    {
        return \App\Services\ApiExceptionService::authorizationError($message);
    }
}
