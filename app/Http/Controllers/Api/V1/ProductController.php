<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\ProductIndexRequest;
use App\Http\Resources\Api\V1\ProductCollection;
use App\Http\Resources\Api\V1\ProductResource;
use App\Models\Product;
use App\Services\ProductService;
use Illuminate\Http\JsonResponse;

/**
 * Product Controller
 * 
 * Handles read-only product operations including listing and viewing individual products.
 * Products are synchronized from external systems and are not editable through this API.
 * Access is controlled by organization membership and product-level permissions.
 */
final class ProductController extends ApiController
{
    public function __construct(
        private readonly ProductService $productService
    ) {
        // Set up automatic authorization for Product resource
        $this->authorizeResource(Product::class, 'product');
    }

    /**
     * Display a listing of products.
     *
     * Supports filtering by:
     * - search: Searches in code, sku, and name fields
     * - organisation_id: Filters products by organization
     * - enabled: Filters by enabled status
     *
     * Results are paginated and filtered based on user permissions.
     */
    public function index(ProductIndexRequest $request): JsonResponse
    {
        $filters = $request->getProcessedData();

        $products = $this->productService->getProducts($filters);

        return $this->successResponse(
            new ProductCollection($products),
            'api.product.list_retrieved'
        );
    }

    /**
     * Display the specified product.
     * 
     * Returns detailed information about a single product including
     * organization details if the user has access.
     */
    public function show(Product $product): JsonResponse
    {
        // Load organization relationship for the response
        $product->load('organisation');

        return $this->successResponse(
            new ProductResource($product),
            'api.product.details_retrieved'
        );
    }
}
