<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\CreateFinancialStatementRequest;
use App\Http\Requests\Api\V1\UpdateFinancialStatementRequest;
use App\Http\Resources\Api\V1\FinancialStatementCollection;
use App\Http\Resources\Api\V1\FinancialStatementResource;
use App\Models\FinancialStatement;
use App\Services\FinancialStatementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class FinancialStatementController extends ApiController
{
    public function __construct(
        private readonly FinancialStatementService $financialStatementService
    ) {
        // Set up automatic authorization for FinancialStatement resource
        $this->authorizeResource(FinancialStatement::class, 'financial_statement');
    }

    /**
     * Display a listing of financial statements.
     * System admins can view all financial statements, organisation owners/members can view their own organisation's financial statements.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = (int) $request->get('per_page', 15);
        $status = $request->get('status');
        $reportType = $request->get('report_type');
        $organisationId = $request->get('organisation_id');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $user = $request->user();

        $financialStatements = $this->financialStatementService->getFinancialStatements(
            user: $user,
            perPage: $perPage,
            status: $status,
            reportType: $reportType,
            organisationId: $organisationId ? (int) $organisationId : null,
            startDate: $startDate,
            endDate: $endDate
        );

        return $this->successResponse(
            new FinancialStatementCollection($financialStatements),
            'api.financial_statement.list_retrieved'
        );
    }

    /**
     * Store a newly created financial statement.
     */
    public function store(CreateFinancialStatementRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $request->user();

        $financialStatement = $this->financialStatementService->create($data, $user);

        return $this->successResponse(
            new FinancialStatementResource($financialStatement->load('organisation')),
            'api.financial_statement.created',
            201
        );
    }

    /**
     * Display the specified financial statement.
     */
    public function show(FinancialStatement $financialStatement): JsonResponse
    {
        // Load organisation relationship
        $financialStatement->load('organisation');

        // Get product sales data
        $productSalesData = $this->financialStatementService->getProductSalesData($financialStatement);

        return $this->successResponse(
            new FinancialStatementResource($financialStatement, $productSalesData),
            'api.financial_statement.details_retrieved'
        );
    }

    /**
     * Update the specified financial statement.
     */
    public function update(UpdateFinancialStatementRequest $request, FinancialStatement $financialStatement): JsonResponse
    {
        $data = $request->validated();
        $user = $request->user();

        $updatedFinancialStatement = $this->financialStatementService->update($financialStatement, $data, $user);

        return $this->successResponse(
            new FinancialStatementResource($updatedFinancialStatement->load(['organisation', 'orders'])),
            'api.financial_statement.updated'
        );
    }

    /**
     * Publish the specified financial statement.
     * Changes status from pending_audit to published.
     */
    public function publish(FinancialStatement $financialStatement): JsonResponse
    {
        // Check permission for publish action
        $this->authorize('publish', $financialStatement);

        if ($financialStatement->isPublished()) {
            return $this->errorResponse(
                'api.financial_statement.already_published',
                null,
                422
            );
        }

        $publishedStatement = $this->financialStatementService->publish($financialStatement);

        return $this->successResponse(
            new FinancialStatementResource($publishedStatement->load('organisation')),
            'api.financial_statement.published'
        );
    }

    /**
     * Get financial statements by organisation.
     */
    public function byOrganisation(Request $request, int $organisationId): JsonResponse
    {
        // Check permission for viewing organisation's financial statements
        $this->authorize('viewByOrganisation', [FinancialStatement::class, $organisationId]);

        $perPage = (int) $request->get('per_page', 15);
        $status = $request->get('status');
        $reportType = $request->get('report_type');

        $financialStatements = $this->financialStatementService->getByOrganisation(
            organisationId: $organisationId,
            perPage: $perPage,
            status: $status,
            reportType: $reportType
        );

        return $this->successResponse(
            new FinancialStatementCollection($financialStatements),
            'api.financial_statement.organisation_list_retrieved'
        );
    }

    /**
     * Get financial statement statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();
        $organisationId = $request->get('organisation_id');

        // Check permission for viewing statistics
        $this->authorize('viewStatistics', [FinancialStatement::class, $organisationId]);

        $statistics = $this->financialStatementService->getStatistics(
            user: $user,
            organisationId: $organisationId ? (int) $organisationId : null
        );

        return $this->successResponse(
            $statistics,
            'api.financial_statement.statistics_retrieved'
        );
    }
}
