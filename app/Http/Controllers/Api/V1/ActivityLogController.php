<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\ActivityLogIndexRequest;
use App\Http\Resources\Api\V1\ActivityLogCollection;
use Illuminate\Http\JsonResponse;
use Spatie\Activitylog\Models\Activity;

/**
 * Activity Log Controller
 *
 * Handles activity log management for system administrators.
 * Provides read-only access to activity logs with filtering and pagination.
 * Authorization is handled by ActivityLogPolicy.
 */
final class ActivityLogController extends ApiController
{
    /**
     * Display a listing of activity logs.
     *
     * Only accessible by system administrators (root/admin with system guard).
     * Supports filtering by log name, subject type, causer, event type, and date range.
     * Authorization is handled by ActivityLogPolicy.
     */
    public function index(ActivityLogIndexRequest $request): JsonResponse
    {
        // Check authorization using ActivityLogPolicy
        $this->authorize('viewAny', Activity::class);

        $validated = $request->validated();
        $perPage = min((int) ($validated['per_page'] ?? 15), 100);
        
        $query = Activity::query()
            ->with(['causer:id,name,email', 'subject'])
            ->orderBy('created_at', 'desc');
        
        // Apply filters
        if (!empty($validated['log_name'])) {
            $query->where('log_name', $validated['log_name']);
        }
        
        if (!empty($validated['subject_type'])) {
            $query->where('subject_type', $validated['subject_type']);
        }
        
        if (!empty($validated['causer_id'])) {
            $query->where('causer_id', $validated['causer_id']);
        }
        
        if (!empty($validated['event'])) {
            $query->where('event', $validated['event']);
        }
        
        if (!empty($validated['date_from'])) {
            $query->whereDate('created_at', '>=', $validated['date_from']);
        }
        
        if (!empty($validated['date_to'])) {
            $query->whereDate('created_at', '<=', $validated['date_to']);
        }
        
        $activities = $query->paginate($perPage);
        
        return $this->successResponse(
            new ActivityLogCollection($activities),
            'api.activity_log.list_retrieved'
        );
    }
}
