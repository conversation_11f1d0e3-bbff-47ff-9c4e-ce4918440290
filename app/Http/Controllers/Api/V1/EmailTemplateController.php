<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\EmailTemplateRequest;
use App\Http\Resources\Api\V1\EmailTemplateCollection;
use App\Http\Resources\Api\V1\EmailTemplateResource;
use App\Models\EmailTemplate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class EmailTemplateController extends ApiController
{
    /**
     * EmailTemplateController constructor.
     */
    public function __construct()
    {
        // Register resource-based authorization
        $this->authorizeResource(EmailTemplate::class, 'email_template');
    }

    /**
     * Display a listing of email templates.
     * 
     * GET /api/v1/email-templates
     * Supports pagination, search, and filtering by active status.
     */
    public function index(Request $request): EmailTemplateCollection
    {
        $query = EmailTemplate::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by active status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Order by name by default
        $query->orderBy('name');

        // Paginate results
        $perPage = min((int) $request->input('per_page', 15), 100);
        $templates = $query->paginate($perPage);

        return new EmailTemplateCollection($templates);
    }

    /**
     * Store a newly created email template.
     * 
     * POST /api/v1/email-templates
     */
    public function store(EmailTemplateRequest $request): EmailTemplateResource
    {
        $template = EmailTemplate::create($request->validated());

        return new EmailTemplateResource($template);
    }

    /**
     * Display the specified email template.
     * 
     * GET /api/v1/email-templates/{id}
     */
    public function show(EmailTemplate $emailTemplate): EmailTemplateResource
    {
        return new EmailTemplateResource($emailTemplate);
    }

    /**
     * Update the specified email template.
     * 
     * PUT /api/v1/email-templates/{id}
     */
    public function update(EmailTemplateRequest $request, EmailTemplate $emailTemplate): EmailTemplateResource
    {
        $emailTemplate->update($request->validated());

        return new EmailTemplateResource($emailTemplate);
    }

    /**
     * Remove the specified email template.
     * 
     * DELETE /api/v1/email-templates/{id}
     */
    public function destroy(EmailTemplate $emailTemplate): JsonResponse
    {
        $emailTemplate->delete();

        return $this->successResponse(null, __('api.email_template.deleted'));
    }

    /**
     * Preview email template with provided variables.
     * 
     * POST /api/v1/email-templates/{id}/preview
     */
    public function preview(Request $request, EmailTemplate $emailTemplate): JsonResponse
    {
        // Authorize preview action
        $this->authorize('preview', $emailTemplate);

        // Validate preview request
        $request->validate([
            'variables' => 'sometimes|array',
            'variables.*' => 'string',
        ]);

        $variables = $request->input('variables', []);

        try {
            $rendered = $emailTemplate->render($variables);

            return $this->successResponse([
                'template' => [
                    'id' => $emailTemplate->id,
                    'name' => $emailTemplate->name,
                    'code' => $emailTemplate->code,
                ],
                'rendered' => $rendered,
                'variables_used' => $variables,
                'required_variables' => $emailTemplate->getRequiredVariables(),
            ], __('api.email_template.preview_generated'));

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse(
                'api.email_template.preview_failed',
                ['error' => $e->getMessage()],
                422
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                'api.email_template.preview_failed',
                ['error' => $e->getMessage()],
                500
            );
        }
    }
}
