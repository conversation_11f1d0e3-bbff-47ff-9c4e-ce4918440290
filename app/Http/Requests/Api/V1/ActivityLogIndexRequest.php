<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

final class ActivityLogIndexRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['sometimes', 'integer', 'min:1'],
            'per_page' => ['sometimes', 'integer', 'min:1', 'max:100'],
            'log_name' => ['sometimes', 'string', 'max:255'],
            'subject_type' => [
                'sometimes',
                'string',
                'in:App\Models\User,App\Models\Organisation,App\Models\Invitation,App\Models\Role,App\Models\ProductPermission,App\Models\SyncLog'
            ],
            'causer_id' => ['sometimes', 'string', 'max:255'],
            'event' => ['sometimes', 'string', 'in:created,updated,deleted'],
            'date_from' => ['sometimes', 'date', 'before_or_equal:date_to'],
            'date_to' => ['sometimes', 'date', 'after_or_equal:date_from', 'before_or_equal:today'],
        ];
    }

    public function messages(): array
    {
        return [
            'per_page.max' => __('validation.activity_log.per_page_max'),
            'subject_type.in' => __('validation.activity_log.subject_type_invalid'),
            'event.in' => __('validation.activity_log.event_invalid'),
            'date_from.before_or_equal' => __('validation.activity_log.date_from_before_date_to'),
            'date_to.after_or_equal' => __('validation.activity_log.date_to_after_date_from'),
            'date_to.before_or_equal' => __('validation.activity_log.date_to_not_future'),
        ];
    }

    public function attributes(): array
    {
        return [
            'per_page' => __('validation.attributes.per_page'),
            'log_name' => __('validation.attributes.log_name'),
            'subject_type' => __('validation.attributes.subject_type'),
            'causer_id' => __('validation.attributes.causer_id'),
            'event' => __('validation.attributes.event'),
            'date_from' => __('validation.attributes.date_from'),
            'date_to' => __('validation.attributes.date_to'),
        ];
    }

    protected function prepareForValidation(): void
    {
        if (!$this->has('per_page')) {
            $this->merge(['per_page' => 15]);
        }

        if (!$this->has('page')) {
            $this->merge(['page' => 1]);
        }
    }
}
