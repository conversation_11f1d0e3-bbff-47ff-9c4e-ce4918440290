<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Models\FinancialStatement;
use App\Rules\UniqueFinancialStatementOrdersByReportType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class UpdateFinancialStatementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization should be handled by policies
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $financialStatement = $this->route('financial_statement');

        return [
            'report_type' => [
                'sometimes',
                'string',
                Rule::in(FinancialStatement::REPORT_TYPES),
            ],
            'start_date' => [
                'sometimes',
                'date',
            ],
            'end_date' => [
                'sometimes',
                'date',
                'after_or_equal:start_date',
            ],
            'organisation_id' => [
                'sometimes',
                'integer',
                'exists:organisations,id',
                function ($attribute, $value, $fail) {
                    // Check if user has permission to update financial statements for this organisation
                    if (!$this->user()->hasSystemAdminAccess() && !$this->user()->hasOrganisationAdminAccess((int) $value)) {
                        $fail(__('validation.financial_statements.organisation_access_denied'));
                    }
                },
            ],
            'status' => [
                'sometimes',
                'string',
                Rule::in(FinancialStatement::REPORT_STATUSES),
                function ($attribute, $value, $fail) {
                    // Only allow status changes from pending_audit to published
                    $currentStatus = $this->route('financial_statement')->status ?? null;
                    if ($currentStatus === FinancialStatement::STATUS_PUBLISHED && $value === FinancialStatement::STATUS_PENDING_AUDIT) {
                        $fail(__('validation.financial_statements.cannot_unpublish'));
                    }
                },
            ],
            'total_amount' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'total_quantity' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'total_orders' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'remarks' => [
                'nullable',
                'string',
                'max:65535',
            ],
            'order_ids' => [
                'sometimes',
                'array',
                new UniqueFinancialStatementOrdersByReportType(
                    $this->input('report_type', $financialStatement->report_type ?? ''),
                    $financialStatement->id ?? null // Exclude current record from uniqueness check
                ),
            ],
            'order_ids.*' => [
                'integer',
                'exists:orders,id',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'report_type.in' => __('validation.financial_statements.report_type.in') . ': ' . implode(', ', FinancialStatement::REPORT_TYPES),
            'start_date.date' => __('validation.financial_statements.start_date.date'),
            'end_date.date' => __('validation.financial_statements.end_date.date'),
            'end_date.after_or_equal' => __('validation.financial_statements.end_date.after_or_equal'),
            'organisation_id.integer' => __('validation.financial_statements.organisation_id.integer'),
            'organisation_id.exists' => __('validation.financial_statements.organisation_id.exists'),
            'status.in' => __('validation.financial_statements.status.in') . ': ' . implode(', ', FinancialStatement::REPORT_STATUSES),
            'total_amount.integer' => __('validation.financial_statements.total_amount.integer'),
            'total_amount.min' => __('validation.financial_statements.total_amount.min'),
            'total_quantity.integer' => __('validation.financial_statements.total_quantity.integer'),
            'total_quantity.min' => __('validation.financial_statements.total_quantity.min'),
            'total_orders.integer' => __('validation.financial_statements.total_orders.integer'),
            'total_orders.min' => __('validation.financial_statements.total_orders.min'),
            'remarks.string' => __('validation.financial_statements.remarks.string'),
            'remarks.max' => __('validation.financial_statements.remarks.max'),
            'order_ids.array' => __('validation.financial_statements.order_ids.array'),
            'order_ids.*.integer' => __('validation.financial_statements.order_ids.integer'),
            'order_ids.*.exists' => __('validation.financial_statements.order_ids.exists'),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $financialStatement = $this->route('financial_statement');

            // Prevent updates to published financial statements (except for specific fields)
            if ($financialStatement && $financialStatement->isPublished()) {
                $allowedFields = ['remarks']; // Only remarks can be updated for published statements
                $updatedFields = array_keys($this->validated());
                $restrictedFields = array_diff($updatedFields, $allowedFields);

                if (!empty($restrictedFields)) {
                    $validator->errors()->add('status', __('validation.financial_statements.published_cannot_update', [
                        'fields' => implode(', ', $restrictedFields)
                    ]));
                }
            }
        });
    }
}
