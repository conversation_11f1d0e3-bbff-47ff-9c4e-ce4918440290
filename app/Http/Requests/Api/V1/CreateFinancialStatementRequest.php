<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Models\FinancialStatement;
use App\Rules\UniqueFinancialStatementOrdersByReportType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class CreateFinancialStatementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization should be handled by policies
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_type' => [
                'required',
                'string',
                Rule::in(FinancialStatement::REPORT_TYPES),
            ],
            'start_date' => [
                'required',
                'date',
            ],
            'end_date' => [
                'required',
                'date',
                'after_or_equal:start_date',
            ],
            'organisation_id' => [
                'required',
                'integer',
                'exists:organisations,id',
                function ($attribute, $value, $fail) {
                    // Check if user has permission to create financial statements for this organisation
                    if (!$this->user()->hasSystemAdminAccess() && !$this->user()->hasOrganisationAdminAccess((int) $value)) {
                        $fail(__('validation.financial_statements.organisation_access_denied'));
                    }
                },
            ],
            'status' => [
                'sometimes',
                'string',
                Rule::in(FinancialStatement::REPORT_STATUSES),
            ],
            'total_amount' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'total_quantity' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'total_orders' => [
                'sometimes',
                'integer',
                'min:0',
            ],
            'remarks' => [
                'nullable',
                'string',
                'max:65535',
            ],
            'order_ids' => [
                'sometimes',
                'array',
                new UniqueFinancialStatementOrdersByReportType(
                    $this->input('report_type', ''),
                    null // No exclusion for new records
                ),
            ],
            'order_ids.*' => [
                'integer',
                'exists:orders,id',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'report_type.required' => __('validation.financial_statements.report_type.required'),
            'report_type.in' => __('validation.financial_statements.report_type.in') . ': ' . implode(', ', FinancialStatement::REPORT_TYPES),
            'start_date.required' => __('validation.financial_statements.start_date.required'),
            'start_date.date' => __('validation.financial_statements.start_date.date'),
            'end_date.required' => __('validation.financial_statements.end_date.required'),
            'end_date.date' => __('validation.financial_statements.end_date.date'),
            'end_date.after_or_equal' => __('validation.financial_statements.end_date.after_or_equal'),
            'organisation_id.required' => __('validation.financial_statements.organisation_id.required'),
            'organisation_id.integer' => __('validation.financial_statements.organisation_id.integer'),
            'organisation_id.exists' => __('validation.financial_statements.organisation_id.exists'),
            'status.in' => __('validation.financial_statements.status.in') . ': ' . implode(', ', FinancialStatement::REPORT_STATUSES),
            'total_amount.integer' => __('validation.financial_statements.total_amount.integer'),
            'total_amount.min' => __('validation.financial_statements.total_amount.min'),
            'total_quantity.integer' => __('validation.financial_statements.total_quantity.integer'),
            'total_quantity.min' => __('validation.financial_statements.total_quantity.min'),
            'total_orders.integer' => __('validation.financial_statements.total_orders.integer'),
            'total_orders.min' => __('validation.financial_statements.total_orders.min'),
            'remarks.string' => __('validation.financial_statements.remarks.string'),
            'remarks.max' => __('validation.financial_statements.remarks.max'),
            'order_ids.array' => __('validation.financial_statements.order_ids.array'),
            'order_ids.*.integer' => __('validation.financial_statements.order_ids.integer'),
            'order_ids.*.exists' => __('validation.financial_statements.order_ids.exists'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Auto-generate report title if not provided
        if (!$this->has('report_title') && $this->has(['report_type', 'start_date', 'end_date', 'organisation_id'])) {
            // We'll generate the title in the service layer after creating the model
            // since we need the organisation relationship loaded
        }
    }

    /**
     * Get the validated data from the request with additional processing.
     *
     * @param string|null $key
     * @param mixed $default
     * @return mixed
     */
    public function validated($key = null, $default = null): mixed
    {
        $validated = parent::validated($key, $default);

        // Set default status if not provided
        if (!isset($validated['status'])) {
            $validated['status'] = FinancialStatement::STATUS_PENDING_AUDIT;
        }

        return $validated;
    }
}
