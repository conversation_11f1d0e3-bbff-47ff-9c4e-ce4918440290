<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Http\Requests\ApiRequest;
use Carbon\Carbon;

/**
 * Bulk Product Permission Request
 * 
 * Validates requests for bulk product permission operations (grant/revoke multiple products)
 */
final class BulkProductPermissionRequest extends ApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                'integer',
                'exists:users,id'
            ],
            'product_ids' => [
                'required',
                'array',
                'min:1',
                'max:100' // Limit bulk operations to prevent performance issues
            ],
            'product_ids.*' => [
                'integer',
                'exists:products,id'
            ],
            'permission_type' => [
                'sometimes',
                'string',
                'in:view-reports,edit-reports,export-reports'
            ],
            'expires_at' => [
                'nullable',
                'date',
                'after:now'
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'user_id.required' => 'User ID is required.',
            'user_id.exists' => 'The specified user does not exist.',
            'product_ids.required' => 'At least one product ID is required.',
            'product_ids.array' => 'Product IDs must be provided as an array.',
            'product_ids.min' => 'At least one product ID is required.',
            'product_ids.max' => 'Cannot process more than 100 products at once.',
            'product_ids.*.exists' => 'One or more specified products do not exist.',
            'permission_type.in' => 'Permission type must be one of: view-reports, edit-reports, export-reports.',
            'expires_at.after' => 'Expiration date must be in the future.',
            'expires_at.date' => 'Expiration date must be a valid date.',
            'notes.max' => 'Notes cannot exceed 1000 characters.'
        ]);
    }

    /**
     * Get custom attribute names for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'user_id' => 'user',
            'product_ids' => 'products',
            'product_ids.*' => 'product',
            'permission_type' => 'permission type',
            'expires_at' => 'expiration date',
            'notes' => 'notes'
        ]);
    }

    /**
     * Get the processed data with defaults applied.
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();
        
        // Set default permission type if not provided
        $data['permission_type'] = $data['permission_type'] ?? 'view-reports';
        
        // Parse expiration date if provided
        if (!empty($data['expires_at'])) {
            $data['expires_at'] = Carbon::parse($data['expires_at']);
        }
        
        // Ensure product_ids are unique
        $data['product_ids'] = array_unique($data['product_ids']);
        
        return $data;
    }
}
