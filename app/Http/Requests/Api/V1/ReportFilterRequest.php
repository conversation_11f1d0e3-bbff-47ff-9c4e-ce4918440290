<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Constants\ErrorCodes;
use App\Http\Requests\ApiRequest;
use App\Models\Organisation;
use App\Models\Product;
use App\Services\OrganisationService;
use App\Services\ReportAccessValidator;
use Carbon\Carbon;

/**
 * Report Filter Request
 *
 * Validates report filtering parameters including date ranges, grouping options,
 * country filters, and organization filters.
 *
 * Enhanced with product-level permission support:
 * - Automatically filters products based on user's accessible product IDs
 * - Supports both organization-level and product-level permissions
 * - Product permissions take priority over organization permissions
 * - Prevents cross-organization data access through filtering
 */
class ReportFilterRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled in the controller
    }

    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        $user = $this->user();
        $isSystemUser = $user && $user->hasSystemAdminAccess();

        return [
            'start_date' => ['required', 'date', 'before_or_equal:end_date'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date', 'before_or_equal:today'],
            'group_by' => ['sometimes', 'string', 'in:hour,day,week,month,quarter,year'],
            'countries' => ['sometimes', 'array'],
            'countries.*' => ['string', 'size:2'], // ISO 2-letter country codes
            'states' => ['sometimes', 'array'],
            'states.*' => ['string', 'in:completed,cancelled,processing,pending'],
            'payment_states' => ['sometimes', 'array'],
            'payment_states.*' => ['string', 'in:completed,pending,failed,cancelled'],
            'organisation_id' => $isSystemUser
                ? ['sometimes', 'integer', 'exists:organisations,id']
                : ['required_without:product_id', 'integer', 'exists:organisations,id'],
            'currency' => ['sometimes', 'string', 'size:3'], // ISO currency code
            'timezone' => ['sometimes', 'string', 'timezone'],
            'include_refunds' => ['sometimes', 'boolean'],
            'refund_status' => ['sometimes', 'string', 'in:success,pending,failed'],
            'product_id' => $isSystemUser
                ? ['sometimes', 'integer', 'exists:products,id']
                : ['required_without:organisation_id', 'integer', 'exists:products,id'],
        ];
    }

    /**
     * Get custom messages for validator errors
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'start_date.required' => __('validation.reports.start_date_required'),
            'end_date.required' => __('validation.reports.end_date_required'),
            'start_date.before_or_equal' => __('validation.reports.invalid_date_range'),
            'end_date.before_or_equal' => __('validation.reports.end_date_future'),
            'countries.*.size' => __('validation.reports.invalid_country_code'),
            'group_by.in' => __('validation.reports.invalid_group_by'),
            'states.*.in' => __('validation.reports.invalid_state'),
            'payment_states.*.in' => __('validation.reports.invalid_payment_state'),
            'organisation_id.required_without' => __('validation.reports.organisation_id_or_product_id_required'),
            'organisation_id.integer' => __('validation.reports.organisation_id_must_be_integer'),
            'organisation_id.exists' => __('validation.reports.organisation_not_found'),
            'currency.size' => __('validation.reports.invalid_currency_code'),
            'timezone.timezone' => __('validation.reports.invalid_timezone'),
            'product_id.required_without' => __('validation.reports.organisation_id_or_product_id_required'),
            'product_id.integer' => __('validation.reports.product_id_must_be_integer'),
            'product_id.exists' => __('validation.reports.product_not_found'),
        ]);
    }

    /**
     * Get custom attribute names for validator errors
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'start_date' => __('validation.attributes.start_date'),
            'end_date' => __('validation.attributes.end_date'),
            'group_by' => __('validation.attributes.group_by'),
            'countries' => __('validation.attributes.countries'),
            'states' => __('validation.attributes.states'),
            'payment_states' => __('validation.attributes.payment_states'),
            'organisation_id' => __('validation.attributes.organisation_id'),
            'currency' => __('validation.attributes.currency'),
            'timezone' => __('validation.attributes.timezone'),
            'include_refunds' => __('validation.attributes.include_refunds'),
            'refund_status' => __('validation.attributes.refund_status'),
            'product_id' => __('validation.attributes.product_id'),
        ]);
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $accessValidator = app(ReportAccessValidator::class);
            $accessValidator->validateReportAccess(
                $validator,
                $this->user(),
                $this->input('organisation_id') ? (int) $this->input('organisation_id') : null,
                $this->input('product_id') ? (int) $this->input('product_id') : null
            );
        });
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Set default timezone if not provided
        if (!$this->has('timezone')) {
            $this->merge(['timezone' => config('app.timezone')]);
        }

        // Set default group_by if not provided
        if (!$this->has('group_by')) {
            $this->merge(['group_by' => 'day']);
        }

        // Set default currency if not provided
        if (!$this->has('currency')) {
            $this->merge(['currency' => 'USD']);
        }

        // Convert include_refunds to boolean if provided as string
        if ($this->has('include_refunds') && is_string($this->include_refunds)) {
            $this->merge(['include_refunds' => filter_var($this->include_refunds, FILTER_VALIDATE_BOOLEAN)]);
        }

        // If organisation_id is not provided but product_id is, try to find organisation_id
        if (!$this->has('organisation_id') && $this->has('product_id')) {
            $organisationId = $this->getOrganisationIdFromProductId((int) $this->input('product_id'));
            if ($organisationId) {
                $this->merge(['organisation_id' => $organisationId]);
            }
        }
    }

    /**
     * Cache for user accessible product IDs to avoid repeated queries
     */
    private ?array $userAccessibleProductIdsCache = null;

    /**
     * Get the validated data with processed values
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();

        // Ensure dates are in proper format
        if (isset($data['start_date'])) {
            $data['start_date'] = Carbon::parse($data['start_date'])->startOfDay()->toDateTimeString();
        }
        if (isset($data['end_date'])) {
            $data['end_date'] = Carbon::parse($data['end_date'])->endOfDay()->toDateTimeString();
        }

        // Handle product filtering with enhanced permission logic
        $user = $this->user();
        $organisationId = $this->input('organisation_id');

        // If organisation_id was derived from product_id, ensure it's an integer
        if ($organisationId) {
            $organisationId = (int) $organisationId;
        }

        // Get user's accessible product IDs with caching to avoid repeated queries
        $userAccessibleProductIds = $this->getUserAccessibleProductIds($user, $organisationId);

        if (isset($data['product_id'])) {
            // If specific product_id is provided, validate user has access to it
            $specificProductId = (int) $data['product_id'];

            // Only include the specific product if user has access to it
            if (in_array($specificProductId, $userAccessibleProductIds, true)) {
                $data['product_ids'] = [$specificProductId];
            } else {
                // User doesn't have access to the specific product, return empty array
                // This will result in no data being returned, which is the correct behavior
                $data['product_ids'] = [];
            }

            // Remove the single product_id from the data array
            unset($data['product_id']);
        } else {
            // Use user's accessible products (organization + product permissions)
            // Product permissions take priority over organization permissions
            $data['product_ids'] = $userAccessibleProductIds;
        }

        return $data;
    }

    /**
     * Get user's accessible product IDs with caching to avoid repeated database queries
     */
    private function getUserAccessibleProductIds(?\App\Models\User $user, ?int $organisationId): array
    {
        // Return cached result if available
        if ($this->userAccessibleProductIdsCache !== null) {
            return $this->userAccessibleProductIdsCache;
        }

        // Use the User model's method directly to leverage its caching
        $this->userAccessibleProductIdsCache = $user ? $user->getAccessibleProductIds($organisationId) : [];

        return $this->userAccessibleProductIdsCache;
    }

    /**
     * Get organisation ID from product ID
     *
     * @param int $productId The product primary key ID
     * @return int|null The organisation ID if found, null otherwise
     */
    private function getOrganisationIdFromProductId(int $productId): ?int
    {
        $product = Product::find($productId);

        if (!$product || !$product->owner_id) {
            return null;
        }

        $organisation = Organisation::where('code', $product->owner_id)->first();

        return $organisation?->id;
    }
}
