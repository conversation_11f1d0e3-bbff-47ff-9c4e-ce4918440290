<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Models\EmailTemplate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class EmailTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Authorization is handled in the controller via policies
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $emailTemplateId = $this->route('email_template')?->id;

        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'min:1',
            ],
            'code' => [
                'required',
                'string',
                'max:100',
                'min:1',
                'regex:/^[a-z0-9_]+$/', // Only lowercase letters, numbers, and underscores
                Rule::unique('email_templates', 'code')->ignore($emailTemplateId),
            ],
            'subject' => [
                'required',
                'string',
                'max:500',
                'min:1',
            ],
            'content' => [
                'required',
                'string',
                'min:1',
            ],
            'variables' => [
                'nullable',
                'array',
            ],
            'variables.*' => [
                'string',
                'max:255',
                'regex:/^[a-z0-9_]+$/', // Only lowercase letters, numbers, and underscores
            ],
            'is_active' => [
                'sometimes',
                'boolean',
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => __('validation.attributes.name'),
            'code' => __('validation.attributes.code'),
            'subject' => __('validation.attributes.subject'),
            'content' => __('validation.attributes.content'),
            'variables' => __('validation.attributes.variables'),
            'is_active' => __('validation.attributes.is_active'),
            'description' => __('validation.attributes.description'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'code.regex' => __('validation.email_template.code_format'),
            'code.unique' => __('validation.email_template.code_unique'),
            'variables.*.regex' => __('validation.email_template.variable_format'),
            'content.required' => __('validation.email_template.content_required'),
            'subject.required' => __('validation.email_template.subject_required'),
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateTemplateVariables($validator);
        });
    }

    /**
     * Validate that variables in content match the variables array.
     */
    private function validateTemplateVariables($validator): void
    {
        $content = $this->input('content', '');
        $subject = $this->input('subject', '');
        $declaredVariables = $this->input('variables', []);

        // Extract variables from content and subject using regex
        $contentVariables = $this->extractVariablesFromText($content);
        $subjectVariables = $this->extractVariablesFromText($subject);
        $usedVariables = array_unique(array_merge($contentVariables, $subjectVariables));

        // Check if all used variables are declared
        $undeclaredVariables = array_diff($usedVariables, $declaredVariables);
        if (!empty($undeclaredVariables)) {
            $validator->errors()->add(
                'variables',
                __('validation.email_template.undeclared_variables', [
                    'variables' => implode(', ', $undeclaredVariables)
                ])
            );
        }

        // Check if all declared variables are used
        $unusedVariables = array_diff($declaredVariables, $usedVariables);
        if (!empty($unusedVariables)) {
            $validator->errors()->add(
                'variables',
                __('validation.email_template.unused_variables', [
                    'variables' => implode(', ', $unusedVariables)
                ])
            );
        }
    }

    /**
     * Extract variable names from text content.
     *
     * @param string $text
     * @return array<string>
     */
    private function extractVariablesFromText(string $text): array
    {
        preg_match_all('/\{([a-z0-9_]+)\}/', $text, $matches);
        return $matches[1] ?? [];
    }
}
