<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Http\Requests\ApiRequest;
use Carbon\Carbon;

/**
 * Organisation Product Permissions Request
 *
 * Validates requests for retrieving organisation product permissions grouped by users
 */
final class OrganisationProductPermissionsRequest extends ApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organisation_id' => [
                'required',
                'integer',
                'exists:organisations,id'
            ],
            'permission_type' => [
                'sometimes',
                'string',
                'in:view-reports,edit-reports,export-reports'
            ],
            'include_expired' => [
                'sometimes',
                'boolean'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'organisation_id.required' => 'Organisation ID is required.',
            'organisation_id.integer' => 'Organisation ID must be an integer.',
            'organisation_id.exists' => 'The specified organisation does not exist.',
            'permission_type.in' => 'Permission type must be one of: view-reports, edit-reports, export-reports.',
            'include_expired.boolean' => 'Include expired must be a boolean value.'
        ]);
    }

    /**
     * Get custom attribute names for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'organisation_id' => 'organisation',
            'permission_type' => 'permission type',
            'include_expired' => 'include expired permissions'
        ]);
    }

    /**
     * Get the processed data with defaults applied.
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();
        
        // Set default values
        $data['include_expired'] = $data['include_expired'] ?? false;
        
        return $data;
    }
}
