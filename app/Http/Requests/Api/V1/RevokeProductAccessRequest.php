<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Http\Requests\ApiRequest;

/**
 * Revoke Product Access Request
 * 
 * Validates requests for revoking product access permissions from users
 */
final class RevokeProductAccessRequest extends ApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'permission_type' => [
                'sometimes',
                'string',
                'in:view-reports,edit-reports,export-reports'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'permission_type.in' => 'Permission type must be one of: view-reports, edit-reports, export-reports.'
        ]);
    }

    /**
     * Get custom attribute names for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'permission_type' => 'permission type'
        ]);
    }

    /**
     * Get the processed data with defaults applied.
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();
        
        // Set default permission type if not provided
        $data['permission_type'] = $data['permission_type'] ?? 'view-reports';
        
        return $data;
    }
}
