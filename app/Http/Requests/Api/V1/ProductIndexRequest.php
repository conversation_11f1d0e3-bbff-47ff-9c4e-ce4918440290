<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

final class ProductIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by the controller policy
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'search' => ['sometimes', 'string', 'max:255'],
            'organisation_id' => [
                'sometimes',
                'integer',
                'exists:organisations,id',
                function ($attribute, $value, $fail) {
                    $user = $this->user();

                    // System administrators can access any organization
                    if ($user->hasSystemAdminAccess()) {
                        return;
                    }

                    // Check if user belongs to the organization
                    if (!$user->belongsToOrganisation((int) $value)) {
                        $fail(__('validation.organisation_access_denied'));
                    }
                }
            ],
            'enabled' => ['sometimes', 'in:true,false,1,0'],
            'per_page' => ['sometimes', 'integer', 'min:1', 'max:100'],
            'page' => ['sometimes', 'integer', 'min:1'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'search' => __('validation.attributes.search'),
            'organisation_id' => __('validation.attributes.organisation_id'),
            'enabled' => __('validation.attributes.enabled'),
            'per_page' => __('validation.attributes.per_page'),
            'page' => __('validation.attributes.page'),
        ];
    }

    /**
     * Get the processed data for the request.
     *
     * @return array<string, mixed>
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();

        // Set default values
        $data['per_page'] = $data['per_page'] ?? 15;
        $data['page'] = $data['page'] ?? 1;

        // Convert enabled parameter to boolean
        if (isset($data['enabled'])) {
            $data['enabled'] = in_array($data['enabled'], ['true', '1', 1, true], true);
        }

        return $data;
    }
}
