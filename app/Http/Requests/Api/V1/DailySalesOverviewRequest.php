<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Http\Requests\ApiRequest;
use App\Models\Product;
use App\Services\ReportAccessValidator;
use App\Traits\HasPermissionHelpers;

/**
 * Daily Sales Overview Request
 *
 * Validates request parameters for daily sales overview API endpoint.
 * Supports optional product_id parameter for product-specific overview data.
 */
final class DailySalesOverviewRequest extends ApiRequest
{
    use HasPermissionHelpers;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $user = $this->user();
        $isSystemUser = $user && $user->hasSystemAdminAccess();

        return [
            'timezone' => ['sometimes', 'string', 'timezone'],
            'organisation_id' => $isSystemUser
                ? ['sometimes', 'integer', 'exists:organisations,id']
                : ['required_without:product_id', 'integer', 'exists:organisations,id'],
            'product_id' => $isSystemUser
                ? ['sometimes', 'integer', 'exists:products,id']
                : ['required_without:organisation_id', 'integer', 'exists:products,id'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'organisation_id.required_without' => __('validation.required_without', [
                'attribute' => __('validation.attributes.organisation_id'),
                'values' => __('validation.attributes.product_id')
            ]),
            'product_id.required_without' => __('validation.required_without', [
                'attribute' => __('validation.attributes.product_id'),
                'values' => __('validation.attributes.organisation_id')
            ]),
            'organisation_id.exists' => __('validation.exists', [
                'attribute' => __('validation.attributes.organisation_id')
            ]),
            'product_id.exists' => __('validation.exists', [
                'attribute' => __('validation.attributes.product_id')
            ]),
            'timezone.timezone' => __('validation.timezone', [
                'attribute' => __('validation.attributes.timezone')
            ]),
        ];
    }

    /**
     * Get processed data for the service layer
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();
        $user = $this->user();

        // Set default timezone if not provided
        if (!isset($data['timezone'])) {
            $data['timezone'] = config('app.timezone');
        }

        // Handle organisation_id derivation from product_id if needed
        if (!isset($data['organisation_id']) && isset($data['product_id'])) {
            $product = Product::find($data['product_id']);
            if ($product && $product->organisation) {
                $data['organisation_id'] = $product->organisation->id;
            }
        }

        // Get organisation IDs for authorization
        $organisationIds = [];
        if (isset($data['organisation_id'])) {
            $organisationIds = [$data['organisation_id']];
        } elseif (!$user->hasSystemAdminAccess()) {
            // For non-system users, get their accessible organisation IDs
            $organisationIds = $user->organisations()->pluck('organisations.id')->toArray();
        }

        $data['organisation_ids'] = $organisationIds;

        // Ensure product_id is an integer if provided
        if (isset($data['product_id'])) {
            $data['product_id'] = (int) $data['product_id'];
        }

        return $data;
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Derive organisation_id from product_id if needed for validation
            $organisationId = $this->input('organisation_id') ? (int) $this->input('organisation_id') : null;
            $productId = $this->input('product_id') ? (int) $this->input('product_id') : null;

            // If organisation_id is not provided but product_id is, derive it
            if (!$organisationId && $productId) {
                $product = Product::find($productId);
                if ($product && $product->organisation) {
                    $organisationId = $product->organisation->id;
                }
            }

            $accessValidator = app(ReportAccessValidator::class);
            $accessValidator->validateReportAccess(
                $validator,
                $this->user(),
                $organisationId,
                $productId
            );
        });
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Set default timezone if not provided
        if (!$this->has('timezone')) {
            $this->merge(['timezone' => config('app.timezone')]);
        }
    }

    /**
     * Get the organisation ID for authorization purposes
     */
    public function getOrganisationIdForAuthorization(): ?int
    {
        $processedData = $this->getProcessedData();

        // Return the first organisation ID if available
        if (!empty($processedData['organisation_ids'])) {
            return (int) $processedData['organisation_ids'][0];
        }

        return null;
    }
}
