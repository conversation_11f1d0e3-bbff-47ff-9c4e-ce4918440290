<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Base API Resource
 * 
 * Provides standardized response format for API resources
 */
abstract class ApiResource extends JsonResource
{
    /**
     * Transform the resource into an array
     */
    public function toArray(Request $request): array
    {
        return parent::toArray($request);
    }

    /**
     * Get additional data that should be returned with the resource array
     */
    public function with(Request $request): array
    {
        return [
            'success' => true,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Customize the response for a request
     */
    public function withResponse(Request $request, $response): void
    {
        $response->header('Content-Type', 'application/json');
        $response->header('X-API-Version', '1.0');
    }

    /**
     * Format date for chart display with smart year handling
     *
     * This method intelligently formats dates to avoid confusion when data spans multiple years or days.
     * For hourly data spanning multiple days, it shows date for 00:00 hours and time for other hours.
     * For hourly data within a single day, it uses HH:00 format.
     * For single-year data, it uses MM/DD format.
     * For multi-year data, it shows the year for the first occurrence of each year
     * (01/01 becomes "2023") and uses MM/DD for other dates.
     */
    protected function formatDateForChart(string $period, array $chartData): string
    {
        $currentDate = strtotime($period);
        $currentYear = (int) date('Y', $currentDate);
        $currentMonth = (int) date('n', $currentDate);
        $currentDay = (int) date('j', $currentDate);
        $currentHour = (int) date('H', $currentDate);

        // Check if this is hourly data by examining the period format
        // Hourly data contains time information (e.g., "2024-01-01 14:00")
        if (strpos($period, ':') !== false) {
            // Check if hourly data spans multiple days
            $dates = array_unique(array_map(function ($item) {
                return date('Y-m-d', strtotime($item['period']));
            }, $chartData));

            // If hourly data spans multiple days, show date for 00:00 hours
            if (count($dates) > 1 && $currentHour === 0) {
                return date('m/d', $currentDate);
            }

            // For other hours or single-day data, show time format
            return date('H:i', $currentDate);
        }

        // Check if data spans multiple years
        $years = array_unique(array_map(function ($item) {
            return (int) date('Y', strtotime($item['period']));
        }, $chartData));

        // If data spans only one year, use simple MM/DD format
        if (count($years) <= 1) {
            return date('m/d', $currentDate);
        }

        // For multi-year data, show year for January 1st of each year
        if ($currentMonth === 1 && $currentDay === 1) {
            return (string) $currentYear;
        }

        // For other dates in multi-year data, use MM/DD format
        return date('m/d', $currentDate);
    }
}
