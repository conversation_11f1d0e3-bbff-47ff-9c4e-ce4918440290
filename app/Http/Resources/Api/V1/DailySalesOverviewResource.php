<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Daily Sales Overview Resource
 * 
 * Transforms daily sales overview data for API responses with formatted values and comparisons
 */
final class DailySalesOverviewResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $data = $this->resource;
        
        return [
            'overview' => [
                'sales_amount' => [
                    'today' => [
                        'value' => $data['today']['sales_amount'],
                        'formatted' => $data['today']['sales_amount_formatted'],
                        'currency' => 'USD', // Could be made dynamic based on request
                    ],
                    'yesterday' => [
                        'value' => $data['yesterday']['sales_amount'],
                        'formatted' => $data['yesterday']['sales_amount_formatted'],
                        'currency' => 'USD',
                    ],
                    'comparison' => $data['comparisons']['sales_amount'],
                ],
                'sales_quantity' => [
                    'today' => [
                        'value' => $data['today']['sales_quantity'],
                    ],
                    'yesterday' => [
                        'value' => $data['yesterday']['sales_quantity'],
                    ],
                    'comparison' => $data['comparisons']['sales_quantity'],
                ],
                'new_customers' => [
                    'today' => [
                        'value' => $data['today']['new_customers'],
                    ],
                    'yesterday' => [
                        'value' => $data['yesterday']['new_customers'],
                    ],
                    // New customers don't have a meaningful comparison since it's about first-time customers
                ],
                'refunds' => [
                    'count' => [
                        'today' => [
                            'value' => $data['today']['refund_count'],
                        ],
                        'yesterday' => [
                            'value' => $data['yesterday']['refund_count'],
                        ],
                        'comparison' => $data['comparisons']['refund_count'],
                    ],
                    'rate' => [
                        'today' => [
                            'value' => $data['today']['refund_rate'],
                            'formatted' => $data['today']['refund_rate'] . '%',
                        ],
                        'yesterday' => [
                            'value' => $data['yesterday']['refund_rate'],
                            'formatted' => $data['yesterday']['refund_rate'] . '%',
                        ],
                        'comparison' => $data['comparisons']['refund_rate'],
                    ],
                ],
            ],
            'additional_metrics' => [
                'today' => [
                    'total_orders' => $data['today']['total_orders'],
                    'unique_customers' => $data['today']['unique_customers'],
                    'total_refunds' => [
                        'value' => $data['today']['total_refunds'],
                        'formatted' => $data['today']['total_refunds_formatted'],
                        'currency' => 'USD',
                    ],
                ],
                'yesterday' => [
                    'total_orders' => $data['yesterday']['total_orders'],
                    'unique_customers' => $data['yesterday']['unique_customers'],
                    'total_refunds' => [
                        'value' => $data['yesterday']['total_refunds'],
                        'formatted' => $data['yesterday']['total_refunds_formatted'],
                        'currency' => 'USD',
                    ],
                ],
            ],
            'meta' => [
                'date' => $data['meta']['date'],
                'timezone' => $data['meta']['timezone'],
                'product_id' => $data['meta']['product_id'],
                'is_product_specific' => $data['meta']['is_product_specific'],
                'generated_at' => now()->toISOString(),
            ],
        ];
    }
}
