<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

final class EmailTemplateResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'subject' => $this->subject,
            'content' => $this->content,
            'variables' => $this->variables ?? [],
            'is_active' => $this->is_active,
            'description' => $this->description,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Additional computed fields
            'required_variables' => $this->getRequiredVariables(),
            'is_valid' => $this->isValid(),
            'variable_count' => count($this->variables ?? []),
        ];
    }
}
