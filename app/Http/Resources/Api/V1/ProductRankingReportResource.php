<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Product Ranking Report Resource
 * 
 * Transforms product ranking report data for API responses with chart-compatible format
 */
final class ProductRankingReportResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $data = $this->resource;

        $transformed = [];

        // Transform sales rankings data for table display
        if (isset($data['sales_rankings']) && is_array($data['sales_rankings'])) {
            $transformed['sales_rankings'] = $this->transformProductRankings($data['sales_rankings']);
            $transformed['sales_ranking_chart'] = $this->transformSalesRankingChart($data['sales_rankings']);
        }

        // Transform quantity rankings data for table display
        if (isset($data['quantity_rankings']) && is_array($data['quantity_rankings'])) {
            $transformed['quantity_rankings'] = $this->transformProductRankings($data['quantity_rankings']);
            $transformed['quantity_ranking_chart'] = $this->transformQuantityRankingChart($data['quantity_rankings']);
        }

        // Add summary information
        $transformed['summary'] = [
            'total_products' => $data['total_products'] ?? 0,
            'limit' => $data['limit'] ?? 10,
            'period' => $data['period'] ?? [],
        ];

        return $transformed;
    }

    /**
     * Transform product rankings data for table display
     */
    private function transformProductRankings(array $rankings): array
    {
        return array_map(function ($item) {
            return [
                'rank' => $item['rank'],
                'store_variant_id' => $item['store_variant_id'],
                'product_name' => $item['product_name'],
                'product_slug' => $item['product_slug'],
                'product_package' => $item['product_package'],
                'total_quantity' => $item['total_quantity'],
                'total_sales' => $item['total_sales'],
                'total_sales_formatted' => $item['total_sales_formatted'],
            ];
        }, $rankings);
    }

    /**
     * Transform sales ranking data for bar chart
     */
    private function transformSalesRankingChart(array $rankings): array
    {
        $productNames = [];
        $salesAmounts = [];

        // Take top 10 for chart display
        $topRankings = array_slice($rankings, 0, 10);

        foreach ($topRankings as $item) {
            // Truncate long product names for better chart display
            $productName = strlen($item['product_name']) > 20 
                ? substr($item['product_name'], 0, 17) . '...' 
                : $item['product_name'];
            
            $productNames[] = $productName;
            $salesAmounts[] = (int) ($item['total_sales'] / 100); // Convert from cents to dollars
        }

        return [
            'xAxis' => [
                'data' => $productNames
            ],
            'series' => [
                [
                    'name' => __('api.reports.chart_labels.sales_amount'),
                    'data' => $salesAmounts
                ]
            ]
        ];
    }

    /**
     * Transform quantity ranking data for bar chart
     */
    private function transformQuantityRankingChart(array $rankings): array
    {
        $productNames = [];
        $quantities = [];

        // Take top 10 for chart display
        $topRankings = array_slice($rankings, 0, 10);

        foreach ($topRankings as $item) {
            // Truncate long product names for better chart display
            $productName = strlen($item['product_name']) > 20 
                ? substr($item['product_name'], 0, 17) . '...' 
                : $item['product_name'];
            
            $productNames[] = $productName;
            $quantities[] = $item['total_quantity'];
        }

        return [
            'xAxis' => [
                'data' => $productNames
            ],
            'series' => [
                [
                    'name' => __('api.reports.chart_labels.quantity'),
                    'data' => $quantities
                ]
            ]
        ];
    }
}
