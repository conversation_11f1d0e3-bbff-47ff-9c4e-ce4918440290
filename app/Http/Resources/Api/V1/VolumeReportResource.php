<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Volume Report Resource
 *
 * Transforms volume report data for API responses with chart-compatible format
 */
final class VolumeReportResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $data = $this->resource;

        $transformed = [];

        // Transform chart_data for daily sales quantity trend chart
        if (isset($data['chart_data']) && is_array($data['chart_data'])) {
            $transformed['daily_quantity_chart'] = $this->transformDailyQuantityChart($data['chart_data']);
        } else {
            // Provide empty chart structure when no data
            $transformed['daily_quantity_chart'] = [
                'xAxis' => ['data' => []],
                'series' => [['data' => []]]
            ];
        }

        // Transform region_data for pie charts
        if (isset($data['region_data']) && is_array($data['region_data'])) {
            $transformed['regional_sales_quantity_chart'] = $this->transformRegionalSalesQuantityChart($data['region_data']);
        } else {
            // Provide empty chart structure when no data
            $transformed['regional_sales_quantity_chart'] = [
                'series' => [['data' => []]]
            ];
        }

        return $transformed;
    }

    /**
     * Transform daily quantity data for line chart
     */
    private function transformDailyQuantityChart(array $chartData): array
    {
        $dates = [];
        $quantities = [];

        foreach ($chartData as $item) {
            // Use smart date formatting to avoid confusion with multi-year data
            $date = $this->formatDateForChart($item['period'], $chartData);
            $dates[] = $date;
            $quantities[] = (int) $item['total_quantity'];
        }

        return [
            'xAxis' => [
                'data' => $dates
            ],
            'series' => [
                [
                    'data' => $quantities
                ]
            ]
        ];
    }

    /**
     * Transform regional sales quantity data for pie chart
     */
    private function transformRegionalSalesQuantityChart(array $regionData): array
    {
        $aggregatedData = [];

        // Aggregate data by region name to avoid duplicates
        foreach ($regionData as $item) {
            $regionName = $this->getRegionDisplayName($item['country']);
            $quantityValue = (int) $item['total_quantity'];

            if (isset($aggregatedData[$regionName])) {
                $aggregatedData[$regionName] += $quantityValue;
            } else {
                $aggregatedData[$regionName] = $quantityValue;
            }
        }

        // Convert aggregated data to chart format
        $data = [];
        foreach ($aggregatedData as $regionName => $totalValue) {
            $data[] = [
                'name' => $regionName,
                'value' => $totalValue
            ];
        }

        return [
            'series' => [
                [
                    'data' => $data
                ]
            ]
        ];
    }

    /**
     * Get display name for region/country using translation
     */
    private function getRegionDisplayName(string $country): string
    {
        $translationKey = "api.reports.regions.{$country}";
        $translated = __($translationKey);

        // If translation key not found, return the fallback 'other'
        if ($translated === $translationKey) {
            return __('api.reports.regions.other');
        }

        return $translated;
    }

}
