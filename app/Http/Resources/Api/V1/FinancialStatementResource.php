<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

final class FinancialStatementResource extends ApiResource
{
    /**
     * Product sales data to include in the response.
     */
    private ?array $productSalesData = null;

    /**
     * Create a new resource instance.
     */
    public function __construct($resource, mixed $productSalesDataOrKey = null)
    {
        parent::__construct($resource);

        // Handle the case where this is called from collection mapping (second param is index)
        // vs direct instantiation (second param is product sales data array)
        if (is_array($productSalesDataOrKey)) {
            $this->productSalesData = $productSalesDataOrKey;
        } else {
            $this->productSalesData = null;
        }
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'report_type' => $this->report_type,
            'report_title' => $this->report_title,
            'report_code' => $this->report_code,
            'start_date' => $this->start_date?->toDateString(),
            'end_date' => $this->end_date?->toDateString(),
            'organisation_id' => $this->organisation_id,
            'status' => $this->status,
            'total_amount' => $this->total_amount,
            'total_amount_in_currency' => $this->total_amount_in_currency,
            'total_quantity' => $this->total_quantity,
            'total_orders' => $this->total_orders,
            'remarks' => $this->remarks,
            'is_monthly' => $this->isMonthly(),
            'is_quarterly' => $this->isQuarterly(),
            'is_yearly' => $this->isYearly(),
            'is_custom' => $this->isCustom(),
            'is_pending_audit' => $this->isPendingAudit(),
            'is_published' => $this->isPublished(),
            'period_description' => $this->getPeriodDescription(),
            'organisation' => $this->whenLoaded('organisation', function () {
                return [
                    'id' => $this->organisation->id,
                    'name' => $this->organisation->name,
                    'code' => $this->organisation->code,
                    'status' => $this->organisation->status,
                ];
            }),
            'product_sales' => $this->productSalesData ?? [],
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
