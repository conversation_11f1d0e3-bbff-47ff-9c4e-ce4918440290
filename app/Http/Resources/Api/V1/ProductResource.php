<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

final class ProductResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'store_variant_id' => $this->store_variant_id,
            'owner_id' => $this->owner_id,
            'sku' => $this->sku,
            'code' => $this->code,
            'name' => $this->name,
            'slug' => $this->slug,
            'enabled' => $this->enabled,
            'release_date' => $this->release_date?->toISOString(),
            'package' => $this->package,
            'current_price' => $this->current_price,
            'original_price' => $this->original_price,
            'minimum_price' => $this->minimum_price,
            'lowest_price_before_discount' => $this->lowest_price_before_discount,
            'price_history' => $this->price_history,
            'store_product_updated_at' => $this->store_product_updated_at?->toISOString(),
            'store_variant_updated_at' => $this->store_variant_updated_at?->toISOString(),
            'organisation' => $this->whenLoaded('organisation', function () {
                return [
                    'id' => $this->organisation->id,
                    'name' => $this->organisation->name,
                    'code' => $this->organisation->code,
                    'status' => $this->organisation->status,
                ];
            }),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
