<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Sync Log Resource
 * 
 * Transforms SyncLog model data for API responses
 */
final class SyncLogResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'batch_id' => $this->batch_id,
            'sync_type' => $this->sync_type,
            'status' => $this->status,
            'status_label' => __('sync.sync_status.' . $this->status),
            'started_at' => $this->started_at?->toISOString(),
            'completed_at' => $this->completed_at?->toISOString(),
            'duration' => $this->getDurationAttribute(),
            'total_records' => $this->total_records,
            'processed_records' => $this->processed_records,
            'success_records' => $this->success_records,
            'failed_records' => $this->failed_records,
            'skipped_records' => $this->total_records - $this->processed_records,
            'progress_percentage' => $this->progress_percentage,
            'summary' => $this->summary,
            'error_message' => $this->error_message,
            'sync_config' => $this->sync_config,
            'validation_results' => $this->validation_results,
            'records' => SyncRecordResource::collection($this->whenLoaded('records')),
            'records_count' => $this->whenCounted('records'),
            'failed_records_count' => $this->when(
                $this->relationLoaded('records'),
                fn() => $this->records->where('status', 'failed')->count()
            ),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }

    /**
     * Get the duration of the sync operation.
     */
    private function getDurationAttribute(): ?string
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        $duration = $this->completed_at->diffInSeconds($this->started_at);
        
        if ($duration < 60) {
            return $duration . 's';
        } elseif ($duration < 3600) {
            return round($duration / 60, 1) . 'm';
        } else {
            return round($duration / 3600, 1) . 'h';
        }
    }
}
