<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Sync Record Resource
 *
 * Transforms SyncRecord model data for API responses.
 *
 * Note: Access to this resource is already protected by SyncPolicy,
 * which ensures only system administrators can access sync endpoints.
 */
final class SyncRecordResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'batch_id' => $this->batch_id,
            'source_table' => $this->source_table,
            'source_id' => $this->source_id,
            'target_table' => $this->target_table,
            'target_id' => $this->target_id,
            'status' => $this->status,
            'status_label' => __('sync.record_status.' . $this->status),
            'error_message' => $this->error_message,
            'source_data' => $this->source_data,
            'transformed_data' => $this->transformed_data,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }
}
