<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use App\Services\ActivityLogFormatterService;
use Illuminate\Http\Request;

/**
 * Activity Log Resource
 *
 * Transforms activity log data for API responses.
 * Includes causer information, subject details, formatted properties, and human-readable descriptions.
 */
final class ActivityLogResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $formatter = app(ActivityLogFormatterService::class);

        return [
            'id' => $this->id,
            'log_name' => $this->log_name,
            'description' => $this->description,
            'formatted_description' => $formatter->formatDescription($this->resource),
            'subject_type' => $this->subject_type,
            'subject_id' => $this->subject_id,
            'subject' => $this->when($this->relationLoaded('subject'), function () {
                return $this->formatSubject();
            }),
            'causer_type' => $this->causer_type,
            'causer_id' => $this->causer_id,
            'causer' => $this->when($this->relationLoaded('causer'), function () {
                return $this->formatCauser();
            }),
            'properties' => $this->properties,
            'event' => $this->event,
            'batch_uuid' => $this->batch_uuid,
            'created_at' => $this->created_at?->toISOString(),
        ];
    }

    /**
     * Format the subject information for display.
     */
    private function formatSubject(): ?array
    {
        if (!$this->subject) {
            return null;
        }

        $subject = $this->subject;
        $baseInfo = [
            'id' => $subject->getKey(),
            'type' => class_basename($subject),
        ];

        // Add type-specific information
        return match (class_basename($subject)) {
            'User' => array_merge($baseInfo, [
                'name' => $subject->name ?? null,
                'email' => $subject->email ?? null,
            ]),
            'Organisation' => array_merge($baseInfo, [
                'name' => $subject->name ?? null,
                'code' => $subject->code ?? null,
                'status' => $subject->status ?? null,
            ]),
            'Invitation' => array_merge($baseInfo, [
                'role' => $subject->role ?? null,
                'email_restriction' => $subject->email_restriction ?? null,
            ]),
            'Role' => array_merge($baseInfo, [
                'name' => $subject->name ?? null,
                'guard_name' => $subject->guard_name ?? null,
            ]),
            'ProductPermission' => array_merge($baseInfo, [
                'permission_type' => $subject->permission_type ?? null,
                'user_id' => $subject->user_id ?? null,
                'product_id' => $subject->product_id ?? null,
            ]),
            'SyncLog' => array_merge($baseInfo, [
                'sync_type' => $subject->sync_type ?? null,
                'status' => $subject->status ?? null,
                'batch_id' => $subject->batch_id ?? null,
            ]),
            default => $baseInfo,
        };
    }

    /**
     * Format the causer (user who performed the action) information.
     */
    private function formatCauser(): ?array
    {
        if (!$this->causer) {
            return null;
        }

        return [
            'id' => $this->causer->id,
            'name' => $this->causer->name,
            'email' => $this->causer->email,
        ];
    }
}
