<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Sales Report Resource
 *
 * Transforms sales report data for API responses with chart-compatible format
 */
final class SalesReportResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $data = $this->resource;

        $transformed = [];

        // Transform chart_data for daily sales amount trend chart
        if (isset($data['chart_data']) && is_array($data['chart_data'])) {
            $transformed['daily_sales_chart'] = $this->transformDailySalesChart($data['chart_data']);
            $transformed['dual_axis_chart'] = $this->transformDualAxisChart($data['chart_data']);
        } else {
            // Provide empty chart structure when no data
            $transformed['daily_sales_chart'] = [
                'xAxis' => ['data' => []],
                'series' => [['data' => []]]
            ];
            $transformed['dual_axis_chart'] = [
                'xAxis' => ['data' => []],
                'series' => [
                    ['name' => __('api.reports.chart_labels.order_amount'), 'yAxisIndex' => 0, 'data' => []],
                    ['name' => __('api.reports.chart_labels.order_quantity'), 'yAxisIndex' => 1, 'data' => []]
                ]
            ];
        }

        // Transform region_data for pie charts
        if (isset($data['region_data']) && is_array($data['region_data'])) {
            $transformed['regional_sales_amount_chart'] = $this->transformRegionalSalesAmountChart($data['region_data']);
        } else {
            // Provide empty chart structure when no data
            $transformed['regional_sales_amount_chart'] = [
                'series' => [['data' => []]]
            ];
        }

        return $transformed;
    }

    /**
     * Transform daily sales data for line chart
     */
    private function transformDailySalesChart(array $chartData): array
    {
        $dates = [];
        $amounts = [];

        foreach ($chartData as $item) {
            // Use smart date formatting to avoid confusion with multi-year data
            $date = $this->formatDateForChart($item['period'], $chartData);
            $dates[] = $date;

            // Convert amount from cents to dollars
            $amounts[] = (int) ($item['total_sales'] / 100);
        }

        return [
            'xAxis' => [
                'data' => $dates
            ],
            'series' => [
                [
                    'data' => $amounts
                ]
            ]
        ];
    }

    /**
     * Transform data for dual-axis chart (sales amount + quantity)
     */
    private function transformDualAxisChart(array $chartData): array
    {
        $dates = [];
        $amounts = [];
        $quantities = [];

        foreach ($chartData as $item) {
            // Use smart date formatting to avoid confusion with multi-year data
            $date = $this->formatDateForChart($item['period'], $chartData);
            $dates[] = $date;

            // Convert amount from cents to dollars
            $amounts[] = (int) ($item['total_sales'] / 100);
            $quantities[] = (int) $item['order_count']; // Use order count as quantity proxy
        }

        return [
            'xAxis' => [
                'data' => $dates
            ],
            'series' => [
                [
                    'name' => __('api.reports.chart_labels.order_amount'),
                    'yAxisIndex' => 0,
                    'data' => $amounts
                ],
                [
                    'name' => __('api.reports.chart_labels.order_quantity'),
                    'yAxisIndex' => 1,
                    'data' => $quantities
                ]
            ]
        ];
    }

    /**
     * Transform regional sales amount data for pie chart
     */
    private function transformRegionalSalesAmountChart(array $regionData): array
    {
        $aggregatedData = [];

        // Aggregate data by region name to avoid duplicates
        foreach ($regionData as $item) {
            $regionName = $this->getRegionDisplayName($item['country']);
            $salesValue = (int) ($item['total_sales'] / 100); // Convert from cents to dollars

            if (isset($aggregatedData[$regionName])) {
                $aggregatedData[$regionName] += $salesValue;
            } else {
                $aggregatedData[$regionName] = $salesValue;
            }
        }

        // Convert aggregated data to chart format
        $data = [];
        foreach ($aggregatedData as $regionName => $totalValue) {
            $data[] = [
                'name' => $regionName,
                'value' => $totalValue
            ];
        }

        return [
            'series' => [
                [
                    'data' => $data
                ]
            ]
        ];
    }

    /**
     * Get display name for region/country using translation
     */
    private function getRegionDisplayName(string $country): string
    {
        $translationKey = "api.reports.regions.{$country}";
        $translated = __($translationKey);

        // If translation key not found, return the fallback 'other'
        if ($translated === $translationKey) {
            return __('api.reports.regions.other');
        }

        return $translated;
    }

}
