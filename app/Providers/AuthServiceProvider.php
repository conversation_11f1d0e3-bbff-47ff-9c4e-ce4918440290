<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\EmailTemplate;
use App\Models\FinancialStatement;
use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\Role;
use App\Models\SyncLog;
use App\Models\User;
use App\Policies\ActivityLogPolicy;
use App\Policies\EmailTemplatePolicy;
use App\Policies\FinancialStatementPolicy;
use App\Policies\InvitationPolicy;
use App\Policies\OrganisationPolicy;
use App\Policies\ProductPolicy;
use App\Policies\ProductPermissionPolicy;
use App\Policies\ReportPolicy;
use App\Policies\RolePolicy;
use App\Policies\SyncPolicy;
use App\Policies\UserPolicy;
use App\Policies\UserRolePolicy;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

final class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        User::class => UserPolicy::class,
        Role::class => RolePolicy::class,
        Organisation::class => OrganisationPolicy::class,
        Invitation::class => InvitationPolicy::class,
        SyncLog::class => SyncPolicy::class,
        Product::class => ProductPolicy::class,
        ProductPermission::class => ProductPermissionPolicy::class,
        EmailTemplate::class => EmailTemplatePolicy::class,
        Activity::class => ActivityLogPolicy::class,
        FinancialStatement::class => FinancialStatementPolicy::class,
        'user_role' => UserRolePolicy::class,
        'report' => ReportPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
    }
}
