<?php

declare(strict_types=1);

namespace App\Rules;

use App\Models\FinancialStatement;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

final class UniqueFinancialStatementOrdersByReportType implements ValidationRule
{
    public function __construct(
        private readonly string $reportType,
        private readonly ?int $excludeFinancialStatementId = null
    ) {
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // $value should be an array of order IDs
        if (!is_array($value) || empty($value)) {
            return;
        }

        // Check if any of these orders are already associated with other financial statements of the same report type
        $conflictingStatements = FinancialStatement::where('report_type', $this->reportType)
            ->when($this->excludeFinancialStatementId, function ($query) {
                $query->where('id', '!=', $this->excludeFinancialStatementId);
            })
            ->whereHas('financialStatementOrders', function ($query) use ($value) {
                $query->whereIn('order_id', $value);
            })
            ->with(['organisation'])
            ->get();

        if ($conflictingStatements->isNotEmpty()) {
            $conflictingNames = $conflictingStatements->map(function ($statement) {
                return $statement->report_title . ' (' . $statement->organisation->name . ')';
            })->join(', ');

            $fail("所选订单已经被以下相同报表类型的财务报表使用: {$conflictingNames}");
        }
    }
}
