<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\App;
use Spatie\Activitylog\Models\Activity;

/**
 * Activity Log Formatter Service
 * 
 * Formats activity log descriptions into human-readable messages
 * supporting both English and Chinese languages.
 */
final class ActivityLogFormatterService
{
    /**
     * Format activity log description based on current locale.
     */
    public function formatDescription(Activity $activity): string
    {
        $locale = App::getLocale();
        
        // Get model name from subject_type
        $modelName = $this->getModelName($activity->subject_type);
        
        // Get event description
        $event = $activity->event;
        
        // Try to get specific description for this model and event
        $specificKey = "activity.descriptions.{$modelName}.{$event}";
        if (trans()->has($specificKey)) {
            $description = __($specificKey);
        } else {
            // Fall back to generic description
            $description = __('activity.events.default', [
                'event' => __("activity.events.{$event}"),
                'model' => __("activity.models.{$modelName}")
            ]);
        }
        
        // Add causer information if available
        if ($activity->causer) {
            $causerInfo = $this->formatCauserInfo($activity->causer);
            if ($locale === 'zh') {
                $description = "用户 {$causerInfo} {$description}";
            } else {
                $description = "User {$causerInfo} {$description}";
            }
        }
        
        // Add subject information if available and relevant
        try {
            $subjectInfo = $this->formatSubjectInfo($activity);
            if ($subjectInfo) {
                if ($locale === 'zh') {
                    $description .= " ({$subjectInfo})";
                } else {
                    $description .= " ({$subjectInfo})";
                }
            }
        } catch (\Exception $e) {
            // If subject cannot be loaded (e.g., unknown model), skip subject info
        }
        
        return $description;
    }
    
    /**
     * Get model name from subject_type class name.
     */
    private function getModelName(string $subjectType): string
    {
        $className = class_basename($subjectType);
        
        return match ($className) {
            'Invitation' => 'invitation',
            'Organisation' => 'organisation',
            'ProductPermission' => 'product_permission',
            'Role' => 'role',
            'SyncLog' => 'sync_log',
            'User' => 'user',
            'Product' => 'product',
            'Order' => 'order',
            'SyncRecord' => 'sync_record',
            default => strtolower($className),
        };
    }
    
    /**
     * Format causer (user) information for display.
     */
    private function formatCauserInfo($causer): string
    {
        if (!$causer) {
            return '';
        }
        
        // Prefer name, fall back to email
        if (!empty($causer->name)) {
            return $causer->name;
        }
        
        if (!empty($causer->email)) {
            return $causer->email;
        }
        
        return "ID: {$causer->id}";
    }
    
    /**
     * Format subject information for display.
     */
    private function formatSubjectInfo(Activity $activity): string
    {
        // Check if subject_type is a valid class before trying to access subject
        if (!class_exists($activity->subject_type)) {
            return '';
        }

        if (!$activity->subject) {
            return '';
        }

        $subject = $activity->subject;
        $className = class_basename($subject);

        return match ($className) {
            'User' => $this->formatUserInfo($subject),
            'Organisation' => $this->formatOrganisationInfo($subject),
            'Invitation' => $this->formatInvitationInfo($subject),
            'Role' => $this->formatRoleInfo($subject),
            'ProductPermission' => $this->formatProductPermissionInfo($subject),
            'SyncLog' => $this->formatSyncLogInfo($subject),
            default => "ID: {$subject->getKey()}",
        };
    }
    
    /**
     * Format user information.
     */
    private function formatUserInfo($user): string
    {
        if (!empty($user->name)) {
            return $user->name;
        }
        
        if (!empty($user->email)) {
            return $user->email;
        }
        
        return "ID: {$user->id}";
    }
    
    /**
     * Format organisation information.
     */
    private function formatOrganisationInfo($organisation): string
    {
        if (!empty($organisation->name)) {
            return $organisation->name;
        }
        
        if (!empty($organisation->code)) {
            return $organisation->code;
        }
        
        return "ID: {$organisation->id}";
    }
    
    /**
     * Format invitation information.
     */
    private function formatInvitationInfo($invitation): string
    {
        $info = [];

        if (!empty($invitation->role)) {
            $info[] = __('activity.fields.role') . ": {$invitation->role}";
        }

        if (!empty($invitation->email_restriction)) {
            $info[] = __('activity.fields.email') . ": {$invitation->email_restriction}";
        }

        return !empty($info) ? implode(', ', $info) : "ID: {$invitation->id}";
    }
    
    /**
     * Format role information.
     */
    private function formatRoleInfo($role): string
    {
        if (!empty($role->name)) {
            return $role->name;
        }
        
        return "ID: {$role->id}";
    }
    
    /**
     * Format product permission information.
     */
    private function formatProductPermissionInfo($permission): string
    {
        $info = [];

        if (!empty($permission->permission_type)) {
            $info[] = __('activity.fields.permission_type') . ": {$permission->permission_type}";
        }

        if (!empty($permission->user_id)) {
            $info[] = __('activity.fields.user_id') . ": {$permission->user_id}";
        }

        if (!empty($permission->product_id)) {
            $info[] = __('activity.fields.product_id') . ": {$permission->product_id}";
        }

        return !empty($info) ? implode(', ', $info) : "ID: {$permission->id}";
    }
    
    /**
     * Format sync log information.
     */
    private function formatSyncLogInfo($syncLog): string
    {
        $info = [];

        if (!empty($syncLog->sync_type)) {
            $info[] = __('activity.fields.sync_type') . ": {$syncLog->sync_type}";
        }

        if (!empty($syncLog->batch_id)) {
            $info[] = __('activity.fields.batch_id') . ": {$syncLog->batch_id}";
        }

        if (!empty($syncLog->status)) {
            $info[] = __('activity.fields.status') . ": {$syncLog->status}";
        }

        return !empty($info) ? implode(', ', $info) : "ID: {$syncLog->id}";
    }
}
