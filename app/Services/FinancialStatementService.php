<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\FinancialStatement;
use App\Models\OrderItem;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

final readonly class FinancialStatementService
{
    /**
     * Get financial statements with filtering and pagination.
     */
    public function getFinancialStatements(
        User $user,
        int $perPage = 15,
        ?string $status = null,
        ?string $reportType = null,
        ?int $organisationId = null,
        ?string $startDate = null,
        ?string $endDate = null
    ): LengthAwarePaginator {
        $query = FinancialStatement::with(['organisation']);

        // Apply user-based filtering
        if (!$user->hasSystemAdminAccess()) {
            // Non-system admins can only see financial statements from their organisations
            $userOrganisationIds = $user->getOrganisationIds();
            $query->whereIn('organisation_id', $userOrganisationIds->toArray());
        }

        // Apply filters
        if ($status) {
            $query->where('status', $status);
        }

        if ($reportType) {
            $query->where('report_type', $reportType);
        }

        if ($organisationId) {
            $query->where('organisation_id', $organisationId);
        }

        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get financial statements by organisation.
     */
    public function getByOrganisation(
        int $organisationId,
        int $perPage = 15,
        ?string $status = null,
        ?string $reportType = null
    ): LengthAwarePaginator {
        $query = FinancialStatement::with(['organisation'])
            ->where('organisation_id', $organisationId);

        if ($status) {
            $query->where('status', $status);
        }

        if ($reportType) {
            $query->where('report_type', $reportType);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get financial statement by ID.
     */
    public function getById(int $id): ?FinancialStatement
    {
        return FinancialStatement::with(['organisation', 'orders'])->find($id);
    }

    /**
     * Create a new financial statement.
     */
    public function create(array $data, User $user): FinancialStatement
    {
        try {
            DB::beginTransaction();

            // Set default status if not provided
            $data['status'] = $data['status'] ?? FinancialStatement::STATUS_PENDING_AUDIT;

            // Extract order IDs if provided
            $orderIds = $data['order_ids'] ?? [];
            unset($data['order_ids']);

            // Load organisation to generate code and title before creating
            $organisation = \App\Models\Organisation::find($data['organisation_id']);
            if (!$organisation) {
                throw new \InvalidArgumentException('Organisation not found');
            }

            // Create a temporary financial statement instance to generate code and title
            $tempStatement = new FinancialStatement($data);
            $tempStatement->organisation = $organisation;

            // Generate and set report code and title
            $data['report_code'] = $tempStatement->generateReportCode();
            $data['report_title'] = $tempStatement->generateReportTitle();

            // Create the financial statement with all required fields
            $financialStatement = FinancialStatement::create($data);

            // Attach orders if provided
            if (!empty($orderIds)) {
                $this->attachOrders($financialStatement, $orderIds);
            }

            DB::commit();

            return $financialStatement->fresh(['organisation', 'orders']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update a financial statement.
     */
    public function update(FinancialStatement $financialStatement, array $data, User $user): FinancialStatement
    {
        try {
            DB::beginTransaction();

            // Extract order IDs if provided
            $orderIds = $data['order_ids'] ?? null;
            unset($data['order_ids']);

            // Update the financial statement
            $financialStatement->update($data);

            // Update orders if provided
            if ($orderIds !== null) {
                $this->syncOrders($financialStatement, $orderIds);
            }

            // Regenerate code and title if relevant fields changed
            if (isset($data['report_type']) || isset($data['start_date']) || isset($data['end_date'])) {
                $financialStatement->load('organisation');
                $financialStatement->update([
                    'report_code' => $financialStatement->generateReportCode(),
                    'report_title' => $financialStatement->generateReportTitle(),
                ]);
            }

            DB::commit();

            return $financialStatement->fresh(['organisation', 'orders']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Publish a financial statement.
     */
    public function publish(FinancialStatement $financialStatement): FinancialStatement
    {
        $financialStatement->update([
            'status' => FinancialStatement::STATUS_PUBLISHED,
        ]);

        return $financialStatement->fresh(['organisation']);
    }

    /**
     * Get financial statement statistics.
     */
    public function getStatistics(User $user, ?int $organisationId = null): array
    {
        $query = FinancialStatement::query();

        // Apply user-based filtering
        if (!$user->hasSystemAdminAccess()) {
            $userOrganisationIds = $user->getOrganisationIds();
            $query->whereIn('organisation_id', $userOrganisationIds->toArray());
        }

        // Apply organisation filter if specified
        if ($organisationId) {
            $query->where('organisation_id', $organisationId);
        }

        return [
            'total_statements' => $query->count(),
            'pending_audit' => (clone $query)->where('status', FinancialStatement::STATUS_PENDING_AUDIT)->count(),
            'published' => (clone $query)->where('status', FinancialStatement::STATUS_PUBLISHED)->count(),
            'by_type' => [
                'monthly' => (clone $query)->where('report_type', FinancialStatement::TYPE_MONTHLY)->count(),
                'quarterly' => (clone $query)->where('report_type', FinancialStatement::TYPE_QUARTERLY)->count(),
                'yearly' => (clone $query)->where('report_type', FinancialStatement::TYPE_YEARLY)->count(),
                'custom' => (clone $query)->where('report_type', FinancialStatement::TYPE_CUSTOM)->count(),
            ],
            'total_amount' => $query->sum('total_amount'),
            'total_orders' => $query->sum('total_orders'),
            'total_quantity' => $query->sum('total_quantity'),
        ];
    }

    /**
     * Get product sales data grouped by product and region for a financial statement.
     */
    public function getProductSalesData(FinancialStatement $financialStatement): array
    {
        // Get all orders associated with this financial statement
        $orderIds = $financialStatement->orders()->pluck('orders.id');

        if ($orderIds->isEmpty()) {
            return [];
        }

        // Query order items with product and order information
        $salesData = OrderItem::query()
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('products', 'order_items.store_variant_id', '=', 'products.store_variant_id')
            ->whereIn('order_items.order_id', $orderIds)
            ->where('orders.state', 'completed')
            ->whereNotNull('orders.shipping_country')
            ->selectRaw('
                products.name as product_name,
                products.slug as product_slug,
                products.package as product_package,
                orders.shipping_country,
                SUM(order_items.quantity - COALESCE(order_items.quantity_refunded, 0)) as total_quantity,
                SUM(order_items.total - COALESCE(
                    CASE
                        WHEN order_items.quantity > 0
                        THEN ROUND((order_items.total * order_items.quantity_refunded) / order_items.quantity)
                        ELSE 0
                    END, 0
                )) as total_sales,
                COUNT(DISTINCT orders.id) as order_count
            ')
            ->groupBy('products.name', 'products.slug', 'products.package', 'orders.shipping_country')
            ->orderBy('products.name')
            ->orderBy('orders.shipping_country')
            ->get();

        // Group data by product, then by region
        $groupedData = [];
        foreach ($salesData as $item) {
            $productKey = $item->product_name;

            if (!isset($groupedData[$productKey])) {
                $groupedData[$productKey] = [
                    'product_name' => $item->product_name,
                    'product_slug' => $item->product_slug,
                    'product_package' => $item->product_package,
                    'regions' => [],
                    'total_quantity' => 0,
                    'total_sales' => 0,
                    'total_orders' => 0,
                ];
            }

            $regionData = [
                'country' => $item->shipping_country,
                'total_quantity' => (int) $item->total_quantity,
                'total_sales' => (int) $item->total_sales,
                'total_sales_formatted' => number_format($item->total_sales / 100, 2),
                'order_count' => (int) $item->order_count,
            ];

            $groupedData[$productKey]['regions'][] = $regionData;
            $groupedData[$productKey]['total_quantity'] += $regionData['total_quantity'];
            $groupedData[$productKey]['total_sales'] += $regionData['total_sales'];
            $groupedData[$productKey]['total_orders'] += $regionData['order_count'];
        }

        // Format final data and add formatted totals
        return array_values(array_map(function ($product) {
            $product['total_sales_formatted'] = number_format($product['total_sales'] / 100, 2);

            // Sort regions by total sales descending
            usort($product['regions'], function ($a, $b) {
                return $b['total_sales'] <=> $a['total_sales'];
            });

            return $product;
        }, $groupedData));
    }

    /**
     * Attach orders to a financial statement.
     */
    private function attachOrders(FinancialStatement $financialStatement, array $orderIds): void
    {
        // Validate order uniqueness by report type
        if (!$this->validateOrderUniqueness($financialStatement, $orderIds)) {
            throw ValidationException::withMessages([
                'order_ids' => [__('validation.financial_statements.orders_already_used')],
            ]);
        }

        $financialStatement->orders()->attach($orderIds);
    }

    /**
     * Sync orders with a financial statement.
     */
    private function syncOrders(FinancialStatement $financialStatement, array $orderIds): void
    {
        // Validate order uniqueness by report type (excluding current statement)
        if (!$this->validateOrderUniqueness($financialStatement, $orderIds, $financialStatement->id)) {
            throw ValidationException::withMessages([
                'order_ids' => [__('validation.financial_statements.orders_already_used')],
            ]);
        }

        $financialStatement->orders()->sync($orderIds);
    }

    /**
     * Validate that orders are not already used in other financial statements of the same report type.
     */
    private function validateOrderUniqueness(FinancialStatement $financialStatement, array $orderIds, ?int $excludeStatementId = null): bool
    {
        if (empty($orderIds)) {
            return true;
        }

        $query = FinancialStatement::where('report_type', $financialStatement->report_type)
            ->whereHas('orders', function (Builder $query) use ($orderIds) {
                $query->whereIn('orders.id', $orderIds);
            });

        if ($excludeStatementId) {
            $query->where('id', '!=', $excludeStatementId);
        }

        return !$query->exists();
    }
}
