<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Collection;
use Carbon\Carbon;

final class EmailLogService
{
    private const LOG_PATH = 'logs/laravel/email.log';
    private const DATE_FORMAT = 'Y-m-d';
    private const DATETIME_FORMAT = 'Y-m-d H:i:s';

    /**
     * Get email logs for a specific date range.
     *
     * @param string|null $startDate Start date (Y-m-d format)
     * @param string|null $endDate End date (Y-m-d format)
     * @param string|null $status Filter by status (sent, failed, sending)
     * @param int $limit Maximum number of records to return
     * @return Collection<int, array>
     */
    public function getLogs(
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $status = null,
        int $limit = 100
    ): Collection {
        $logs = collect();
        
        // Default to today if no dates provided
        $startDate = $startDate ?: now()->format(self::DATE_FORMAT);
        $endDate = $endDate ?: now()->format(self::DATE_FORMAT);

        // Get log files for the date range
        $logFiles = $this->getLogFilesForDateRange($startDate, $endDate);

        foreach ($logFiles as $logFile) {
            if (File::exists($logFile)) {
                $fileLogs = $this->parseLogFile($logFile, $status);
                $logs = $logs->merge($fileLogs);
            }
        }

        // Sort by timestamp descending and limit
        return $logs->sortByDesc('timestamp')->take($limit)->values();
    }

    /**
     * Get email sending statistics.
     *
     * @param string|null $startDate Start date (Y-m-d format)
     * @param string|null $endDate End date (Y-m-d format)
     * @return array<string, mixed>
     */
    public function getStats(?string $startDate = null, ?string $endDate = null): array
    {
        $logs = $this->getLogs($startDate, $endDate, null, 10000); // Get more logs for stats

        $stats = [
            'total_emails' => $logs->count(),
            'sent' => $logs->where('status', 'sent')->count(),
            'failed' => $logs->where('status', 'failed')->count(),
            'failed_permanently' => $logs->where('status', 'failed_permanently')->count(),
            'sending' => $logs->where('status', 'sending')->count(),
            'success_rate' => 0,
            'average_processing_time_ms' => 0,
            'templates_used' => [],
            'hourly_distribution' => [],
        ];

        if ($stats['total_emails'] > 0) {
            $stats['success_rate'] = round(($stats['sent'] / $stats['total_emails']) * 100, 2);
        }

        // Calculate average processing time
        $processingTimes = $logs->whereNotNull('processing_time_ms')->pluck('processing_time_ms');
        if ($processingTimes->count() > 0) {
            $stats['average_processing_time_ms'] = round($processingTimes->average(), 2);
        }

        // Template usage statistics
        $templateStats = $logs->whereNotNull('template_code')
            ->groupBy('template_code')
            ->map(fn($group) => $group->count())
            ->sortDesc();
        $stats['templates_used'] = $templateStats->toArray();

        // Hourly distribution
        $hourlyStats = $logs->groupBy(function ($log) {
            return Carbon::parse($log['timestamp'])->format('H:00');
        })->map(fn($group) => $group->count())->sortKeys();
        $stats['hourly_distribution'] = $hourlyStats->toArray();

        return $stats;
    }

    /**
     * Get log files for a date range.
     *
     * @param string $startDate
     * @param string $endDate
     * @return array<string>
     */
    private function getLogFilesForDateRange(string $startDate, string $endDate): array
    {
        $files = [];
        $current = Carbon::createFromFormat(self::DATE_FORMAT, $startDate);
        $end = Carbon::createFromFormat(self::DATE_FORMAT, $endDate);

        while ($current->lte($end)) {
            $logFile = storage_path(self::LOG_PATH . '-' . $current->format(self::DATE_FORMAT));
            $files[] = $logFile;
            $current->addDay();
        }

        // Also check the main log file (today's log)
        $files[] = storage_path(self::LOG_PATH);

        return array_unique($files);
    }

    /**
     * Parse a log file and extract email log entries.
     *
     * @param string $logFile
     * @param string|null $statusFilter
     * @return Collection<int, array>
     */
    private function parseLogFile(string $logFile, ?string $statusFilter = null): Collection
    {
        $logs = collect();
        
        if (!File::exists($logFile)) {
            return $logs;
        }

        $content = File::get($logFile);
        $lines = explode("\n", $content);

        foreach ($lines as $line) {
            if (empty(trim($line))) {
                continue;
            }

            $logEntry = $this->parseLogLine($line);
            if ($logEntry && (!$statusFilter || $logEntry['status'] === $statusFilter)) {
                $logs->push($logEntry);
            }
        }

        return $logs;
    }

    /**
     * Parse a single log line.
     *
     * @param string $line
     * @return array|null
     */
    private function parseLogLine(string $line): ?array
    {
        // Look for JSON content in the log line
        if (preg_match('/\{.*\}/', $line, $matches)) {
            $jsonData = json_decode($matches[0], true);
            
            if (json_last_error() === JSON_ERROR_NONE && isset($jsonData['timestamp'])) {
                return [
                    'timestamp' => $jsonData['timestamp'] ?? null,
                    'template_code' => $jsonData['template_code'] ?? null,
                    'to_email' => $jsonData['to_email'] ?? null,
                    'to_name' => $jsonData['to_name'] ?? null,
                    'subject' => $jsonData['subject'] ?? null,
                    'status' => $jsonData['status'] ?? null,
                    'variables' => $jsonData['variables'] ?? [],
                    'error_message' => $jsonData['error_message'] ?? null,
                    'processing_time_ms' => $jsonData['processing_time_ms'] ?? null,
                    'queue_delay_ms' => $jsonData['queue_delay_ms'] ?? null,
                    'attempt' => $jsonData['attempt'] ?? null,
                    'max_tries' => $jsonData['max_tries'] ?? null,
                ];
            }
        }

        return null;
    }
}
