<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Report Service
 *
 * Main service class for handling report generation and data processing.
 * Implements caching strategies for improved performance.
 */
final class ReportService
{
    public function __construct(
        private readonly SalesReportService $salesReportService,
        private readonly RefundReportService $refundReportService,
        private readonly OrderStatusReportService $orderStatusReportService
    ) {}

    /**
     * Get sales report data
     */
    public function getSalesReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('sales', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->salesReportService->generateReport($query, $filters);
        });
    }

    /**
     * Get volume report data
     */
    public function getVolumeReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('volume', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->salesReportService->generateVolumeReport($query, $filters);
        });
    }

    /**
     * Get refund report data
     */
    public function getRefundReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('refunds', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->refundReportService->generateReport($query, $filters);
        });
    }

    /**
     * Get order status report data
     */
    public function getOrderStatusReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('order_status', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->orderStatusReportService->generateReport($query, $filters);
        });
    }

    /**
     * Get product ranking report data
     */
    public function getProductRankingReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('product_ranking', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            return $this->generateProductRankingReport($filters);
        });
    }

    /**
     * Export report data
     */
    public function exportReport(array $config): array
    {
        // This will be implemented in later phases
        return [
            'message' => 'Export functionality will be implemented in phase 5',
            'config' => $config,
        ];
    }

    /**
     * Build base query with filters
     */
    private function buildBaseQuery(array $filters): \Illuminate\Database\Eloquent\Builder
    {
        $query = Order::query()
            ->with(['orderItems'])
            ->whereBetween('completed_at', [$filters['start_date'], $filters['end_date']]);

        // Apply filters
        if (!empty($filters['countries'])) {
            $query->whereIn('shipping_country', $filters['countries']);
        }

        if (!empty($filters['states'])) {
            $query->whereIn('state', $filters['states']);
        }

        if (!empty($filters['payment_states'])) {
            $query->whereIn('payment_state', $filters['payment_states']);
        }

        // Apply product-based filtering for organisation
        if (isset($filters['product_ids'])) {
            if (empty($filters['product_ids'])) {
                // If organisation has no products, return no results
                $query->whereRaw('1 = 0');
            } else {
                // Convert product primary key IDs to store_variant_ids for order_items filtering
                $storeVariantIds = \DB::table('products')
                    ->whereIn('id', $filters['product_ids'])
                    ->pluck('store_variant_id')
                    ->toArray();

                if (empty($storeVariantIds)) {
                    $query->whereRaw('1 = 0');
                } else {
                    // Use whereIn with subquery for better performance on large datasets
                    // Specify table name to avoid ambiguity when joins are used later
                    $query->whereIn('orders.id', function ($subquery) use ($storeVariantIds) {
                        $subquery->select('order_id')
                            ->from('order_items')
                            ->whereIn('store_variant_id', $storeVariantIds)
                            ->distinct();
                    });
                }
            }
        }

        if (isset($filters['include_refunds']) && !$filters['include_refunds']) {
            $query->where(function ($q) {
                $q->whereNull('refund_status')
                  ->orWhere('refund_status', '!=', 'success');
            });
        }

        if (!empty($filters['refund_status'])) {
            $query->where('refund_status', $filters['refund_status']);
        }

        return $query;
    }

    /**
     * Generate product ranking report data
     */
    private function generateProductRankingReport(array $filters): array
    {
        $limit = $filters['limit'] ?? 20;

        // Build base query for order items with product information
        $baseQuery = OrderItem::query()
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('products', 'order_items.store_variant_id', '=', 'products.store_variant_id')
            ->whereBetween('orders.completed_at', [$filters['start_date'], $filters['end_date']])
            ->where('orders.state', 'completed');

        // Apply filters to base query
        if (!empty($filters['countries'])) {
            $baseQuery->whereIn('orders.shipping_country', $filters['countries']);
        }

        if (!empty($filters['states'])) {
            $baseQuery->whereIn('orders.state', $filters['states']);
        }

        if (!empty($filters['payment_states'])) {
            $baseQuery->whereIn('orders.payment_state', $filters['payment_states']);
        }

        if (!empty($filters['currency'])) {
            $baseQuery->where('orders.currency_code', $filters['currency']);
        }

        // Apply product filtering based on organization
        if (!empty($filters['product_ids'])) {
            // Convert product primary key IDs to store_variant_ids for filtering
            $storeVariantIds = \DB::table('products')
                ->whereIn('id', $filters['product_ids'])
                ->pluck('store_variant_id')
                ->toArray();

            if (!empty($storeVariantIds)) {
                $baseQuery->whereIn('products.store_variant_id', $storeVariantIds);
            } else {
                // If no valid store_variant_ids found, return no results
                $baseQuery->whereRaw('1 = 0');
            }
        }

        // Handle refunds
        if (isset($filters['include_refunds']) && !$filters['include_refunds']) {
            $baseQuery->where('order_items.quantity_refunded', 0);
        }

        // Get aggregated data for both rankings
        $aggregatedData = $baseQuery
            ->select([
                'order_items.store_variant_id',
                'products.name as product_name',
                'products.slug as product_slug',
                'products.package as product_package',
                \DB::raw('SUM(order_items.quantity - order_items.quantity_refunded) as total_quantity'),
                \DB::raw('SUM(CASE WHEN order_items.quantity > 0 THEN order_items.total - (order_items.total * order_items.quantity_refunded / order_items.quantity) ELSE 0 END) as total_sales')
            ])
            ->groupBy([
                'order_items.store_variant_id',
                'products.name',
                'products.slug',
                'products.package'
            ])
            ->get();

        // Generate sales-based rankings (ordered by total_sales)
        $salesRankings = $aggregatedData
            ->sortByDesc('total_sales')
            ->take($limit)
            ->values()
            ->map(function ($result, $index) {
                return [
                    'rank' => $index + 1,
                    'store_variant_id' => $result->store_variant_id,
                    'product_name' => $result->product_name,
                    'product_slug' => $result->product_slug,
                    'product_package' => $result->product_package,
                    'total_quantity' => (int) $result->total_quantity,
                    'total_sales' => (int) $result->total_sales,
                    'total_sales_formatted' => number_format($result->total_sales / 100, 2),
                ];
            })
            ->toArray();

        // Generate quantity-based rankings (ordered by total_quantity)
        $quantityRankings = $aggregatedData
            ->sortByDesc('total_quantity')
            ->take($limit)
            ->values()
            ->map(function ($result, $index) {
                return [
                    'rank' => $index + 1,
                    'store_variant_id' => $result->store_variant_id,
                    'product_name' => $result->product_name,
                    'product_slug' => $result->product_slug,
                    'product_package' => $result->product_package,
                    'total_quantity' => (int) $result->total_quantity,
                    'total_sales' => (int) $result->total_sales,
                    'total_sales_formatted' => number_format($result->total_sales / 100, 2),
                ];
            })
            ->toArray();

        return [
            'sales_rankings' => $salesRankings,
            'quantity_rankings' => $quantityRankings,
            'total_products' => $aggregatedData->count(),
            'limit' => $limit,
            'period' => [
                'start_date' => $filters['start_date'],
                'end_date' => $filters['end_date'],
            ],
        ];
    }



    /**
     * Generate cache key for report data
     */
    private function generateCacheKey(string $type, array $filters): string
    {
        $user = auth()->user();
        $userContext = $user ? $user->id : 'guest';

        return sprintf(
            'reports:%s:%s:%s',
            $type,
            $userContext,
            md5(serialize($filters))
        );
    }
}
