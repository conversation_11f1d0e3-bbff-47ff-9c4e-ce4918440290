<?php

declare(strict_types=1);

namespace App\Services;

use App\Jobs\SendEmailJob;
use App\Models\EmailTemplate;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

final class EmailService
{
    /**
     * Send email using a template.
     *
     * @param string $templateCode Template code to use
     * @param string $toEmail Recipient email address
     * @param array<string, mixed> $variables Variables for template rendering
     * @param string|null $toName Recipient name (optional)
     * @return bool True if email was queued successfully
     * @throws InvalidArgumentException If template not found or variables invalid
     */
    public function sendTemplateEmail(
        string $templateCode,
        string $toEmail,
        array $variables = [],
        ?string $toName = null
    ): bool {
        try {
            // Find and validate template
            $template = EmailTemplate::active()->byCode($templateCode)->first();
            
            if (!$template) {
                throw new InvalidArgumentException("Email template with code '{$templateCode}' not found or inactive");
            }

            if (!$template->isValid()) {
                throw new InvalidArgumentException("Email template '{$templateCode}' is not valid");
            }

            // Validate variables
            $this->validateVariables($template, $variables);

            // Render template
            $rendered = $this->renderTemplate($template, $variables);

            // Dispatch email job
            SendEmailJob::dispatch(
                $toEmail,
                $rendered['subject'],
                $rendered['content'],
                $toName,
                $templateCode,
                $variables
            )->onQueue(config('mail.queue.name', 'emails'));

            Log::info('Template email queued for sending', [
                'template_code' => $templateCode,
                'to_email' => $toEmail,
                'to_name' => $toName,
                'variables_count' => count($variables),
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to queue template email', [
                'template_code' => $templateCode,
                'to_email' => $toEmail,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Send custom email without template.
     *
     * @param string $toEmail Recipient email address
     * @param string $subject Email subject
     * @param string $content Email content (HTML)
     * @param string|null $toName Recipient name (optional)
     * @return bool True if email was queued successfully
     */
    public function sendCustomEmail(
        string $toEmail,
        string $subject,
        string $content,
        ?string $toName = null
    ): bool {
        try {
            // Basic validation
            if (empty($toEmail) || !filter_var($toEmail, FILTER_VALIDATE_EMAIL)) {
                throw new InvalidArgumentException('Invalid email address provided');
            }

            if (empty($subject)) {
                throw new InvalidArgumentException('Email subject cannot be empty');
            }

            if (empty($content)) {
                throw new InvalidArgumentException('Email content cannot be empty');
            }

            // Dispatch email job
            SendEmailJob::dispatch(
                $toEmail,
                $subject,
                $content,
                $toName
            )->onQueue(config('mail.queue.name', 'emails'));

            Log::info('Custom email queued for sending', [
                'to_email' => $toEmail,
                'to_name' => $toName,
                'subject' => $subject,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to queue custom email', [
                'to_email' => $toEmail,
                'subject' => $subject,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Render template with variables.
     *
     * @param EmailTemplate $template
     * @param array<string, mixed> $variables
     * @return array{subject: string, content: string}
     */
    private function renderTemplate(EmailTemplate $template, array $variables): array
    {
        return $template->render($variables);
    }

    /**
     * Validate that all required variables are provided.
     *
     * @param EmailTemplate $template
     * @param array<string, mixed> $variables
     * @throws InvalidArgumentException
     */
    private function validateVariables(EmailTemplate $template, array $variables): void
    {
        $template->validateVariables($variables);
    }
}
