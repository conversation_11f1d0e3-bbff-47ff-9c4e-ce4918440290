<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use Illuminate\Validation\Validator;

/**
 * Report Access Validator Service
 *
 * Handles access permission validation for report filtering requests.
 * This service is responsible for validating user access to organizations
 * and products in the context of report generation.
 *
 * Separated from ReportFilterRequest to follow Single Responsibility Principle:
 * - ReportFilterRequest handles input validation and data processing
 * - ReportAccessValidator handles business logic for access permissions
 */
final readonly class ReportAccessValidator
{
    public function __construct(
        private OrganisationService $organisationService
    ) {}

    /**
     * Check if user has access to the specified organisation
     */
    public function hasOrganisationAccess(?User $user, ?int $organisationId): bool
    {
        // System admins can access any organisation
        if ($user && $user->hasSystemAdminAccess()) {
            return true;
        }

        if (empty($organisationId) || !$user) {
            return false;
        }

        // Check if user belongs to the specified organisation
        return $user->belongsToOrganisation($organisationId);
    }

    /**
     * Validate organisation access permissions and add errors to validator
     */
    public function validateOrganisationAccess(Validator $validator, ?User $user, ?int $organisationId): void
    {
        if (!$this->hasOrganisationAccess($user, $organisationId)) {
            $validator->errors()->add(
                'organisation_id',
                __('errors.report_organisation_access_denied')
            );
        }
    }

    /**
     * Validate product access permissions and add errors to validator
     *
     * Note: The product_id parameter represents the primary key id, not store_variant_id.
     * This is consistent with the ReportFilterRequest validation rule: exists:products,id
     */
    public function validateProductAccess(Validator $validator, ?User $user, ?int $organisationId, ?int $productId): void
    {
        // Skip validation if no product_id provided
        if (empty($productId)) {
            return;
        }

        // Only validate product access if user has access to the organisation
        if (!$this->hasOrganisationAccess($user, $organisationId)) {
            // Don't add product error if organisation access already failed
            // The organisation validation will handle this
            return;
        }

        // Get user's accessible product IDs (primary key ids) for the organisation
        $allowedProductIds = $this->getUserAccessibleProductIds($user, $organisationId);

        // Check if the requested product_id (primary key id) is in the user's allowed list
        if (!in_array($productId, $allowedProductIds, true)) {
            $validator->errors()->add(
                'product_id',
                __('errors.report_product_access_denied')
            );
        }
    }

    /**
     * Get product IDs (primary key ids) that the user can access for reports in the specified organisation
     *
     * Note: This method returns primary key ids, not store_variant_ids, which is consistent
     * with the ReportFilterRequest validation and the User::getAccessibleProductIds() method.
     */
    public function getUserAccessibleProductIds(?User $user, ?int $organisationId): array
    {
        // Use the User model's optimized method directly to leverage its caching
        // This avoids duplicate permission checks and database queries
        return $user ? $user->getAccessibleProductIds($organisationId) : [];
    }

    /**
     * Validate both organisation and product access in a single call
     * 
     * This is a convenience method that combines both validations
     */
    public function validateReportAccess(Validator $validator, ?User $user, ?int $organisationId, ?int $productId = null): void
    {
        $this->validateOrganisationAccess($validator, $user, $organisationId);
        
        if ($productId !== null) {
            $this->validateProductAccess($validator, $user, $organisationId, $productId);
        }
    }
}
