<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

final class EmailVerificationService
{
    private const VERIFICATION_CODE_LENGTH = 6;
    private const CACHE_TTL = 900; // 15 minutes in seconds - verification code expiry time
    private const RETRY_INTERVAL = 180; // 3 minutes in seconds - minimum interval between resend attempts
    private const CACHE_PREFIX = 'email_verification:';
    private const RETRY_PREFIX = 'email_verification_retry:';

    public function __construct(
        private readonly EmailService $emailService
    ) {}

    /**
     * Check if a new verification code can be sent (respects retry interval).
     */
    public function canResendCode(string $email): bool
    {
        $retryKey = self::RETRY_PREFIX . $email;
        return !Cache::has($retryKey);
    }

    /**
     * Generate and send verification code to email address.
     * Will replace existing code if retry interval has passed.
     */
    public function sendVerificationCode(string $email): string
    {
        // Check if we can send a new code
        if (!$this->canResendCode($email)) {
            Log::warning('Verification code resend blocked due to retry interval', [
                'email' => $email,
            ]);
            throw new \Exception('Please wait before requesting a new verification code.');
        }

        $code = $this->generateVerificationCode();

        // Store code in cache with email as key (this will replace any existing code)
        $cacheKey = self::CACHE_PREFIX . $email;
        Cache::put($cacheKey, $code, self::CACHE_TTL);

        // Set retry interval lock
        $retryKey = self::RETRY_PREFIX . $email;
        Cache::put($retryKey, true, self::RETRY_INTERVAL);

        // Send actual email using template
        try {
            $this->emailService->sendTemplateEmail(
                'user_email_verification',
                $email,
                [
                    'user_name' => $email, // Use email as fallback for user name
                    'verification_code' => $code,
                    'expiry_time' => now()->addSeconds(self::CACHE_TTL)->format('Y-m-d H:i:s'),
                ]
            );

            Log::info('Verification code sent', [
                'email' => $email,
                'expires_at' => now()->addSeconds(self::CACHE_TTL)->toISOString(),
            ]);

        } catch (\Exception $e) {
            // If email sending fails, remove the cached code and retry lock
            Cache::forget($cacheKey);
            Cache::forget($retryKey);

            Log::error('Failed to send verification email', [
                'email' => $email,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to send verification email. Please try again later.');
        }

        return $code;
    }

    /**
     * Verify the provided code against cached value.
     */
    public function verifyCode(string $email, string $code): bool
    {
        $cacheKey = self::CACHE_PREFIX . $email;
        $cachedCode = Cache::get($cacheKey);

        if ($cachedCode === null) {
            Log::warning('Verification code not found or expired', ['email' => $email]);
            return false;
        }

        $isValid = $cachedCode === $code;

        if ($isValid) {
            // Remove both verification code and retry lock after successful verification
            Cache::forget($cacheKey);
            Cache::forget(self::RETRY_PREFIX . $email);
            Log::info('Verification code verified successfully', ['email' => $email]);
        } else {
            Log::warning('Invalid verification code provided', ['email' => $email]);
        }

        return $isValid;
    }

    /**
     * Check if verification code exists for email.
     */
    public function hasValidCode(string $email): bool
    {
        $cacheKey = self::CACHE_PREFIX . $email;
        return Cache::has($cacheKey);
    }

    /**
     * Generate a random 6-digit verification code.
     */
    private function generateVerificationCode(): string
    {
        return str_pad((string) random_int(0, 999999), self::VERIFICATION_CODE_LENGTH, '0', STR_PAD_LEFT);
    }


}
