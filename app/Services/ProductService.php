<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

final readonly class ProductService
{
    /**
     * Get products with filtering and pagination.
     * Authorization is handled by the Policy layer.
     */
    public function getProducts(array $filters = []): LengthAwarePaginator
    {
        $query = Product::query()->with('organisation');

        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function (Builder $q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%");
            });
        }

        // Apply enabled filter
        if (isset($filters['enabled'])) {
            $query->where('enabled', $filters['enabled']);
        }

        // Apply organization filter (authorization is handled by Policy/Request validation)
        if (!empty($filters['organisation_id'])) {
            $organisationId = (int) $filters['organisation_id'];
            $query->whereHas('organisation', function (Builder $q) use ($organisationId) {
                $q->where('id', $organisationId);
            });
        }

        // Order by name for consistent results
        $query->orderBy('name');

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get a single product by ID.
     */
    public function getProduct(int $id): ?Product
    {
        return Product::with('organisation')->find($id);
    }


}
