<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Organisation;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

final readonly class OrganisationService
{
    /**
     * Get all organisations with optional pagination.
     */
    public function getAll(int $perPage = 15): LengthAwarePaginator
    {
        return Organisation::orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get organisation by ID.
     */
    public function getById(int $id): ?Organisation
    {
        return Organisation::find($id);
    }

    /**
     * Get organisation by code.
     */
    public function getByCode(string $code): ?Organisation
    {
        return Organisation::where('code', $code)->first();
    }

    /**
     * Create a new organisation.
     */
    public function create(array $data): Organisation
    {
        return Organisation::create($data);
    }

    /**
     * Update an organisation.
     */
    public function update(Organisation $organisation, array $data): bool
    {
        return $organisation->update($data);
    }



    /**
     * Suspend an organisation.
     */
    public function suspend(Organisation $organisation): bool
    {
        return $organisation->suspend();
    }

    /**
     * Get organisations by status.
     */
    public function getByStatus(string $status, int $perPage = 15): LengthAwarePaginator
    {
        return Organisation::where('status', $status)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Check if organisation code is unique.
     */
    public function isCodeUnique(string $code, ?int $excludeId = null): bool
    {
        $query = Organisation::where('code', $code);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Get active organisations.
     */
    public function getActive(int $perPage = 15): LengthAwarePaginator
    {
        return $this->getByStatus('active', $perPage);
    }

    /**
     * Get pending organisations.
     */
    public function getPending(int $perPage = 15): LengthAwarePaginator
    {
        return $this->getByStatus('pending', $perPage);
    }

    /**
     * Get suspended organisations.
     */
    public function getSuspended(int $perPage = 15): LengthAwarePaginator
    {
        return $this->getByStatus('suspended', $perPage);
    }

    /**
     * Get organisations that the user owns (has owner role).
     */
    public function getUserOwnedOrganisations(User $user, int $perPage = 15, ?string $status = null): LengthAwarePaginator
    {
        // Get organisations where user has owner role
        $ownedOrganisationIds = $user->getOwnedOrganisationIds();

        if ($ownedOrganisationIds->isEmpty()) {
            // Return empty paginator if user owns no organisations
            return new LengthAwarePaginator([], 0, $perPage, 1);
        }

        $query = Organisation::whereIn('id', $ownedOrganisationIds->toArray());

        if ($status) {
            $query->where('status', $status);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Search organisations by name or code.
     */
    public function searchOrganisations(?string $search = null, int $perPage = 15, ?string $status = null, ?array $organizationIds = null): LengthAwarePaginator
    {
        $query = Organisation::query();

        // Filter by organization IDs if provided
        if ($organizationIds && !empty($organizationIds)) {
            $query->whereIn('id', $organizationIds);
        }

        // Apply search filter if provided
        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($status) {
            $query->where('status', $status);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Search organisations that the user owns (has owner role).
     */
    public function searchUserOwnedOrganisations(User $user, ?string $search = null, int $perPage = 15, ?string $status = null, ?array $organizationIds = null): LengthAwarePaginator
    {
        // Get organisations where user has owner role
        $ownedOrganisationIds = $user->getOwnedOrganisationIds();

        if ($ownedOrganisationIds->isEmpty()) {
            // Return empty paginator if user owns no organisations
            return new LengthAwarePaginator([], 0, $perPage, 1);
        }

        $query = Organisation::whereIn('id', $ownedOrganisationIds->toArray());

        // Further filter by specific organization IDs if provided
        if ($organizationIds && !empty($organizationIds)) {
            $query->whereIn('id', array_intersect($ownedOrganisationIds->toArray(), $organizationIds));
        }

        // Apply search filter if provided
        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($status) {
            $query->where('status', $status);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get product IDs for the specified organisation
     *
     * @param int|null $organisationId
     * @return array Array of product primary key IDs (not store_variant_ids)
     */
    public function getOrganisationProductIds(?int $organisationId): array
    {
        // If no organisation ID provided, return empty array (no filtering)
        if (empty($organisationId)) {
            return [];
        }

        // Get the organisation and its product IDs
        $organisation = Organisation::find($organisationId);

        if (!$organisation) {
            return [];
        }

        return $organisation->products()->pluck('id')->toArray();
    }
}
