<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

final class EmailNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the notification may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the notification can run.
     */
    public int $timeout = 60;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        private readonly string $templateCode,
        private readonly array $variables = [],
        private readonly ?string $customSubject = null,
        private readonly ?string $customContent = null
    ) {
        // Set queue name from config
        $this->onQueue(config('mail.queue.name', 'emails'));
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        try {
            // If custom content is provided, use it directly
            if ($this->customSubject && $this->customContent) {
                return $this->buildCustomMail();
            }

            // Otherwise, use template
            return $this->buildTemplateMail();

        } catch (\Exception $e) {
            Log::error('Failed to build email notification', [
                'template_code' => $this->templateCode,
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->id ?? null,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Build mail message using template.
     */
    private function buildTemplateMail(): MailMessage
    {
        // Find and validate template
        $template = EmailTemplate::active()->byCode($this->templateCode)->first();
        
        if (!$template) {
            throw new InvalidArgumentException("Email template with code '{$this->templateCode}' not found or inactive");
        }

        if (!$template->isValid()) {
            throw new InvalidArgumentException("Email template '{$this->templateCode}' is not valid");
        }

        // Validate and render template
        $template->validateVariables($this->variables);
        $rendered = $template->render($this->variables);

        // Build mail message
        $mailMessage = new MailMessage();
        $mailMessage->subject($rendered['subject']);
        $mailMessage->view('emails.template', [
            'content' => $rendered['content'],
            'template_code' => $this->templateCode,
            'variables' => $this->variables,
        ]);

        Log::info('Template email notification built', [
            'template_code' => $this->templateCode,
            'subject' => $rendered['subject'],
            'variables_count' => count($this->variables),
        ]);

        return $mailMessage;
    }

    /**
     * Build mail message using custom content.
     */
    private function buildCustomMail(): MailMessage
    {
        $mailMessage = new MailMessage();
        $mailMessage->subject($this->customSubject);
        $mailMessage->view('emails.custom', [
            'content' => $this->customContent,
        ]);

        Log::info('Custom email notification built', [
            'subject' => $this->customSubject,
        ]);

        return $mailMessage;
    }

    /**
     * Determine the time at which the notification should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(10);
    }

    /**
     * Handle a notification failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Email notification failed permanently after all retries', [
            'template_code' => $this->templateCode,
            'variables' => $this->variables,
            'custom_subject' => $this->customSubject,
            'error_message' => $exception->getMessage(),
            'attempts' => $this->tries,
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'template_code' => $this->templateCode,
            'variables' => $this->variables,
            'custom_subject' => $this->customSubject,
            'sent_at' => now()->toISOString(),
        ];
    }
}
