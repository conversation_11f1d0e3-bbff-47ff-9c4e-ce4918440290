<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Product;
use App\Models\User;

/**
 * Product Policy
 * 
 * Handles authorization for product viewing operations.
 * Products are read-only, so only index and show methods are needed.
 * Access is controlled by organization membership and product-level permissions.
 */
final class ProductPolicy
{
    /**
     * Determine whether the user can view any products.
     *
     * Users can view products if they:
     * - Are system administrators (root/admin)
     * - Are organization owners or members (not just users with product-level permissions)
     */
    public function viewAny(User $user): bool
    {
        // System administrators can view all products
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Users must belong to at least one organization as owner or member
        // Users with only product-level permissions (view-reports) cannot list all products
        // Since users can only belong to organizations with owner or member roles,
        // checking organization membership is sufficient
        return $user->getOrganisationIds()->isNotEmpty();
    }

    /**
     * Determine whether the user can view the specific product.
     * 
     * Users can view a product if they:
     * - Are system administrators (root/admin)
     * - Belong to the product's organization
     * - Have specific product-level permissions
     */
    public function view(User $user, Product $product): bool
    {
        // System administrators can view any product
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Check if user belongs to the product's organization
        if ($product->organisation && $user->belongsToOrganisation($product->organisation->id)) {
            return true;
        }

        // Check if user has specific product-level permissions
        return $product->userHasAccess($user, 'view-reports');
    }
}
