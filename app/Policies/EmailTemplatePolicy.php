<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\EmailTemplate;
use App\Models\User;

/**
 * Email Template Policy
 * 
 * Handles authorization for email template management operations.
 * Based on role hierarchy: only admin and root users can manage templates,
 * organization users can only view templates.
 */
final class EmailTemplatePolicy
{
    /**
     * Determine whether the user can view any email templates.
     * 
     * All authenticated users can view email templates for preview purposes.
     */
    public function viewAny(User $user): bool
    {
        // All authenticated users can view email templates
        return true;
    }

    /**
     * Determine whether the user can view the email template.
     * 
     * All authenticated users can view individual email templates.
     */
    public function view(User $user, EmailTemplate $emailTemplate): bool
    {
        // All authenticated users can view email templates
        return true;
    }

    /**
     * Determine whether the user can create email templates.
     * 
     * Only system administrators (root and admin) can create email templates.
     */
    public function create(User $user): bool
    {
        // Only system administrators can create email templates
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can update the email template.
     * 
     * Only system administrators (root and admin) can update email templates.
     */
    public function update(User $user, EmailTemplate $emailTemplate): bool
    {
        // Only system administrators can update email templates
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can delete the email template.
     * 
     * Only system administrators (root and admin) can delete email templates.
     */
    public function delete(User $user, EmailTemplate $emailTemplate): bool
    {
        // Only system administrators can delete email templates
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can preview the email template.
     * 
     * All authenticated users can preview email templates.
     */
    public function preview(User $user, EmailTemplate $emailTemplate): bool
    {
        // All authenticated users can preview email templates
        return true;
    }
}
