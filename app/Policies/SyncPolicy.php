<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\SyncLog;
use App\Models\User;

/**
 * Sync Policy
 * 
 * Authorization policy for sync operations.
 * Only system administrators can access sync functionality.
 */
final class SyncPolicy
{
    /**
     * Determine whether the user can view any sync logs.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can view the sync log.
     */
    public function view(User $user, SyncLog $syncLog): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can create sync logs.
     * This is not used directly but required for authorizeResource.
     */
    public function create(User $user): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can update the sync log.
     * This is not used directly but required for authorizeResource.
     */
    public function update(User $user, SyncLog $syncLog): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can delete the sync log.
     * This is not used directly but required for authorizeResource.
     */
    public function delete(User $user, SyncLog $syncLog): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can trigger a new synchronization.
     */
    public function trigger(User $user): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can retry a failed synchronization.
     */
    public function retry(User $user, SyncLog $syncLog): bool
    {
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can re-validate a failed validation.
     */
    public function revalidate(User $user, SyncLog $syncLog): bool
    {
        return $user->hasSystemAdminAccess();
    }
}
