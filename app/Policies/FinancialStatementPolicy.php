<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\FinancialStatement;
use App\Models\User;
use App\Traits\HasPermissionHelpers;

final class FinancialStatementPolicy
{
    use HasPermissionHelpers;

    /**
     * Determine whether the user can view any models.
     * System admins can view all financial statements, organisation owners/members can view their own organisation's financial statements.
     */
    public function viewAny(User $user): bool
    {
        // System admins can view all financial statements
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Check if user belongs to any organisation (owners/members can view their organisation's financial statements)
        $userOrganisationIds = $user->getOrganisationIds();
        return !$userOrganisationIds->isEmpty();
    }

    /**
     * Determine whether the user can view the model.
     * System admins can view any financial statement, organisation owners/members can view their own organisation's financial statements.
     */
    public function view(User $user, FinancialStatement $financialStatement): bool
    {
        // System admins can view any financial statement
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Check if user belongs to the same organisation as the financial statement
        return $user->belongsToOrganisation($financialStatement->organisation_id);
    }

    /**
     * Determine whether the user can create models.
     * Only system admins can create financial statements.
     */
    public function create(User $user): bool
    {
        // Only system admins can create financial statements
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can update the model.
     * Only system admins can update financial statements.
     */
    public function update(User $user, FinancialStatement $financialStatement): bool
    {
        // Only system admins can update financial statements
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can delete the model.
     * Only system admins can delete financial statements, and only if they are in pending audit status.
     */
    public function delete(User $user, FinancialStatement $financialStatement): bool
    {
        // Only system admins can delete financial statements
        if (!$user->hasSystemAdminAccess()) {
            return false;
        }

        // Only pending audit financial statements can be deleted
        return $financialStatement->isPendingAudit();
    }

    /**
     * Determine whether the user can publish the model.
     * Only system admins can publish financial statements.
     */
    public function publish(User $user, FinancialStatement $financialStatement): bool
    {
        // Only system admins can publish financial statements
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can view financial statements by organisation.
     */
    public function viewByOrganisation(User $user, int $organisationId): bool
    {
        // System admins can view financial statements for any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Check if user belongs to the specified organisation
        return $user->belongsToOrganisation($organisationId);
    }

    /**
     * Determine whether the user can view financial statement statistics.
     */
    public function viewStatistics(User $user, ?int $organisationId = null): bool
    {
        // System admins can view statistics for any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // If organisation ID is specified, check if user belongs to that organisation
        if ($organisationId !== null) {
            return $user->belongsToOrganisation($organisationId);
        }

        // If no organisation ID specified, user must belong to at least one organisation
        $userOrganisationIds = $user->getOrganisationIds();
        return !$userOrganisationIds->isEmpty();
    }

    /**
     * Determine whether the user can create a financial statement for a specific organisation.
     * Only system admins can create financial statements.
     */
    public function createForOrganisation(User $user, int $organisationId): bool
    {
        // Only system admins can create financial statements for any organisation
        return $user->hasSystemAdminAccess();
    }
}
