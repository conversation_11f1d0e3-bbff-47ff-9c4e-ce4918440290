<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;

/**
 * Product Permission Policy
 * 
 * Handles authorization for product permission management operations.
 * Only system administrators (root/admin) and organization owners can manage product permissions.
 */
final class ProductPermissionPolicy
{
    /**
     * Determine whether the user can view any product permissions.
     */
    public function viewAny(User $user): bool
    {
        // Only system administrators can view all product permissions
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can view the product permission.
     */
    public function view(User $user, ProductPermission $productPermission): bool
    {
        // System administrators can view any permission
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Users can view their own permissions
        if ($user->id === $productPermission->user_id) {
            return true;
        }

        // Organization owners can view permissions for their organization's products
        if ($productPermission->product && $productPermission->product->organisation) {
            return $user->hasOrganisationAdminAccess($productPermission->product->organisation->id);
        }

        return false;
    }

    /**
     * Determine whether the user can grant product access to another user.
     */
    public function grantProductAccess(User $user, Product $product, User $targetUser): bool
    {
        // System administrators can grant access to any product
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can grant access to their organization's products
        if ($product->organisation &&
            $user->hasOrganisationAdminAccess($product->organisation->id)) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can revoke product access from another user.
     */
    public function revokeProductAccess(User $user, Product $product, User $targetUser): bool
    {
        // System administrators can revoke access from any product
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can revoke access from their organization's products
        if ($product->organisation &&
            $user->hasOrganisationAdminAccess($product->organisation->id)) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view product permissions for a specific product.
     */
    public function viewProductPermissions(User $user, Product $product): bool
    {
        // System administrators can view permissions for any product
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can view permissions for their organization's products
        if ($product->organisation) {
            return $user->hasOrganisationAdminAccess($product->organisation->id);
        }

        return false;
    }

    /**
     * Determine whether the user can view their own accessible products.
     */
    public function viewAccessibleProducts(User $user): bool
    {
        // All authenticated users can view their own accessible products
        return true;
    }

    /**
     * Determine whether the user can view another user's product permissions.
     */
    public function viewUserProductPermissions(User $user, User $targetUser): bool
    {
        // System administrators can view any user's permissions
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Users can view their own permissions
        if ($user->id === $targetUser->id) {
            return true;
        }

        // Organization owners can view permissions of users in their organizations
        $userOrganisations = $user->organisations->pluck('id')->toArray();
        $targetUserOrganisations = $targetUser->organisations->pluck('id')->toArray();

        // Check if there's any overlap in organizations and user has admin access to those organizations
        $sharedOrganisations = array_intersect($userOrganisations, $targetUserOrganisations);
        foreach ($sharedOrganisations as $organisationId) {
            if ($user->hasOrganisationAdminAccess($organisationId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can grant multiple product permissions.
     */
    public function grantMultipleProductAccess(User $user, array $productIds, User $targetUser): bool
    {
        // System administrators can grant access to any products
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // For organization owners, check each product individually
        $products = Product::whereIn('id', $productIds)->with('organisation')->get();

        foreach ($products as $product) {
            if (!$product->organisation ||
                !$user->hasOrganisationAdminAccess($product->organisation->id)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Determine whether the user can revoke multiple product permissions.
     */
    public function revokeMultipleProductAccess(User $user, array $productIds, User $targetUser): bool
    {
        // System administrators can revoke access from any products
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // For organization owners, check each product individually
        $products = Product::whereIn('id', $productIds)->with('organisation')->get();

        foreach ($products as $product) {
            if (!$product->organisation ||
                !$user->hasOrganisationAdminAccess($product->organisation->id)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Determine whether the user can view organisation product permissions.
     */
    public function viewOrganisationProductPermissions(User $user, int $organisationId): bool
    {
        // System administrators can view permissions for any organisation
        if ($user->hasSystemAdminAccess()) {
            return true;
        }

        // Organization owners can view permissions for their own organisation
        return $user->hasOrganisationAdminAccess($organisationId);
    }
}
