<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;

/**
 * Activity Log Policy
 *
 * Authorization policy for activity log operations.
 * Only system administrators can access activity log functionality.
 * Only contains viewAny method as ActivityLogController only has index endpoint.
 */
final class ActivityLogPolicy
{
    /**
     * Determine whether the user can view any activity logs.
     * Only system administrators (root/admin with system guard) can access activity logs.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasSystemAdminAccess();
    }
}
