<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

final class Organisation extends Model
{
    use HasFactory, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'code',
        'details',
        'remarks',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'details' => 'array',
        ];
    }

    /**
     * Get the options for activity logging.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name',
                'code',
                'details',
                'remarks',
                'status'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => match($eventName) {
                'created' => __('activity.descriptions.organisation.created'),
                'updated' => __('activity.descriptions.organisation.updated'),
                'deleted' => __('activity.descriptions.organisation.deleted'),
                default => __('activity.events.default', ['model' => __('activity.models.organisation'), 'event' => $eventName])
            });
    }

    /**
     * Get the users for the organisation.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_organisation')
            ->withTimestamps();
    }

    /**
     * Get the products for the organisation.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'owner_id', 'code');
    }

    /**
     * Check if the organisation is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the organisation is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the organisation is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === 'suspended';
    }



    /**
     * Suspend the organisation.
     */
    public function suspend(): bool
    {
        return $this->update(['status' => 'suspended']);
    }
}
