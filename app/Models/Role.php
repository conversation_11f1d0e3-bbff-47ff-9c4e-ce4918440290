<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Models\Role as SpatieRole;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

final class Role extends SpatieRole
{
    use LogsActivity;
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'guard_name',
        'organisation_id',
    ];

    /**
     * Get the options for activity logging.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name',
                'guard_name',
                'organisation_id'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => match($eventName) {
                'created' => __('activity.descriptions.role.created'),
                'updated' => __('activity.descriptions.role.updated'),
                'deleted' => __('activity.descriptions.role.deleted'),
                default => __('activity.events.default', ['model' => __('activity.models.role'), 'event' => $eventName])
            });
    }

    /**
     * Get the organisation that owns the role.
     */
    public function organisation(): BelongsTo
    {
        return $this->belongsTo(Organisation::class);
    }

    /**
     * Scope a query to only include roles for a specific organisation.
     */
    public function scopeForOrganisation($query, int $organisationId)
    {
        return $query->where('organisation_id', $organisationId);
    }

    /**
     * Check if the role belongs to a specific organisation.
     */
    public function belongsToOrganisation(int $organisationId): bool
    {
        return $this->organisation_id === $organisationId;
    }
}
