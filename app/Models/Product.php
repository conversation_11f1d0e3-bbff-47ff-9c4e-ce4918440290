<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

final class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_variant_id', 'owner_id', 'sku', 'code', 'name', 'enabled', 'slug', 'release_date', 'package',
        'current_price', 'original_price', 'minimum_price', 'lowest_price_before_discount',
        'price_history', 'store_product_updated_at', 'store_variant_updated_at'
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'price_history' => 'array',
        'release_date' => 'datetime',
        'store_product_updated_at' => 'datetime',
        'store_variant_updated_at' => 'datetime',
    ];

    /**
     * Get the organisation that owns this product.
     */
    public function organisation(): BelongsTo
    {
        return $this->belongsTo(Organisation::class, 'owner_id', 'code');
    }

    /**
     * Get the product permissions for this product.
     */
    public function productPermissions(): HasMany
    {
        return $this->hasMany(ProductPermission::class);
    }

    /**
     * Grant access to a user for this product.
     */
    public function grantAccessToUser(
        User $user,
        string $permissionType = 'view-reports',
        ?User $grantedBy = null,
        ?Carbon $expiresAt = null,
        ?string $notes = null
    ): ProductPermission {
        return ProductPermission::updateOrCreate(
            [
                'user_id' => $user->id,
                'product_id' => $this->id,
                'permission_type' => $permissionType,
            ],
            [
                'expires_at' => $expiresAt,
                'granted_by' => $grantedBy?->id,
                'granted_at' => now(),
                'notes' => $notes,
            ]
        );
    }

    /**
     * Revoke access from a user for this product.
     */
    public function revokeAccessFromUser(User $user, string $permissionType = 'view-reports'): bool
    {
        return $this->productPermissions()
            ->where('user_id', $user->id)
            ->where('permission_type', $permissionType)
            ->delete() > 0;
    }

    /**
     * Get users who have access to this product.
     */
    public function getAuthorizedUsers(string $permissionType = 'view-reports'): Collection
    {
        return User::whereHas('productPermissions', function ($query) use ($permissionType) {
            $query->where('product_id', $this->id)
                  ->where('permission_type', $permissionType)
                  ->valid();
        })->get();
    }

    /**
     * Check if a user has access to this product.
     */
    public function userHasAccess(User $user, string $permissionType = 'view-reports'): bool
    {
        return $this->productPermissions()
            ->where('user_id', $user->id)
            ->where('permission_type', $permissionType)
            ->valid()
            ->exists();
    }

    /**
     * Get all valid permissions for this product with user information.
     */
    public function getValidPermissions(): Collection
    {
        return $this->productPermissions()
            ->with(['user', 'grantedBy'])
            ->valid()
            ->get();
    }
}
