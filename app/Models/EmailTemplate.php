<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

final class EmailTemplate extends Model
{
    use HasFactory, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'code',
        'subject',
        'content',
        'variables',
        'is_active',
        'description',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'variables' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the options for activity logging.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name',
                'code',
                'subject',
                'content',
                'variables',
                'is_active',
                'description'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => match($eventName) {
                'created' => __('activity.descriptions.email_template.created'),
                'updated' => __('activity.descriptions.email_template.updated'),
                'deleted' => __('activity.descriptions.email_template.deleted'),
                default => __('activity.events.default', ['model' => __('activity.models.email_template'), 'event' => $eventName])
            });
    }

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to find template by code.
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    /**
     * Render the template with provided variables.
     *
     * @param array<string, mixed> $variables
     * @return array{subject: string, content: string}
     * @throws \InvalidArgumentException
     */
    public function render(array $variables = []): array
    {
        $this->validateVariables($variables);

        $renderedSubject = $this->renderString($this->subject, $variables);
        $renderedContent = $this->renderString($this->content, $variables);

        return [
            'subject' => $renderedSubject,
            'content' => $renderedContent,
        ];
    }

    /**
     * Get all variable placeholders from template content and subject.
     *
     * @return array<string>
     */
    public function getRequiredVariables(): array
    {
        $subjectVariables = $this->extractVariables($this->subject);
        $contentVariables = $this->extractVariables($this->content);

        return array_unique(array_merge($subjectVariables, $contentVariables));
    }

    /**
     * Check if the template is valid (active and has required fields).
     */
    public function isValid(): bool
    {
        return $this->is_active &&
               !empty($this->name) &&
               !empty($this->code) &&
               !empty($this->subject) &&
               !empty($this->content);
    }

    /**
     * Validate that all required variables are provided.
     *
     * @param array<string, mixed> $variables
     * @throws \InvalidArgumentException
     */
    public function validateVariables(array $variables): void
    {
        $requiredVariables = $this->getRequiredVariables();
        $providedVariables = array_keys($variables);
        $missingVariables = array_diff($requiredVariables, $providedVariables);

        if (!empty($missingVariables)) {
            throw new \InvalidArgumentException(
                'Missing required variables: ' . implode(', ', $missingVariables)
            );
        }
    }

    /**
     * Render a string by replacing variable placeholders.
     *
     * @param string $template
     * @param array<string, mixed> $variables
     */
    private function renderString(string $template, array $variables): string
    {
        $rendered = $template;

        foreach ($variables as $key => $value) {
            // Convert value to string, handling different types appropriately
            $stringValue = $this->convertValueToString($value);

            // Replace {variable_name} placeholders
            $rendered = str_replace('{' . $key . '}', $stringValue, $rendered);
        }

        return $rendered;
    }

    /**
     * Extract variable placeholders from a string.
     *
     * @param string $text
     * @return array<string>
     */
    private function extractVariables(string $text): array
    {
        preg_match_all('/\{([a-zA-Z_][a-zA-Z0-9_]*)\}/', $text, $matches);
        return $matches[1] ?? [];
    }

    /**
     * Convert a value to string for template rendering.
     *
     * @param mixed $value
     */
    private function convertValueToString($value): string
    {
        if (is_null($value)) {
            return '';
        }

        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        if (is_array($value) || is_object($value)) {
            return json_encode($value) ?: '';
        }

        return (string) $value;
    }
}
