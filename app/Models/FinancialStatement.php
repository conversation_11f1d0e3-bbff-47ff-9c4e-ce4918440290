<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class FinancialStatement extends Model
{
    use HasFactory;

    /**
     * Report type constants
     */
    public const TYPE_MONTHLY = 'monthly';
    public const TYPE_QUARTERLY = 'quarterly';
    public const TYPE_YEARLY = 'yearly';
    public const TYPE_CUSTOM = 'custom';

    /**
     * Available report types
     */
    public const REPORT_TYPES = [
        self::TYPE_MONTHLY,
        self::TYPE_QUARTERLY,
        self::TYPE_YEARLY,
        self::TYPE_CUSTOM,
    ];

    /**
     * Report status constants
     */
    public const STATUS_PENDING_AUDIT = 'pending_audit';
    public const STATUS_PUBLISHED = 'published';

    /**
     * Available report statuses
     */
    public const REPORT_STATUSES = [
        self::STATUS_PENDING_AUDIT,
        self::STATUS_PUBLISHED,
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'report_type',
        'report_title',
        'start_date',
        'end_date',
        'report_code',
        'organisation_id',
        'total_amount',
        'total_quantity',
        'total_orders',
        'status',
        'remarks',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'total_amount' => 'integer',
            'total_quantity' => 'integer',
            'total_orders' => 'integer',
        ];
    }

    /**
     * Get the organisation that owns this financial statement.
     */
    public function organisation(): BelongsTo
    {
        return $this->belongsTo(Organisation::class);
    }

    /**
     * Get the orders associated with this financial statement.
     */
    public function orders(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'financial_statement_orders')
            ->withTimestamps();
    }

    /**
     * Get the financial statement orders (pivot records).
     */
    public function financialStatementOrders(): HasMany
    {
        return $this->hasMany(FinancialStatementOrder::class);
    }

    /**
     * Generate report code based on organisation code, report type and date range.
     */
    public function generateReportCode(): string
    {
        if (!$this->organisation) {
            throw new \InvalidArgumentException('Organisation must be loaded to generate report code');
        }

        $orgCode = $this->organisation->code;
        $startDate = Carbon::parse($this->start_date);

        return match ($this->report_type) {
            self::TYPE_MONTHLY => $orgCode . $startDate->format('Ym') . 'm',
            self::TYPE_QUARTERLY => $orgCode . $startDate->format('Y') . sprintf('%02d', $startDate->quarter) . 'q',
            self::TYPE_YEARLY => $orgCode . $startDate->format('Y') . 'y',
            self::TYPE_CUSTOM => $orgCode . $startDate->format('Ymd') . '_' . Carbon::parse($this->end_date)->format('Ymd') . 'c',
            default => throw new \InvalidArgumentException('Invalid report type: ' . $this->report_type),
        };
    }

    /**
     * Generate default report title.
     */
    public function generateReportTitle(): string
    {
        if (!$this->organisation) {
            throw new \InvalidArgumentException('Organisation must be loaded to generate report title');
        }

        $orgName = $this->organisation->name;
        $period = $this->getPeriodDescription();

        return $orgName . ' ' . $period . ' 销售报表';
    }

    /**
     * Get period description based on report type and dates.
     */
    public function getPeriodDescription(): string
    {
        $startDate = Carbon::parse($this->start_date);
        $endDate = Carbon::parse($this->end_date);

        return match ($this->report_type) {
            self::TYPE_MONTHLY => $startDate->format('Y年n月'),
            self::TYPE_QUARTERLY => $startDate->format('Y年') . '第' . $startDate->quarter . '季度',
            self::TYPE_YEARLY => $startDate->format('Y年'),
            self::TYPE_CUSTOM => $startDate->format('Y年n月j日') . ' - ' . $endDate->format('Y年n月j日'),
            default => $startDate->format('Y年n月j日') . ' - ' . $endDate->format('Y年n月j日'),
        };
    }

    /**
     * Validate date range based on report type.
     */
    public function validateDateRange(): bool
    {
        $startDate = Carbon::parse($this->start_date);
        $endDate = Carbon::parse($this->end_date);

        if ($startDate->greaterThan($endDate)) {
            return false;
        }

        return match ($this->report_type) {
            self::TYPE_MONTHLY => $this->validateMonthlyRange($startDate, $endDate),
            self::TYPE_QUARTERLY => $this->validateQuarterlyRange($startDate, $endDate),
            self::TYPE_YEARLY => $this->validateYearlyRange($startDate, $endDate),
            self::TYPE_CUSTOM => true, // Custom ranges are always valid if start <= end
            default => false,
        };
    }

    /**
     * Validate monthly report date range.
     */
    private function validateMonthlyRange(Carbon $startDate, Carbon $endDate): bool
    {
        return $startDate->isSameMonth($endDate) &&
               $startDate->day === 1 &&
               $endDate->day === $endDate->daysInMonth;
    }

    /**
     * Validate quarterly report date range.
     */
    private function validateQuarterlyRange(Carbon $startDate, Carbon $endDate): bool
    {
        $quarterStart = $startDate->copy()->firstOfQuarter();
        $quarterEnd = $startDate->copy()->lastOfQuarter();

        return $startDate->equalTo($quarterStart) && $endDate->equalTo($quarterEnd);
    }

    /**
     * Validate yearly report date range.
     */
    private function validateYearlyRange(Carbon $startDate, Carbon $endDate): bool
    {
        return $startDate->month === 1 &&
               $startDate->day === 1 &&
               $endDate->month === 12 &&
               $endDate->day === 31 &&
               $startDate->year === $endDate->year;
    }

    /**
     * Get the total amount in currency format.
     */
    public function getTotalAmountInCurrencyAttribute(): float
    {
        return $this->total_amount / 100;
    }

    /**
     * Check if the report type is monthly.
     */
    public function isMonthly(): bool
    {
        return $this->report_type === self::TYPE_MONTHLY;
    }

    /**
     * Check if the report type is quarterly.
     */
    public function isQuarterly(): bool
    {
        return $this->report_type === self::TYPE_QUARTERLY;
    }

    /**
     * Check if the report type is yearly.
     */
    public function isYearly(): bool
    {
        return $this->report_type === self::TYPE_YEARLY;
    }

    /**
     * Check if the report type is custom.
     */
    public function isCustom(): bool
    {
        return $this->report_type === self::TYPE_CUSTOM;
    }

    /**
     * Scope to filter by report type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('report_type', $type);
    }

    /**
     * Scope to filter by organisation.
     */
    public function scopeByOrganisation($query, int $organisationId)
    {
        return $query->where('organisation_id', $organisationId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeByDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function ($q) use ($startDate, $endDate) {
                        $q->where('start_date', '<=', $startDate)
                          ->where('end_date', '>=', $endDate);
                    });
    }

    /**
     * Check if the report status is pending audit.
     */
    public function isPendingAudit(): bool
    {
        return $this->status === self::STATUS_PENDING_AUDIT;
    }

    /**
     * Check if the report status is published.
     */
    public function isPublished(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by pending audit status.
     */
    public function scopePendingAudit($query)
    {
        return $query->where('status', self::STATUS_PENDING_AUDIT);
    }

    /**
     * Scope to filter by published status.
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    /**
     * Validate that the financial statement orders are not associated with other financial statements of the same report type.
     *
     * This validation ensures that orders included in this financial statement are not already
     * included in other financial statements with the same report_type.
     */
    public function validateUniqueOrdersByReportType(): bool
    {
        // If no orders are associated, validation passes
        if ($this->financialStatementOrders()->count() === 0) {
            return true;
        }

        // Get all order IDs associated with this financial statement
        $orderIds = $this->financialStatementOrders()->pluck('order_id')->toArray();

        if (empty($orderIds)) {
            return true;
        }

        // Check if any of these orders are associated with other financial statements of the same report type
        $conflictingStatements = self::where('report_type', $this->report_type)
            ->where('id', '!=', $this->id ?? 0) // Exclude current statement (for updates)
            ->whereHas('financialStatementOrders', function ($query) use ($orderIds) {
                $query->whereIn('order_id', $orderIds);
            })
            ->exists();

        return !$conflictingStatements;
    }

    /**
     * Get conflicting financial statements that share orders with the same report type.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getConflictingStatements()
    {
        // Get all order IDs associated with this financial statement
        $orderIds = $this->financialStatementOrders()->pluck('order_id')->toArray();

        if (empty($orderIds)) {
            return collect();
        }

        return self::where('report_type', $this->report_type)
            ->where('id', '!=', $this->id ?? 0)
            ->whereHas('financialStatementOrders', function ($query) use ($orderIds) {
                $query->whereIn('order_id', $orderIds);
            })
            ->with(['organisation', 'financialStatementOrders.order'])
            ->get();
    }
}
