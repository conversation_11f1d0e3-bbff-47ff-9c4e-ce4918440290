<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

final class SyncLog extends Model
{
    use HasFactory, LogsActivity;
    protected $fillable = [
        'sync_type', 'batch_id', 'status', 'started_at', 'completed_at',
        'total_records', 'processed_records', 'success_records', 'failed_records',
        'summary', 'error_message', 'sync_config', 'validation_results'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'summary' => 'array',
        'sync_config' => 'array',
        'validation_results' => 'array',
    ];

    /**
     * Get the options for activity logging.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'sync_type',
                'batch_id',
                'status',
                'started_at',
                'completed_at',
                'total_records',
                'processed_records',
                'success_records',
                'failed_records',
                'summary',
                'error_message',
                'sync_config',
                'validation_results'
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => match($eventName) {
                'created' => __('activity.descriptions.sync_log.created'),
                'updated' => __('activity.descriptions.sync_log.updated'),
                'deleted' => __('activity.descriptions.sync_log.deleted'),
                default => __('activity.events.default', ['model' => __('activity.models.sync_log'), 'event' => $eventName])
            });
    }

    public function records(): HasMany
    {
        return $this->hasMany(SyncRecord::class, 'batch_id', 'batch_id');
    }

    public function getProgressPercentageAttribute(): float
    {
        if ($this->total_records === 0) {
            return 0;
        }
        return round(($this->processed_records / $this->total_records) * 100, 2);
    }
}
