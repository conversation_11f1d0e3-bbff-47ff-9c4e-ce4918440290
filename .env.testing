APP_NAME=Laravel
APP_ENV=testing
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_ADMIN_EMAIL=<EMAIL>
APP_ADMIN_PASSWORD=password

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_TIMEZONE=UTC

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

MAIN_DB_CONNECTION=mysql
MAIN_DB_HOST=mysql
MAIN_DB_PORT=3306
MAIN_DB_DATABASE=laravel_testing
MAIN_DB_USERNAME=root
MAIN_DB_PASSWORD=test123

# Testing database configuration
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=laravel_testing
DB_USERNAME=root
DB_PASSWORD=test123

# Store database for testing (same as main for testing)
STORE_DB_CONNECTION=mysql
STORE_DB_HOST=mysql
STORE_DB_PORT=3306
STORE_DB_DATABASE=laravel_testing
STORE_DB_USERNAME=root
STORE_DB_PASSWORD=test123

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=redis
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

VITE_APP_NAME="${APP_NAME}"

# sync config
SYNC_BATCH_SIZE=300
SYNC_NOTIFICATION_EMAIL=<EMAIL>
SYNC_MEMORY_LIMIT=512M
SYNC_TIME_LIMIT=0

# performance optimization config (only used configs)
SYNC_BATCH_INSERT_SIZE=300

# scheduled sync control
SYNC_SCHEDULED_INCREMENTAL_ENABLED=true
SYNC_SCHEDULED_FULL_ENABLED=true
SYNC_SCHEDULED_CLEANUP_ENABLED=true

# Activity Log Configuration
# Activity logging is disabled by default in testing environment
# Individual tests can enable it using $this->enableActivityLogging()
ACTIVITY_LOGGER_ENABLED=false
ACTIVITY_LOG_CLEANUP_DAYS=90
ACTIVITY_LOG_CLEANUP_ENABLED=true
