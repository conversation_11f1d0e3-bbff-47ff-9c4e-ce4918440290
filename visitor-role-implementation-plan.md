# Visitor 角色实现方案

## 概述

本文档详细描述了如何在现有系统中实现 `visitor` 角色功能，使用户能够访问特定产品的报表数据，即使该用户不属于产品所在的组织。

## 当前系统分析

### 现有权限系统
- **系统级角色**: `root`, `admin` (guard: system)
- **组织级角色**: `owner`, `member` (guard: api, 绑定到特定组织)
- **权限检查**: 基于用户是否属于某个组织

### 现有数据流程
1. 用户请求报表时提供 `organisation_id`
2. 系统验证用户是否属于该组织
3. 通过组织的 `code` 获取该组织的所有产品ID
4. 使用产品ID构造查询条件获取报表数据

### 问题分析
1. **权限校验问题**: `ReportPolicy` 只检查组织成员身份，无法处理产品级权限
2. **数据获取问题**: 当前逻辑基于组织获取所有产品，无法支持单个产品访问

## 原始方案的逻辑漏洞分析

### 漏洞1：跨组织数据访问风险
**问题描述**: 原始的 `getAccessibleProductIds` 方法中，第86-89行会将用户的所有产品权限ID与当前组织的产品ID合并：
```php
// 原始有问题的逻辑
$productIds = $organisationService->getOrganisationProductIds($organisationId);
$permissionProductIds = $this->getProductPermissionIds();
return array_unique(array_merge($productIds, $permissionProductIds));
```

**安全风险**:
- 用户可能拥有组织A的成员身份和组织B某个产品的访问权限
- 当用户请求组织A的报表时，系统会错误地包含组织B的产品数据
- 违反了组织数据隔离的基本安全原则

### 漏洞2：权限设计逻辑冲突
**问题描述**: 如果用户既属于某个组织，又被单独授予了该组织内某些产品的访问权限，会产生逻辑冲突：
- 组织成员身份：用户可以访问组织内所有产品的报表
- 产品权限：管理员希望限制用户只能访问特定产品的报表

**逻辑问题**:
- 两种权限同时存在时，产品权限的限制作用失效
- 管理员的权限控制意图无法实现
- 权限检查变成冗余操作

### 漏洞3：权限检查顺序错误
**问题描述**: 原始方案没有明确的权限优先级，可能导致权限检查顺序混乱：
- 应该优先检查更精确的产品级权限
- 只有在没有产品权限时才回退到组织权限
- 避免权限升级攻击（通过组织权限绕过产品权限限制）

## 修订后的解决方案

### 核心设计原则
1. **权限优先级**: 产品权限 > 组织权限
2. **数据隔离**: 严格按组织边界过滤数据
3. **精确控制**: 产品权限存在时，不再检查组织权限
4. **安全第一**: 默认拒绝，明确授权才允许访问

## 解决方案设计（优化版 - 使用 Spatie Permission）

### 核心思路
1. **利用 Spatie Permission 系统**: 使用现有的 `permissions` 和 `model_has_permissions` 表
2. **创建产品级权限**: 为每个产品创建 `view-product-{product_id}` 权限
3. **增强权限检查**: 在现有组织权限基础上增加产品权限检查
4. **保持向后兼容**: 完全不影响现有组织级权限逻辑

### 架构设计原则
- **复用现有系统**: 最大化利用 Spatie Permission 的成熟功能
- **一致性**: 与现有权限系统保持完全一致
- **性能优化**: 利用 Spatie 的缓存和查询优化
- **可维护性**: 减少自定义逻辑，降低维护成本

## 详细实现步骤

### 第一步：权限系统扩展

#### 1.1 创建固定权限名称策略
```php
// 使用固定的权限名称，便于 i18n 和管理
const PRODUCT_OBSERVER_PERMISSION = 'view-product-reports';

// 权限检查时结合具体的产品ID
// 使用 Spatie Permission 的 model_has_permissions 表的 team_foreign_key 字段
// 将产品ID存储在 team_foreign_key 字段中实现资源级权限控制
```

#### 1.2 权限系统设计
```php
// 1. 创建固定权限（只需创建一次）
Permission::create([
    'name' => 'view-product-reports',
    'guard_name' => 'api'
]);

// 2. 授予用户对特定产品的权限（使用 team_foreign_key 存储产品ID）
$user->givePermissionTo('view-product-reports', $productId);

// 3. 检查用户是否有特定产品的权限
$user->hasPermissionTo('view-product-reports', $productId);
```

### 第二步：模型层扩展

#### 2.1 扩展 User 模型
添加产品权限相关方法：
```php
/**
 * 获取用户可访问的产品ID（优先产品权限，其次组织权限）
 */
public function getAccessibleProductIds(?int $organisationId = null): array
{
    // 1. 优先检查产品级权限
    $permissionProductIds = $this->getProductPermissionIds();

    // 如果用户有产品级权限，只返回这些产品ID（不再检查组织权限）
    // 这样可以实现精确的权限控制：管理员给用户设置了产品权限，
    // 就是希望限制用户只能看到这些特定产品的报表
    if (!empty($permissionProductIds)) {
        // 如果指定了组织ID，需要验证这些产品是否属于该组织
        // 防止跨组织数据访问
        if ($organisationId) {
            return $this->filterProductsByOrganisation($permissionProductIds, $organisationId);
        }
        return $permissionProductIds;
    }

    // 2. 如果没有产品级权限，则使用组织级权限（现有逻辑）
    if ($organisationId && $this->belongsToOrganisation($organisationId)) {
        $organisationService = app(OrganisationService::class);
        return $organisationService->getOrganisationProductIds($organisationId);
    }

    // 3. 既没有产品权限也不属于组织，返回空数组
    return [];
}

/**
 * 过滤产品ID，确保它们属于指定的组织
 * 防止跨组织数据访问的安全漏洞
 */
private function filterProductsByOrganisation(array $productIds, int $organisationId): array
{
    if (empty($productIds)) {
        return [];
    }

    return DB::table('products')
        ->join('organisations', 'products.organisation_id', '=', 'organisations.id')
        ->where('organisations.id', $organisationId)
        ->whereIn('products.id', $productIds)
        ->pluck('products.id')
        ->toArray();
}

/**
 * 获取用户通过权限可访问的产品ID列表
 */
public function getProductPermissionIds(): array
{
    return DB::table('model_has_permissions')
        ->join('permissions', 'model_has_permissions.permission_id', '=', 'permissions.id')
        ->where('model_has_permissions.model_type', self::class)
        ->where('model_has_permissions.model_id', $this->id)
        ->where('permissions.name', 'view-product-reports')
        ->whereNotNull('model_has_permissions.team_foreign_key')
        ->pluck('model_has_permissions.team_foreign_key')
        ->toArray();
}

/**
 * 检查用户是否有特定产品的访问权限
 */
public function hasProductPermission(int $productId): bool
{
    return $this->hasPermissionTo('view-product-reports', $productId);
}

/**
 * 检查用户是否可以访问特定产品的报表
 * 优先检查产品权限，其次检查组织权限
 */
public function canAccessReportsForProduct(int $productId): bool
{
    // 1. 优先检查产品级权限
    if ($this->hasProductPermission($productId)) {
        return true;
    }

    // 2. 如果没有产品权限，检查组织权限
    $product = Product::find($productId);
    if ($product && $product->organisation) {
        return $this->belongsToOrganisation($product->organisation->id);
    }

    return false;
}
```

#### 2.2 扩展 Product 模型
添加权限相关方法：
```php
/**
 * 获取固定的产品权限名称
 */
public function getPermissionName(): string
{
    return 'view-product-reports';
}

/**
 * 为用户授予产品访问权限（使用产品ID作为team）
 */
public function grantAccessToUser(User $user): void
{
    $user->givePermissionTo($this->getPermissionName(), $this->id);
}

/**
 * 撤销用户的产品访问权限
 */
public function revokeAccessFromUser(User $user): void
{
    $user->revokePermissionTo($this->getPermissionName(), $this->id);
}

/**
 * 获取有访问权限的用户列表
 */
public function getAuthorizedUsers(): Collection
{
    return User::permission($this->getPermissionName(), $this->id)->get();
}
```

### 第三步：权限系统增强

#### 3.1 扩展 ReportPolicy
增加产品级权限检查方法：
```php
/**
 * 检查用户是否可以访问特定产品的报表
 */
public function viewForProduct(User $user, int $productId): bool
{
    return $user->canAccessReportsForProduct($productId);
}

/**
 * 综合检查组织和产品权限
 * 优先检查产品权限，确保权限控制的精确性
 */
public function viewForOrganisationOrProducts(User $user, ?int $organisationId): bool
{
    // 系统管理员拥有所有权限
    if ($user->hasRole(['root', 'admin'], 'system')) {
        return true;
    }

    // 如果没有指定组织ID，拒绝访问
    if (!$organisationId) {
        return false;
    }

    // 检查用户是否有该组织的任何产品权限
    $userProductIds = $user->getAccessibleProductIds($organisationId);

    // 只要用户有可访问的产品ID，就允许访问
    // 具体的产品过滤在数据层面处理
    return !empty($userProductIds);
}
```

#### 3.2 创建 ProductPermissionService
专门处理产品权限的服务类：
```php
/**
 * 确保产品权限存在（系统初始化时调用）
 */
public function ensureProductPermissionExists(): Permission
{
    return Permission::firstOrCreate([
        'name' => 'view-product-reports',
        'guard_name' => 'api'
    ]);
}

/**
 * 授予用户产品访问权限
 */
public function grantProductAccess(User $user, Product $product): void
{
    $this->ensureProductPermissionExists();
    $user->givePermissionTo('view-product-reports', $product->id);
}

/**
 * 撤销用户产品访问权限
 */
public function revokeProductAccess(User $user, Product $product): void
{
    $user->revokePermissionTo('view-product-reports', $product->id);
}

/**
 * 获取用户可访问的产品列表
 */
public function getUserAccessibleProducts(User $user): Collection
{
    $productIds = $user->getProductPermissionIds();
    return Product::whereIn('id', $productIds)->get();
}

/**
 * 获取产品的授权用户列表
 */
public function getProductAuthorizedUsers(Product $product): Collection
{
    return User::permission('view-product-reports', $product->id)->get();
}

/**
 * 批量授予用户多个产品的访问权限
 */
public function grantMultipleProductAccess(User $user, array $productIds): void
{
    $this->ensureProductPermissionExists();

    foreach ($productIds as $productId) {
        $user->givePermissionTo('view-product-reports', $productId);
    }
}
```

### 第四步：控制器层调整

#### 4.1 修改 ReportController
保持现有API接口不变，只需调整权限检查：
```php
// 新的权限检查逻辑，支持组织和产品权限
$this->authorize('viewForOrganisationOrProducts', ['report', $organisationId]);
```

### 第五步：请求处理层优化

#### 5.1 扩展 ReportFilterRequest
修改 `getProcessedData()` 方法：
```php
public function getProcessedData(): array
{
    $data = $this->validated();

    // 处理日期格式
    // ...existing date processing code...

    // 获取用户可访问的产品ID（组织产品 + 权限产品）
    $user = $this->user();
    $organisationId = $this->input('organisation_id');
    $data['product_ids'] = $user->getAccessibleProductIds($organisationId);

    return $data;
}
```

### 第六步：服务层增强

#### 6.1 扩展现有服务
- **OrganisationService**: 保持现有逻辑不变
- **ReportService**: 无需修改，继续使用 `product_ids` 进行过滤
- **新增 ProductPermissionService**: 专门处理产品权限逻辑

### 第七步：API接口扩展

#### 7.1 产品权限管理接口
```php
// 新增API端点
POST   /api/v1/products/{product}/permissions    // 授予产品访问权限
DELETE /api/v1/products/{product}/permissions/{user} // 撤销特定用户的产品访问权限
GET    /api/v1/products/accessible               // 获取当前用户可访问的产品列表
GET    /api/v1/products/{product}/authorized-users // 获取产品的授权用户列表
POST   /api/v1/users/{user}/product-permissions  // 批量授予用户产品权限
GET    /api/v1/users/{user}/product-permissions  // 获取用户的产品权限列表
```

#### 7.2 权限管理控制器示例
```php
// app/Http/Controllers/Api/V1/ProductPermissionController.php

/**
 * 授予用户产品访问权限
 */
public function grantAccess(Product $product, Request $request)
{
    $request->validate([
        'user_id' => 'required|exists:users,id',
        'expires_at' => 'nullable|date|after:now'
    ]);

    $user = User::findOrFail($request->user_id);

    // 权限检查：只有系统管理员或组织所有者可以授予权限
    $this->authorize('grantProductAccess', [$product, $user]);

    $this->productPermissionService->grantProductAccess($user, $product);

    return $this->successResponse(null, 'Product access granted successfully');
}
```

### 第八步：测试覆盖

#### 8.1 单元测试
- User 模型产品权限方法测试
- Product 模型权限方法测试
- ReportPolicy 产品权限测试
- ProductPermissionService 测试

#### 8.2 功能测试
- 产品权限授予/撤销测试
- 报表访问权限测试（组织+产品混合场景）
- 权限边界测试

#### 8.3 集成测试
- 完整的报表访问流程测试
- Spatie Permission 集成测试
- 性能测试（利用 Spatie 缓存）

## 使用 Spatie Permission 的优势

### 1. 系统一致性
- 完全复用现有权限系统架构
- 与组织级权限使用相同的底层机制
- 统一的权限检查和缓存策略

### 2. 成熟稳定
- 利用 Spatie Permission 的成熟功能
- 自动权限缓存和性能优化
- 经过大量项目验证的稳定性

### 3. 开发效率
- 无需创建额外的数据表
- 减少自定义权限逻辑
- 利用现有的权限管理基础设施

### 4. 可扩展性
- 未来可以轻松扩展更多产品权限类型
- 支持权限继承和复杂权限组合
- 与现有角色系统无缝集成

### 5. 向后兼容
- 现有组织级权限逻辑完全保留
- API接口保持不变
- 现有用户权限不受影响

## 风险评估与缓解

### 1. 权限数据一致性
**风险**: 产品删除时权限数据可能残留
**缓解**: 利用 Spatie Permission 的自动清理机制，在产品删除时自动删除相关权限

### 2. 权限管理复杂性
**风险**: 使用 team_foreign_key 存储产品ID可能增加查询复杂性
**缓解**: 封装专门的查询方法，提供清晰的API接口

### 3. 性能影响
**风险**: 增加权限检查可能影响性能
**缓解**: 利用 Spatie Permission 的内置缓存机制，性能影响最小

### 4. i18n 和权限展示
**风险**: 固定权限名称需要合适的国际化处理
**缓解**: 在语言文件中定义权限的显示名称，便于前端展示和管理

## 部署计划

### 阶段1：权限基础设施（1天）
- 创建 ProductPermissionService
- 扩展 Product 模型的权限方法
- 为现有产品创建权限（可选）

### 阶段2：权限检查增强（1-2天）
- 扩展 User 模型的产品权限方法
- 修改 ReportPolicy 支持产品权限
- 调整 ReportFilterRequest 的数据处理逻辑

### 阶段3：API接口实现（1-2天）
- 创建 ProductPermissionController
- 实现权限管理接口
- 添加权限管理的请求验证

### 阶段4：测试验证（2天）
- 单元测试（模型方法、服务类）
- 功能测试（权限授予、报表访问）
- 集成测试（完整流程）

### 阶段5：部署上线（半天）
- 生产环境部署
- 权限数据初始化（如需要）

## 优化后的核心实现思路

```php
// 1. 创建固定的权限（只需创建一次）
$permission = Permission::create([
    'name' => 'view-product-reports',
    'guard_name' => 'api'
]);

// 2. 授予用户对特定产品的权限（使用产品ID作为team）
$user->givePermissionTo('view-product-reports', $productId);

// 3. 检查用户是否有特定产品的权限
$hasAccess = $user->hasPermissionTo('view-product-reports', $productId);

// 4. 获取用户可访问的产品ID（优先产品权限）
$accessibleProductIds = $user->getAccessibleProductIds($organisationId);

// 5. 权限展示和i18n
$permissionDisplayName = __('permissions.view-product-reports'); // "查看产品报表"
```

## 权限命名策略的优势

### 1. **i18n 友好**
- 固定权限名称便于翻译：`view-product-reports` → "查看产品报表"
- 避免动态权限名称带来的翻译复杂性

### 2. **管理简化**
- 只需要一个权限记录，而不是每个产品一个权限
- 权限管理界面更加清晰

### 3. **查询优化**
- 利用 `team_foreign_key` 字段存储产品ID
- 查询时可以高效地过滤特定产品的权限

### 4. **扩展性强**
- 未来可以轻松添加其他产品相关权限（如 `edit-product-reports`）
- 权限体系更加规范和可预测

## 总结

**修订后方案的核心改进：**

### 1. 安全性增强
- **跨组织数据隔离**: 通过 `filterProductsByOrganisation` 方法确保产品权限不会跨越组织边界
- **权限优先级明确**: 产品权限优先于组织权限，避免权限冲突
- **默认拒绝策略**: 没有明确权限时拒绝访问，提高安全性

### 2. 逻辑一致性
- **权限检查顺序**: 优先检查产品权限，避免冗余查询
- **精确权限控制**: 产品权限存在时不再检查组织权限，实现管理员的精确控制意图
- **边界清晰**: 明确区分组织级权限和产品级权限的适用场景

### 3. 性能优化
- **减少冗余查询**: 有产品权限时不再查询组织产品
- **早期返回**: 权限检查采用早期返回策略，减少不必要的计算
- **缓存友好**: 利用 Spatie Permission 的缓存机制

### 4. 技术优势保持
- **最大化复用**: 完全利用现有的 Spatie Permission 系统
- **i18n 友好**: 使用固定权限名称，便于国际化处理
- **管理简化**: 减少权限记录数量，简化权限管理
- **查询优化**: 利用 team_foreign_key 实现高效的资源级权限控制
- **扩展性强**: 为未来的产品权限扩展奠定良好基础

### 5. 修订要点总结
1. **`getAccessibleProductIds` 方法重构**:
   - 优先检查产品权限
   - 产品权限存在时跳过组织权限检查
   - 添加跨组织数据过滤保护

2. **`canAccessReportsForProduct` 方法优化**:
   - 明确权限检查顺序
   - 简化逻辑流程

3. **`ReportPolicy` 增强**:
   - 实现精确的权限控制逻辑
   - 确保数据访问的安全性

这个修订后的方案不仅解决了原始设计中的安全漏洞和逻辑问题，还保持了技术架构的优雅性和可维护性，为产品级权限控制提供了一个安全、高效、可扩展的解决方案。
