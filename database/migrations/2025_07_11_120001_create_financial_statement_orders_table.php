<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_statement_orders', function (Blueprint $table) {
            $table->id();

            // Foreign key relationships
            $table->foreignId('financial_statement_id')
                  ->constrained('financial_statements')
                  ->onDelete('cascade')
                  ->comment('Financial statement this record belongs to');

            $table->foreignId('order_id')
                  ->constrained('orders')
                  ->onDelete('cascade')
                  ->comment('Order included in the financial statement');

            // Standard timestamps
            $table->timestamps();

            // Unique constraint to prevent duplicate entries
            $table->unique(['financial_statement_id', 'order_id'], 'unique_statement_order');

            // Indexes for performance
            $table->index('financial_statement_id', 'idx_financial_statement_id');
            $table->index('order_id', 'idx_order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_statement_orders');
    }
};
