<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('permission_type')->default('view-reports')->comment('Permission type: view-reports, edit-reports, etc.');
            $table->timestamp('granted_at')->useCurrent()->comment('When the permission was granted');
            $table->timestamp('expires_at')->nullable()->comment('When the permission expires (null = never expires)');
            $table->foreignId('granted_by')->nullable()->constrained('users')->comment('User who granted this permission');
            $table->text('notes')->nullable()->comment('Notes about this permission grant');
            $table->timestamps();

            // Ensure user can only have one permission record per product per permission type
            $table->unique(['user_id', 'product_id', 'permission_type'], 'unique_user_product_permission');

            // Indexes for optimized queries
            $table->index(['user_id', 'permission_type'], 'idx_user_permission_type');
            $table->index(['product_id', 'permission_type'], 'idx_product_permission_type');
            $table->index('expires_at', 'idx_expires_at');
            $table->index('granted_at', 'idx_granted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_permissions');
    }
};
