<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_statements', function (Blueprint $table) {
            $table->id();

            // Report type and identification
            $table->enum('report_type', ['monthly', 'quarterly', 'yearly', 'custom'])
                  ->comment('Report type: monthly, quarterly, yearly, or custom period');
            $table->string('report_title')->comment('Report title');
            $table->string('report_code')->unique()->comment('Generated report code based on org code and period');

            // Date range
            $table->date('start_date')->comment('Report start date');
            $table->date('end_date')->comment('Report end date');

            // Organisation relationship
            $table->foreignId('organisation_id')
                  ->constrained('organisations')
                  ->onDelete('cascade')
                  ->comment('Organisation this report belongs to');

            // Financial summary data (amounts in cents)
            $table->bigInteger('total_amount')->default(0)->comment('Total amount of all orders (cents)');
            $table->integer('total_quantity')->default(0)->comment('Total quantity of all order items');
            $table->integer('total_orders')->default(0)->comment('Total number of orders included');

            // Report status
            $table->enum('status', ['pending_audit', 'published'])
                  ->default('pending_audit')
                  ->comment('Report status: pending_audit (待审计), published (已发布)');

            // Additional information
            $table->text('remarks')->nullable()->comment('Admin remarks or notes');

            // Standard timestamps
            $table->timestamps();

            // Indexes for performance
            $table->index('report_type', 'idx_report_type');
            $table->index('organisation_id', 'idx_organisation_id');
            $table->index(['start_date', 'end_date'], 'idx_date_range');
            $table->index('report_code', 'idx_report_code');

            // Composite index for common queries
            $table->index(['organisation_id', 'report_type', 'start_date'], 'idx_org_type_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_statements');
    }
};
