<?php

declare(strict_types=1);

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        DB::transaction(function () {
            // 1. Check if any users are assigned visitor roles
            $visitorRoleUsers = DB::table('model_has_roles')
                ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                ->where('roles.name', 'visitor')
                ->where('roles.guard_name', 'api')
                ->count();

            if ($visitorRoleUsers > 0) {
                throw new \Exception(
                    "Cannot remove visitor roles: {$visitorRoleUsers} users still have visitor role assignments. " .
                    "Please migrate these users to use ProductPermission system first."
                );
            }

            // 2. Remove all visitor role records
            Role::where('name', 'visitor')
                ->where('guard_name', 'api')
                ->delete();
        });
    }

    public function down(): void
    {
        // Re-create visitor roles (if rollback is needed)
        $organisations = DB::table('organisations')->get();

        foreach ($organisations as $organisation) {
            Role::firstOrCreate([
                'name' => 'visitor',
                'guard_name' => 'api',
                'organisation_id' => $organisation->id,
            ]);
        }
    }
};
