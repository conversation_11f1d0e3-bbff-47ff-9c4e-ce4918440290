<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255)->comment('Template name');
            $table->string('code', 100)->unique()->comment('Template code for calling');
            $table->string('subject', 500)->comment('Email subject');
            $table->longText('content')->comment('Email content (supports HTML and variable placeholders)');
            $table->json('variables')->nullable()->comment('Available variables list and descriptions');
            $table->boolean('is_active')->default(true)->comment('Whether the template is active');
            $table->text('description')->nullable()->comment('Template description');
            $table->timestamps();

            // Indexes for optimized queries
            $table->index('code', 'idx_code');
            $table->index('is_active', 'idx_is_active');
        });

        // Insert default email templates
        $this->insertDefaultTemplates();
    }

    /**
     * Insert default email templates.
     */
    private function insertDefaultTemplates(): void
    {
        $templates = [
            [
                'name' => 'User Email Verification',
                'code' => 'user_email_verification',
                'subject' => 'Email Verification Code',
                'content' => $this->getUserVerificationTemplate(),
                'variables' => json_encode(['user_name', 'verification_code', 'expiry_time']),
                'is_active' => true,
                'description' => 'Template for user email verification during registration',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Organization Invitation',
                'code' => 'organization_invitation',
                'subject' => 'Invitation to Join {organization_name}',
                'content' => $this->getOrganizationInvitationTemplate(),
                'variables' => json_encode(['user_name', 'organization_name', 'inviter_name', 'invitation_link', 'expiry_date']),
                'is_active' => true,
                'description' => 'Template for organization invitation emails',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Password Reset',
                'code' => 'password_reset',
                'subject' => 'Password Reset Request',
                'content' => $this->getPasswordResetTemplate(),
                'variables' => json_encode(['user_name', 'reset_link', 'expiry_time']),
                'is_active' => true,
                'description' => 'Template for password reset emails',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('email_templates')->insert($templates);
    }

    /**
     * Get the user email verification template content.
     */
    private function getUserVerificationTemplate(): string
    {
        return '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
        .footer { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
        .verification-code { background-color: #e3f2fd; padding: 15px; text-align: center; border-radius: 4px; margin: 20px 0; }
        .code { font-size: 24px; font-weight: bold; color: #1976d2; letter-spacing: 2px; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Email Verification</h1>
    </div>
    <div class="content">
        <p>Hello <strong>{user_name}</strong>,</p>
        <p>Thank you for registering with JAST Partner Portal. To complete your registration, please verify your email address using the verification code below:</p>

        <div class="verification-code">
            <div class="code">{verification_code}</div>
        </div>

        <div class="warning">
            <strong>Important:</strong> This verification code will expire in <strong>{expiry_time}</strong>. Please use it as soon as possible.
        </div>

        <p>If you did not request this verification, please ignore this email.</p>
        <p>Thank you,<br>The JAST Partner Portal Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
    </div>
</body>
</html>';
    }

    /**
     * Get the organization invitation template content.
     */
    private function getOrganizationInvitationTemplate(): string
    {
        return '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Invitation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
        .footer { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
        .invitation-box { background-color: #e8f5e8; border: 1px solid #4caf50; padding: 20px; border-radius: 4px; margin: 20px 0; text-align: center; }
        .organization-name { font-size: 20px; font-weight: bold; color: #2e7d32; margin: 10px 0; }
        .accept-button { display: inline-block; background-color: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 15px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>You are Invited!</h1>
    </div>
    <div class="content">
        <p>Hello <strong>{user_name}</strong>,</p>
        <p><strong>{inviter_name}</strong> has invited you to join their organization on JAST Partner Portal.</p>

        <div class="invitation-box">
            <p>Organization:</p>
            <div class="organization-name">{organization_name}</div>
            <a href="{invitation_link}" class="accept-button">Accept Invitation</a>
        </div>

        <div class="warning">
            <strong>Important:</strong> This invitation will expire on <strong>{expiry_date}</strong>. Please accept it before the expiration date.
        </div>

        <p>If you do not wish to join this organization, you can safely ignore this email.</p>
        <p>Thank you,<br>The JAST Partner Portal Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
    </div>
</body>
</html>';
    }

    /**
     * Get the password reset template content.
     */
    private function getPasswordResetTemplate(): string
    {
        return '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
        .footer { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
        .reset-box { background-color: #fff3e0; border: 1px solid #ff9800; padding: 20px; border-radius: 4px; margin: 20px 0; text-align: center; }
        .reset-button { display: inline-block; background-color: #ff9800; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 15px 0; }
        .warning { background-color: #ffebee; border: 1px solid #f44336; padding: 10px; border-radius: 4px; margin: 15px 0; }
        .security-note { background-color: #e3f2fd; border: 1px solid #2196f3; padding: 10px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Password Reset Request</h1>
    </div>
    <div class="content">
        <p>Hello <strong>{user_name}</strong>,</p>
        <p>We received a request to reset your password for your JAST Partner Portal account.</p>

        <div class="reset-box">
            <p>Click the button below to reset your password:</p>
            <a href="{reset_link}" class="reset-button">Reset Password</a>
        </div>

        <div class="warning">
            <strong>Important:</strong> This password reset link will expire in <strong>{expiry_time}</strong>. Please use it as soon as possible.
        </div>

        <div class="security-note">
            <strong>Security Note:</strong> If you did not request this password reset, please ignore this email and consider changing your password if you suspect unauthorized access to your account.
        </div>

        <p>Thank you,<br>The JAST Partner Portal Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
    </div>
</body>
</html>';
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};
