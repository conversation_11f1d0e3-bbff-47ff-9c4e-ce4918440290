<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

final class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            [
                'store_variant_id' => 1001,
                'owner_id' => 'TECH001', // Tech Solutions Ltd
                'sku' => 'GAME-001',
                'code' => 'adventure-quest-deluxe',
                'name' => 'Adventure Quest Deluxe',
                'slug' => 'adventure-quest-deluxe',
                'enabled' => true,
                'release_date' => now()->subMonths(6),
                'package' => 'images/products/adventure-quest-deluxe.jpg',
                'current_price' => 2999, // $29.99
                'original_price' => 3999, // $39.99
                'minimum_price' => 1999, // $19.99
                'lowest_price_before_discount' => 2499, // $24.99
                'price_history' => [
                    ['price' => 3999, 'date' => now()->subMonths(6)->toDateString()],
                    ['price' => 2999, 'date' => now()->subMonths(3)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(5),
                'store_variant_updated_at' => now()->subDays(3),
            ],
            [
                'store_variant_id' => 1002,
                'owner_id' => 'TECH001', // Tech Solutions Ltd
                'sku' => 'GAME-002',
                'code' => 'space-explorer-premium',
                'name' => 'Space Explorer Premium',
                'slug' => 'space-explorer-premium',
                'enabled' => true,
                'release_date' => now()->subMonths(3),
                'package' => 'images/products/space-explorer-premium.jpg',
                'current_price' => 4999, // $49.99
                'original_price' => 5999, // $59.99
                'minimum_price' => 3999, // $39.99
                'lowest_price_before_discount' => 4499, // $44.99
                'price_history' => [
                    ['price' => 5999, 'date' => now()->subMonths(3)->toDateString()],
                    ['price' => 4999, 'date' => now()->subMonths(1)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(2),
                'store_variant_updated_at' => now()->subDays(1),
            ],
            [
                'store_variant_id' => 1006,
                'owner_id' => 'TECH001', // Tech Solutions Ltd
                'sku' => 'GAME-006',
                'code' => 'cyber-warfare-elite',
                'name' => 'Cyber Warfare Elite',
                'slug' => 'cyber-warfare-elite',
                'enabled' => true,
                'release_date' => now()->subMonths(4),
                'package' => 'images/products/cyber-warfare-elite.jpg',
                'current_price' => 3499, // $34.99
                'original_price' => 4499, // $44.99
                'minimum_price' => 2999, // $29.99
                'lowest_price_before_discount' => 3999, // $39.99
                'price_history' => [
                    ['price' => 4499, 'date' => now()->subMonths(4)->toDateString()],
                    ['price' => 3499, 'date' => now()->subMonths(2)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(3),
                'store_variant_updated_at' => now()->subDays(2),
            ],
            [
                'store_variant_id' => 1007,
                'owner_id' => 'TECH001', // Tech Solutions Ltd
                'sku' => 'GAME-007',
                'code' => 'puzzle-master-pro',
                'name' => 'Puzzle Master Pro',
                'slug' => 'puzzle-master-pro',
                'enabled' => true,
                'release_date' => now()->subMonths(5),
                'package' => 'images/products/puzzle-master-pro.jpg',
                'current_price' => 1999, // $19.99
                'original_price' => 2499, // $24.99
                'minimum_price' => 1499, // $14.99
                'lowest_price_before_discount' => 1999, // $19.99
                'price_history' => [
                    ['price' => 2499, 'date' => now()->subMonths(5)->toDateString()],
                    ['price' => 1999, 'date' => now()->subMonths(3)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(4),
                'store_variant_updated_at' => now()->subDays(3),
            ],
            [
                'store_variant_id' => 1008,
                'owner_id' => 'TECH001', // Tech Solutions Ltd
                'sku' => 'GAME-008',
                'code' => 'strategy-commander',
                'name' => 'Strategy Commander',
                'slug' => 'strategy-commander',
                'enabled' => true,
                'release_date' => now()->subMonths(7),
                'package' => 'images/products/strategy-commander.jpg',
                'current_price' => 5999, // $59.99
                'original_price' => 6999, // $69.99
                'minimum_price' => 4999, // $49.99
                'lowest_price_before_discount' => 5499, // $54.99
                'price_history' => [
                    ['price' => 6999, 'date' => now()->subMonths(7)->toDateString()],
                    ['price' => 5999, 'date' => now()->subMonths(4)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(6),
                'store_variant_updated_at' => now()->subDays(4),
            ],
            [
                'store_variant_id' => 1009,
                'owner_id' => 'TECH001', // Tech Solutions Ltd
                'sku' => 'GAME-009',
                'code' => 'racing-simulator-vr',
                'name' => 'Racing Simulator VR',
                'slug' => 'racing-simulator-vr',
                'enabled' => true,
                'release_date' => now()->subMonths(8),
                'package' => 'images/products/racing-simulator-vr.jpg',
                'current_price' => 7999, // $79.99
                'original_price' => 8999, // $89.99
                'minimum_price' => 6999, // $69.99
                'lowest_price_before_discount' => 7499, // $74.99
                'price_history' => [
                    ['price' => 8999, 'date' => now()->subMonths(8)->toDateString()],
                    ['price' => 7999, 'date' => now()->subMonths(5)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(8),
                'store_variant_updated_at' => now()->subDays(6),
            ],

            [
                'store_variant_id' => 1003,
                'owner_id' => 'HEALTH001', // Healthcare Innovations
                'sku' => 'GAME-003',
                'code' => 'mystery-mansion-collector',
                'name' => 'Mystery Mansion Collector Edition',
                'slug' => 'mystery-mansion-collector',
                'enabled' => false, // Disabled product for testing
                'release_date' => now()->addMonths(1), // Future release
                'package' => 'images/products/mystery-mansion-collector.jpg',
                'current_price' => 7999, // $79.99
                'original_price' => 7999, // $79.99
                'minimum_price' => 5999, // $59.99
                'lowest_price_before_discount' => 7999, // $79.99
                'price_history' => [
                    ['price' => 7999, 'date' => now()->subWeeks(2)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(7),
                'store_variant_updated_at' => now()->subDays(7),
            ],
            [
                'store_variant_id' => 1004,
                'owner_id' => null, // No owner for testing
                'sku' => 'GAME-004',
                'code' => 'racing-legends-standard',
                'name' => 'Racing Legends Standard',
                'slug' => 'racing-legends-standard',
                'enabled' => true,
                'release_date' => now()->subMonths(12),
                'package' => 'images/products/racing-legends-standard.jpg',
                'current_price' => 1999, // $19.99
                'original_price' => 2999, // $29.99
                'minimum_price' => 999, // $9.99
                'lowest_price_before_discount' => 1499, // $14.99
                'price_history' => [
                    ['price' => 2999, 'date' => now()->subMonths(12)->toDateString()],
                    ['price' => 2499, 'date' => now()->subMonths(8)->toDateString()],
                    ['price' => 1999, 'date' => now()->subMonths(4)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(10),
                'store_variant_updated_at' => now()->subDays(8),
            ],
            [
                'store_variant_id' => 1005,
                'owner_id' => 'HEALTH001', // Healthcare Innovations
                'sku' => 'GAME-005',
                'code' => 'fantasy-kingdom-ultimate',
                'name' => 'Fantasy Kingdom Ultimate',
                'slug' => 'fantasy-kingdom-ultimate',
                'enabled' => true,
                'release_date' => now()->subMonths(2),
                'package' => 'images/products/fantasy-kingdom-ultimate.jpg',
                'current_price' => 5499, // $54.99
                'original_price' => 6999, // $69.99
                'minimum_price' => 4999, // $49.99
                'lowest_price_before_discount' => 5999, // $59.99
                'price_history' => [
                    ['price' => 6999, 'date' => now()->subMonths(2)->toDateString()],
                    ['price' => 5499, 'date' => now()->subWeeks(3)->toDateString()],
                ],
                'store_product_updated_at' => now()->subDays(1),
                'store_variant_updated_at' => now()->subHours(12),
            ],
        ];

        foreach ($products as $productData) {
            Product::create($productData);
        }
    }
}
