<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

final class OrderSeeder extends Seeder
{
    /**
     * TECH001 organization's first 3 products for focused testing
     */
    private const TECH001_PRODUCTS = [
        [
            'store_variant_id' => 1001,
            'name' => 'Adventure Quest Deluxe',
            'price' => 2999, // $29.99
        ],
        [
            'store_variant_id' => 1002,
            'name' => 'Space Explorer Premium',
            'price' => 4999, // $49.99
        ],
        [
            'store_variant_id' => 1006,
            'name' => 'Cyber Warfare Elite',
            'price' => 3499, // $34.99
        ],
    ];

    /**
     * Countries for geographic distribution
     */
    private const COUNTRIES = ['US', 'GB', 'DE', 'FR', 'CA', 'AU', 'JP', 'IT', 'ES', 'NL'];

    /**
     * Currency codes by country
     */
    private const CURRENCIES = [
        'US' => 'USD', 'GB' => 'GBP', 'DE' => 'EUR', 'FR' => 'EUR',
        'CA' => 'CAD', 'AU' => 'AUD', 'JP' => 'JPY', 'IT' => 'EUR',
        'ES' => 'EUR', 'NL' => 'EUR',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create comprehensive orders for TECH001 products with time distribution
        $this->createTech001Orders();

        // Create some additional orders for other products
        $this->createOtherOrders();

        // Create some cancelled orders for testing
        $this->createCancelledOrders();
    }

    /**
     * Create comprehensive orders for TECH001 products with time distribution
     */
    private function createTech001Orders(): void
    {
        $orderCounter = 10001;
        $itemCounter = 20001;
        $customerCounter = 1001;

        // Generate orders across different time periods for better analytics testing
        // Total: 495 orders (within 500 limit)
        $timeRanges = [
            // Last 12 months for monthly reports
            ['start' => now()->subMonths(12), 'end' => now()->subMonths(11), 'count' => 35],
            ['start' => now()->subMonths(11), 'end' => now()->subMonths(10), 'count' => 30],
            ['start' => now()->subMonths(10), 'end' => now()->subMonths(9), 'count' => 40],
            ['start' => now()->subMonths(9), 'end' => now()->subMonths(8), 'count' => 35],
            ['start' => now()->subMonths(8), 'end' => now()->subMonths(7), 'count' => 38],
            ['start' => now()->subMonths(7), 'end' => now()->subMonths(6), 'count' => 45],
            ['start' => now()->subMonths(6), 'end' => now()->subMonths(5), 'count' => 42],
            ['start' => now()->subMonths(5), 'end' => now()->subMonths(4), 'count' => 48],
            ['start' => now()->subMonths(4), 'end' => now()->subMonths(3), 'count' => 45],
            ['start' => now()->subMonths(3), 'end' => now()->subMonths(2), 'count' => 52],
            ['start' => now()->subMonths(2), 'end' => now()->subMonths(1), 'count' => 40],
            ['start' => now()->subMonths(1), 'end' => now()->subDays(1), 'count' => 35],
            ['start' => now()->startOfDay(), 'end' => now(), 'count' => 10], // Today's orders
        ];

        // Create additional 50 orders for July 7th hour distribution testing
        $july7Orders = [
            'start' => Carbon::create(2025, 3, 1, 5, 0, 0),
            'end' => Carbon::create(2025, 3, 1, 20, 10, 10),
            'count' => 20
        ];

        // Process regular time ranges
        foreach ($timeRanges as $range) {
            for ($i = 0; $i < $range['count']; $i++) {
                $completedAt = $this->randomTimeBetween($range['start'], $range['end']);
                $country = $this->getRandomCountry();
                $currency = self::CURRENCIES[$country];

                // Randomly select 1-3 products for this order
                $selectedProducts = $this->getRandomProducts(rand(1, 3));

                // Calculate order totals
                $itemsTotal = 0;
                $orderItems = [];

                foreach ($selectedProducts as $product) {
                    $quantity = rand(1, 3);
                    $unitPrice = $product['price'];
                    $unitsTotal = $unitPrice * $quantity;
                    $adjustmentsTotal = rand(0, 1) ? -rand(100, 500) : 0; // Random discount
                    $total = $unitsTotal + $adjustmentsTotal;

                    $itemsTotal += $unitsTotal;

                    $orderItems[] = [
                        'store_order_item_id' => $itemCounter++,
                        'store_variant_id' => $product['store_variant_id'],
                        'product_name' => $product['name'],
                        'variant_name' => 'Standard Edition',
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'units_total' => $unitsTotal,
                        'adjustments_total' => $adjustmentsTotal,
                        'total' => $total,
                        'quantity_refunded' => 0,
                    ];
                }

                $adjustmentsTotal = array_sum(array_column($orderItems, 'adjustments_total'));
                $totalAmount = $itemsTotal + $adjustmentsTotal;

                // Determine if this should be a refunded order (10% chance)
                $isRefunded = rand(1, 10) === 1;
                $refundData = $isRefunded ? $this->generateRefundData($totalAmount, $completedAt) : [];

                $orderData = array_merge([
                    'store_order_id' => $orderCounter++,
                    'order_number' => sprintf('ORD-2025-%04d', $orderCounter - 10001),
                    'state' => 'completed',
                    'completed_at' => $completedAt,
                    'items_total' => $itemsTotal,
                    'adjustments_total' => $adjustmentsTotal,
                    'total_amount' => $totalAmount,
                    'currency_code' => $currency,
                    'payment_state' => 'completed',
                    'shipping_country' => $country,
                    'customer_id' => $customerCounter++,
                    'store_updated_at' => $completedAt,
                ], $refundData);

                $this->createOrderWithItems($orderData, $orderItems, $isRefunded);
            }
        }

        // Create July 7th orders for hour distribution testing
        for ($i = 0; $i < $july7Orders['count']; $i++) {
            $completedAt = $this->randomTimeBetween($july7Orders['start'], $july7Orders['end']);
            $country = $this->getRandomCountry();
            $currency = self::CURRENCIES[$country];

            // Randomly select 1-3 products for this order
            $selectedProducts = $this->getRandomProducts(rand(1, 3));

            // Calculate order totals
            $itemsTotal = 0;
            $orderItems = [];

            foreach ($selectedProducts as $product) {
                $quantity = rand(1, 3);
                $unitPrice = $product['price'];
                $unitsTotal = $unitPrice * $quantity;
                $adjustmentsTotal = rand(0, 1) ? -rand(100, 500) : 0; // Random discount
                $total = $unitsTotal + $adjustmentsTotal;

                $itemsTotal += $unitsTotal;

                $orderItems[] = [
                    'store_order_item_id' => $itemCounter++,
                    'store_variant_id' => $product['store_variant_id'],
                    'product_name' => $product['name'],
                    'variant_name' => 'Standard Edition',
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'units_total' => $unitsTotal,
                    'adjustments_total' => $adjustmentsTotal,
                    'total' => $total,
                    'quantity_refunded' => 0,
                ];
            }

            $adjustmentsTotal = array_sum(array_column($orderItems, 'adjustments_total'));
            $totalAmount = $itemsTotal + $adjustmentsTotal;

            // Determine if this should be a refunded order (10% chance)
            $isRefunded = rand(1, 10) === 1;
            $refundData = $isRefunded ? $this->generateRefundData($totalAmount, $completedAt) : [];

            $orderData = array_merge([
                'store_order_id' => $orderCounter++,
                'order_number' => sprintf('ORD-2025-%04d', $orderCounter - 10001),
                'state' => 'completed',
                'completed_at' => $completedAt,
                'items_total' => $itemsTotal,
                'adjustments_total' => $adjustmentsTotal,
                'total_amount' => $totalAmount,
                'currency_code' => $currency,
                'payment_state' => 'completed',
                'shipping_country' => $country,
                'customer_id' => $customerCounter++,
                'store_updated_at' => $completedAt,
            ], $refundData);

            $this->createOrderWithItems($orderData, $orderItems, $isRefunded);
        }
    }
    /**
     * Create some additional orders for other products (non-TECH001)
     */
    private function createOtherOrders(): void
    {
        // Create a few orders for other products to maintain existing test data
        $otherOrders = [
            [
                'store_order_id' => 90001,
                'order_number' => 'ORD-2025-9001',
                'state' => 'completed',
                'completed_at' => Carbon::now()->subDays(3),
                'items_total' => 8000,
                'adjustments_total' => 800,
                'total_amount' => 8800,
                'currency_code' => 'GBP',
                'payment_state' => 'completed',
                'shipping_country' => 'GB',
                'customer_id' => 9001,
                'store_updated_at' => Carbon::now()->subDays(3),
                'items' => [
                    [
                        'store_order_item_id' => 90001,
                        'store_variant_id' => 1004,
                        'product_name' => 'Racing Legends Standard',
                        'variant_name' => 'Standard Edition',
                        'quantity' => 2,
                        'unit_price' => 1999,
                        'units_total' => 3998,
                        'adjustments_total' => 400,
                        'total' => 4398,
                        'quantity_refunded' => 0,
                    ],
                    [
                        'store_order_item_id' => 90002,
                        'store_variant_id' => 1005,
                        'product_name' => 'Fantasy Kingdom Ultimate',
                        'variant_name' => 'Digital Download',
                        'quantity' => 1,
                        'unit_price' => 5499,
                        'units_total' => 5499,
                        'adjustments_total' => 400,
                        'total' => 5899,
                        'quantity_refunded' => 0,
                    ],
                ],
            ],
        ];

        foreach ($otherOrders as $orderData) {
            $this->createOrderWithItems($orderData, $orderData['items']);
        }
    }

    /**
     * Create cancelled orders for testing
     */
    private function createCancelledOrders(): void
    {
        $cancelledOrders = [
            [
                'store_order_id' => 80001,
                'order_number' => 'ORD-2025-8001',
                'state' => 'cancelled',
                'completed_at' => Carbon::now()->subDays(5),
                'items_total' => 2999,
                'adjustments_total' => 0,
                'total_amount' => 2999,
                'currency_code' => 'USD',
                'payment_state' => 'cancelled',
                'shipping_country' => 'US',
                'customer_id' => 8001,
                'store_updated_at' => Carbon::now()->subDays(5),
                'items' => [
                    [
                        'store_order_item_id' => 80001,
                        'store_variant_id' => 1001,
                        'product_name' => 'Adventure Quest Deluxe',
                        'variant_name' => 'Standard Edition',
                        'quantity' => 1,
                        'unit_price' => 2999,
                        'units_total' => 2999,
                        'adjustments_total' => 0,
                        'total' => 2999,
                        'quantity_refunded' => 0,
                    ],
                ],
            ],
            [
                'store_order_id' => 80002,
                'order_number' => 'ORD-2025-8002',
                'state' => 'cancelled',
                'completed_at' => Carbon::now()->subDays(2),
                'items_total' => 4999,
                'adjustments_total' => 0,
                'total_amount' => 4999,
                'currency_code' => 'EUR',
                'payment_state' => 'cancelled',
                'shipping_country' => 'DE',
                'customer_id' => 8002,
                'store_updated_at' => Carbon::now()->subDays(2),
                'items' => [
                    [
                        'store_order_item_id' => 80002,
                        'store_variant_id' => 1002,
                        'product_name' => 'Space Explorer Premium',
                        'variant_name' => 'Standard Edition',
                        'quantity' => 1,
                        'unit_price' => 4999,
                        'units_total' => 4999,
                        'adjustments_total' => 0,
                        'total' => 4999,
                        'quantity_refunded' => 0,
                    ],
                ],
            ],
        ];

        foreach ($cancelledOrders as $orderData) {
            $this->createOrderWithItems($orderData, $orderData['items']);
        }
    }

    /**
     * Generate random time between two Carbon instances
     */
    private function randomTimeBetween(Carbon $start, Carbon $end): Carbon
    {
        $startTimestamp = $start->timestamp;
        $endTimestamp = $end->timestamp;
        $randomTimestamp = rand($startTimestamp, $endTimestamp);

        return Carbon::createFromTimestamp($randomTimestamp);
    }

    /**
     * Get random country from available countries
     */
    private function getRandomCountry(): string
    {
        return self::COUNTRIES[array_rand(self::COUNTRIES)];
    }

    /**
     * Get random selection of TECH001 products
     */
    private function getRandomProducts(int $count): array
    {
        $products = self::TECH001_PRODUCTS;
        shuffle($products);

        return array_slice($products, 0, min($count, count($products)));
    }

    /**
     * Generate refund data for an order
     */
    private function generateRefundData(int $totalAmount, Carbon $completedAt): array
    {
        $refundTypes = ['partial', 'full'];
        $refundType = $refundTypes[array_rand($refundTypes)];

        $refundTotal = $refundType === 'full'
            ? $totalAmount
            : rand(intval($totalAmount * 0.2), intval($totalAmount * 0.8));

        $refundedAt = $completedAt->copy()->addDays(rand(1, 14));

        $comments = [
            'Customer returned item - defective product',
            'Order cancelled by customer within return period',
            'Product not as described',
            'Customer changed mind',
            'Shipping damage reported',
        ];

        return [
            'refund_total' => $refundTotal,
            'refund_comment' => $comments[array_rand($comments)],
            'refund_status' => 'success',
            'refunded_at' => $refundedAt,
        ];
    }

    /**
     * Create order with items efficiently
     */
    private function createOrderWithItems(array $orderData, array $items, bool $isRefunded = false): void
    {
        // Remove items from order data if present
        unset($orderData['items']);

        $order = Order::create($orderData);

        foreach ($items as $itemData) {
            $itemData['order_id'] = $order->id;

            // Handle refunded quantities for refunded orders
            if ($isRefunded && isset($orderData['refund_total']) && $orderData['refund_total'] > 0) {
                // Randomly determine which items are refunded
                if (rand(1, 3) === 1) { // 33% chance this item is refunded
                    $itemData['quantity_refunded'] = rand(1, $itemData['quantity']);
                }
            }

            OrderItem::create($itemData);
        }
    }
}
