<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\FinancialStatement;
use App\Models\FinancialStatementOrder;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FinancialStatementOrder>
 */
final class FinancialStatementOrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<\Illuminate\Database\Eloquent\Model>
     */
    protected $model = FinancialStatementOrder::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'financial_statement_id' => FinancialStatement::factory(),
            'order_id' => Order::factory(),
        ];
    }

    /**
     * Create a record for a specific financial statement.
     */
    public function forFinancialStatement(FinancialStatement $financialStatement): static
    {
        return $this->state([
            'financial_statement_id' => $financialStatement->id,
        ]);
    }

    /**
     * Create a record for a specific order.
     */
    public function forOrder(Order $order): static
    {
        return $this->state([
            'order_id' => $order->id,
        ]);
    }

    /**
     * Create a record with specific financial statement and order IDs.
     */
    public function forStatementAndOrder(int $financialStatementId, int $orderId): static
    {
        return $this->state([
            'financial_statement_id' => $financialStatementId,
            'order_id' => $orderId,
        ]);
    }
}
