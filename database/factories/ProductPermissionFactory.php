<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductPermission>
 */
final class ProductPermissionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductPermission::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'product_id' => Product::factory(),
            'permission_type' => $this->faker->randomElement(['view-reports', 'edit-reports', 'export-reports']),
            'granted_at' => now(),
            'expires_at' => $this->faker->optional(0.3)->dateTimeBetween('now', '+1 year'), // 30% chance of having expiration
            'granted_by' => User::factory(),
            'notes' => $this->faker->optional(0.5)->sentence(), // 50% chance of having notes
        ];
    }

    /**
     * Indicate that the permission is for view-reports.
     */
    public function viewReports(): static
    {
        return $this->state(fn (array $attributes) => [
            'permission_type' => 'view-reports',
        ]);
    }

    /**
     * Indicate that the permission is for edit-reports.
     */
    public function editReports(): static
    {
        return $this->state(fn (array $attributes) => [
            'permission_type' => 'edit-reports',
        ]);
    }

    /**
     * Indicate that the permission is for export-reports.
     */
    public function exportReports(): static
    {
        return $this->state(fn (array $attributes) => [
            'permission_type' => 'export-reports',
        ]);
    }

    /**
     * Indicate that the permission never expires.
     */
    public function neverExpires(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => null,
        ]);
    }

    /**
     * Indicate that the permission expires in the future.
     */
    public function expiresIn(string $period): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => now()->add($period),
        ]);
    }

    /**
     * Indicate that the permission has already expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('-1 year', '-1 day'),
        ]);
    }

    /**
     * Indicate that the permission has notes.
     */
    public function withNotes(string $notes): static
    {
        return $this->state(fn (array $attributes) => [
            'notes' => $notes,
        ]);
    }

    /**
     * Indicate that the permission was granted by a specific user.
     */
    public function grantedBy(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'granted_by' => $user->id,
        ]);
    }
}
