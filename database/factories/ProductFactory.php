<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
final class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'store_variant_id' => $this->faker->unique()->numberBetween(1000, 9999),
            'owner_id' => 'TEST001',
            'sku' => $this->faker->unique()->regexify('[A-Z0-9]{6}'),
            'code' => $this->faker->unique()->regexify('[A-Z0-9]{8}'),
            'name' => $this->faker->words(3, true),
            'enabled' => true,
            'slug' => $this->faker->slug(),
            'release_date' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'package' => $this->faker->randomElement(['Standard', 'Premium', 'Deluxe', 'Basic']),
            'current_price' => $this->faker->numberBetween(1000, 10000), // Price in cents
            'original_price' => $this->faker->numberBetween(1000, 10000),
            'minimum_price' => $this->faker->numberBetween(500, 5000),
            'lowest_price_before_discount' => $this->faker->numberBetween(800, 8000),
            'price_history' => [],
            'store_product_updated_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'store_variant_updated_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Indicate that the product is disabled.
     */
    public function disabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'enabled' => false,
        ]);
    }

    /**
     * Indicate that the product has a specific owner.
     */
    public function ownedBy(string $ownerId): static
    {
        return $this->state(fn (array $attributes) => [
            'owner_id' => $ownerId,
        ]);
    }

    /**
     * Indicate that the product has a specific store variant ID.
     */
    public function withStoreVariantId(int $storeVariantId): static
    {
        return $this->state(fn (array $attributes) => [
            'store_variant_id' => $storeVariantId,
        ]);
    }
}
