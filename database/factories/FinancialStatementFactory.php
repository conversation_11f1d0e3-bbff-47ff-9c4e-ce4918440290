<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\FinancialStatement;
use App\Models\Organisation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FinancialStatement>
 */
final class FinancialStatementFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<\Illuminate\Database\Eloquent\Model>
     */
    protected $model = FinancialStatement::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $reportType = fake()->randomElement(FinancialStatement::REPORT_TYPES);
        $dates = $this->generateDateRange($reportType);

        return [
            'report_type' => $reportType,
            'report_title' => fake()->company() . ' ' . $dates['period_description'] . ' 销售报表',
            'start_date' => $dates['start_date'],
            'end_date' => $dates['end_date'],
            'report_code' => fake()->unique()->regexify('[A-Z]{3}[0-9]{3}[0-9]{6}[mqyc]'),
            'organisation_id' => Organisation::factory(),
            'total_amount' => fake()->numberBetween(100000, 10000000), // 1000 to 100000 in currency
            'total_quantity' => fake()->numberBetween(50, 5000),
            'total_orders' => fake()->numberBetween(10, 500),
            'status' => fake()->randomElement(FinancialStatement::REPORT_STATUSES),
            'remarks' => fake()->optional(0.3)->sentence(),
        ];
    }

    /**
     * Generate date range based on report type.
     *
     * @param string $reportType
     * @return array<string, mixed>
     */
    private function generateDateRange(string $reportType): array
    {
        $baseDate = fake()->dateTimeBetween('-2 years', '-1 month');
        $carbon = Carbon::instance($baseDate);

        return match ($reportType) {
            FinancialStatement::TYPE_MONTHLY => $this->generateMonthlyRange($carbon),
            FinancialStatement::TYPE_QUARTERLY => $this->generateQuarterlyRange($carbon),
            FinancialStatement::TYPE_YEARLY => $this->generateYearlyRange($carbon),
            FinancialStatement::TYPE_CUSTOM => $this->generateCustomRange($carbon),
            default => $this->generateCustomRange($carbon),
        };
    }

    /**
     * Generate monthly date range.
     *
     * @param Carbon $date
     * @return array<string, mixed>
     */
    private function generateMonthlyRange(Carbon $date): array
    {
        $startDate = $date->copy()->startOfMonth();
        $endDate = $date->copy()->endOfMonth();

        return [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'period_description' => $startDate->format('Y年m月'),
        ];
    }

    /**
     * Generate quarterly date range.
     *
     * @param Carbon $date
     * @return array<string, mixed>
     */
    private function generateQuarterlyRange(Carbon $date): array
    {
        $startDate = $date->copy()->firstOfQuarter();
        $endDate = $date->copy()->lastOfQuarter();

        return [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'period_description' => $startDate->format('Y年') . '第' . $startDate->quarter . '季度',
        ];
    }

    /**
     * Generate yearly date range.
     *
     * @param Carbon $date
     * @return array<string, mixed>
     */
    private function generateYearlyRange(Carbon $date): array
    {
        $startDate = $date->copy()->startOfYear();
        $endDate = $date->copy()->endOfYear();

        return [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'period_description' => $startDate->format('Y年'),
        ];
    }

    /**
     * Generate custom date range.
     *
     * @param Carbon $date
     * @return array<string, mixed>
     */
    private function generateCustomRange(Carbon $date): array
    {
        $startDate = $date->copy();
        $endDate = $startDate->copy()->addDays(fake()->numberBetween(7, 90));

        return [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'period_description' => $startDate->format('Y年m月d日') . ' - ' . $endDate->format('Y年m月d日'),
        ];
    }

    /**
     * Create a monthly report.
     */
    public function monthly(): static
    {
        return $this->state(function (array $attributes) {
            $dates = $this->generateDateRange(FinancialStatement::TYPE_MONTHLY);
            return [
                'report_type' => FinancialStatement::TYPE_MONTHLY,
                'start_date' => $dates['start_date'],
                'end_date' => $dates['end_date'],
            ];
        });
    }

    /**
     * Create a quarterly report.
     */
    public function quarterly(): static
    {
        return $this->state(function (array $attributes) {
            $dates = $this->generateDateRange(FinancialStatement::TYPE_QUARTERLY);
            return [
                'report_type' => FinancialStatement::TYPE_QUARTERLY,
                'start_date' => $dates['start_date'],
                'end_date' => $dates['end_date'],
            ];
        });
    }

    /**
     * Create a yearly report.
     */
    public function yearly(): static
    {
        return $this->state(function (array $attributes) {
            $dates = $this->generateDateRange(FinancialStatement::TYPE_YEARLY);
            return [
                'report_type' => FinancialStatement::TYPE_YEARLY,
                'start_date' => $dates['start_date'],
                'end_date' => $dates['end_date'],
            ];
        });
    }

    /**
     * Create a custom report.
     */
    public function custom(): static
    {
        return $this->state(function (array $attributes) {
            $dates = $this->generateDateRange(FinancialStatement::TYPE_CUSTOM);
            return [
                'report_type' => FinancialStatement::TYPE_CUSTOM,
                'start_date' => $dates['start_date'],
                'end_date' => $dates['end_date'],
            ];
        });
    }

    /**
     * Create a report with high values.
     */
    public function highValue(): static
    {
        return $this->state([
            'total_amount' => fake()->numberBetween(5000000, 50000000), // 50k to 500k in currency
            'total_quantity' => fake()->numberBetween(2000, 20000),
            'total_orders' => fake()->numberBetween(200, 2000),
        ]);
    }

    /**
     * Create a report with low values.
     */
    public function lowValue(): static
    {
        return $this->state([
            'total_amount' => fake()->numberBetween(10000, 500000), // 100 to 5k in currency
            'total_quantity' => fake()->numberBetween(10, 200),
            'total_orders' => fake()->numberBetween(5, 50),
        ]);
    }

    /**
     * Create a report with pending audit status.
     */
    public function pendingAudit(): static
    {
        return $this->state([
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);
    }

    /**
     * Create a report with published status.
     */
    public function published(): static
    {
        return $this->state([
            'status' => FinancialStatement::STATUS_PUBLISHED,
        ]);
    }
}
