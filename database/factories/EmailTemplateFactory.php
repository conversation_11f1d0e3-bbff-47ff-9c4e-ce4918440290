<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\EmailTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EmailTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $templateTypes = [
            'welcome' => [
                'name' => 'Welcome Email',
                'subject' => 'Welcome to {app_name}, {user_name}!',
                'content' => '<h1>Welcome {user_name}!</h1><p>Thank you for joining {app_name}. We are excited to have you on board.</p>',
                'variables' => ['user_name', 'app_name'],
            ],
            'verification' => [
                'name' => 'Email Verification',
                'subject' => 'Verify your email address',
                'content' => '<h1>Email Verification</h1><p>Hello {user_name},</p><p>Your verification code is: <strong>{verification_code}</strong></p><p>This code will expire in {expiry_time}.</p>',
                'variables' => ['user_name', 'verification_code', 'expiry_time'],
            ],
            'password_reset' => [
                'name' => 'Password Reset',
                'subject' => 'Password Reset Request',
                'content' => '<h1>Password Reset</h1><p>Hello {user_name},</p><p>Click the link below to reset your password:</p><p><a href="{reset_link}">Reset Password</a></p><p>This link will expire in {expiry_time}.</p>',
                'variables' => ['user_name', 'reset_link', 'expiry_time'],
            ],
        ];

        $type = $this->faker->randomElement(array_keys($templateTypes));
        $template = $templateTypes[$type];

        return [
            'name' => $template['name'],
            'code' => $type . '_' . $this->faker->unique()->numberBetween(1000, 9999),
            'subject' => $template['subject'],
            'content' => $template['content'],
            'variables' => $template['variables'],
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'description' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the template is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a user verification template.
     */
    public function userVerification(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'User Email Verification',
            'code' => 'user_email_verification',
            'subject' => 'Email Verification Code',
            'content' => '<h1>Email Verification</h1><p>Hello {user_name},</p><p>Your verification code is: <strong>{verification_code}</strong></p><p>This code will expire in {expiry_time}.</p>',
            'variables' => ['user_name', 'verification_code', 'expiry_time'],
            'is_active' => true,
            'description' => 'Template for user email verification during registration',
        ]);
    }

    /**
     * Create an organization invitation template.
     */
    public function organizationInvitation(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Organization Invitation',
            'code' => 'organization_invitation',
            'subject' => 'Invitation to Join {organization_name}',
            'content' => '<h1>You are invited!</h1><p>Hello {user_name},</p><p>{inviter_name} has invited you to join <strong>{organization_name}</strong>.</p><p><a href="{invitation_link}">Accept Invitation</a></p><p>This invitation will expire on {expiry_date}.</p>',
            'variables' => ['user_name', 'organization_name', 'inviter_name', 'invitation_link', 'expiry_date'],
            'is_active' => true,
            'description' => 'Template for organization invitation emails',
        ]);
    }

    /**
     * Create a password reset template.
     */
    public function passwordReset(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Password Reset',
            'code' => 'password_reset',
            'subject' => 'Password Reset Request',
            'content' => '<h1>Password Reset</h1><p>Hello {user_name},</p><p>Click the link below to reset your password:</p><p><a href="{reset_link}">Reset Password</a></p><p>This link will expire in {expiry_time}.</p>',
            'variables' => ['user_name', 'reset_link', 'expiry_time'],
            'is_active' => true,
            'description' => 'Template for password reset emails',
        ]);
    }
}
