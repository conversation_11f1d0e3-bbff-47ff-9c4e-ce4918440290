<?php

declare(strict_types=1);

namespace Tests\Traits;

use Illuminate\Support\Facades\DB;

trait CreatesStoreTestTables
{
    /**
     * Create test store tables that mimic the Sylius database structure.
     */
    protected function createStoreTestTables(): void
    {
        // Create sylius_product_variant table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_product_variant (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_id INT NOT NULL,
                code VARCHAR(255) NOT NULL,
                enabled BOOLEAN NOT NULL DEFAULT TRUE,
                price INT NOT NULL DEFAULT 0,
                original_price INT NULL,
                package VARCHAR(255) NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_product table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_product (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(255) NOT NULL,
                owner_id INT NULL,
                sku VARCHAR(255) NULL,
                release_date DATETIME NULL,
                enabled BOOLEAN NOT NULL DEFAULT TRUE,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_product_variant_translation table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_product_variant_translation (
                id INT AUTO_INCREMENT PRIMARY KEY,
                translatable_id INT NOT NULL,
                locale VARCHAR(10) NOT NULL DEFAULT "en_US",
                name VARCHAR(255) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_product_translation table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_product_translation (
                id INT AUTO_INCREMENT PRIMARY KEY,
                translatable_id INT NOT NULL,
                locale VARCHAR(10) NOT NULL DEFAULT "en_US",
                name VARCHAR(255) NULL,
                slug VARCHAR(255) NULL,
                description TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_channel_pricing table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_channel_pricing (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_variant_id INT NOT NULL,
                price INT NULL,
                original_price INT NULL,
                minimum_price INT NULL,
                lowest_price_before_discount INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_channel_pricing_log_entry table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_channel_pricing_log_entry (
                id INT AUTO_INCREMENT PRIMARY KEY,
                channel_pricing_id INT NOT NULL,
                price INT NULL,
                original_price INT NULL,
                logged_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_product_image table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_product_image (
                id INT AUTO_INCREMENT PRIMARY KEY,
                owner_id INT NOT NULL,
                type VARCHAR(255) NULL,
                path VARCHAR(255) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
        
        // Create order-related tables for OrderSyncService tests
        $this->createOrderTestTables();
    }

    /**
     * Create order-related test tables.
     */
    protected function createOrderTestTables(): void
    {
        // Create sylius_order table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_order (
                id INT AUTO_INCREMENT PRIMARY KEY,
                number VARCHAR(255) NULL,
                state VARCHAR(50) NOT NULL,
                checkout_completed_at DATETIME NULL,
                items_total INT NOT NULL DEFAULT 0,
                adjustments_total INT NOT NULL DEFAULT 0,
                total INT NOT NULL DEFAULT 0,
                currency_code VARCHAR(3) NOT NULL DEFAULT "USD",
                payment_state VARCHAR(50) NULL,
                customer_id INT NULL,
                shipping_address_id INT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_order_item table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_order_item (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                variant_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                unit_price INT NOT NULL DEFAULT 0,
                units_total INT NOT NULL DEFAULT 0,
                adjustments_total INT NOT NULL DEFAULT 0,
                total INT NOT NULL DEFAULT 0,
                product_name VARCHAR(255) NULL,
                variant_name VARCHAR(255) NULL,
                quantity_refunded INT NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
        
        // Create sylius_address table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS sylius_address (
                id INT AUTO_INCREMENT PRIMARY KEY,
                country_code VARCHAR(10) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
        
        // Create bitbag_refund_credit_memo table
        DB::connection('store')->statement('
            CREATE TABLE IF NOT EXISTS bitbag_refund_credit_memo (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                total INT NOT NULL DEFAULT 0,
                comment TEXT NULL,
                status VARCHAR(50) NOT NULL DEFAULT "pending",
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ');
    }

    /**
     * Clean up test store data between tests.
     */
    protected function cleanupStoreTestData(): void
    {
        // Clean up in reverse order of dependencies, but only if tables exist
        try {
            DB::connection('store')->table('sylius_product_image')->delete();
            DB::connection('store')->table('sylius_channel_pricing_log_entry')->delete();
            DB::connection('store')->table('sylius_channel_pricing')->delete();
            DB::connection('store')->table('sylius_product_variant_translation')->delete();
            DB::connection('store')->table('sylius_product_translation')->delete();
            DB::connection('store')->table('sylius_product_variant')->delete();
            DB::connection('store')->table('sylius_product')->delete();
            
            // Clean order tables
            DB::connection('store')->table('bitbag_refund_credit_memo')->delete();
            DB::connection('store')->table('sylius_order_item')->delete();
            DB::connection('store')->table('sylius_order')->delete();
            DB::connection('store')->table('sylius_address')->delete();
        } catch (\Exception $e) {
            // Tables might not exist yet, ignore errors
        }
    }
}
