<?php

declare(strict_types=1);

namespace Tests\Unit\Security;

use App\Models\Organisation;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Security test to verify that permission caching doesn't create security vulnerabilities
 * when users switch accounts or when different users access the system.
 */
class PermissionCacheSecurityTest extends TestCase
{
    use RefreshDatabase;

    private User $systemAdminUser;
    private User $regularUser;
    private Organisation $organisation;
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organization
        $this->organisation = Organisation::factory()->create([
            'code' => 'TEST001',
            'name' => 'Test Organization'
        ]);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $rootRole = collect($systemRoles)->firstWhere('name', 'root');

        // Create organization roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $memberRole = collect($orgRoles)->firstWhere('name', 'member');

        // Create system admin user
        $this->systemAdminUser = User::factory()->create(['email' => '<EMAIL>']);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($rootRole);

        // Create regular user
        $this->regularUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->regularUser->organisations()->attach($this->organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->regularUser->assignRole($memberRole);
    }

    /**
     * Test that different User instances have isolated caches
     */
    public function test_different_user_instances_have_isolated_caches(): void
    {
        // Create two separate instances of the same user
        $userInstance1 = User::find($this->systemAdminUser->id);
        $userInstance2 = User::find($this->systemAdminUser->id);

        // Verify they are different object instances
        $this->assertNotSame($userInstance1, $userInstance2);

        // Call hasSystemAdminAccess on first instance (should cache the result)
        $result1 = $userInstance1->hasSystemAdminAccess();
        $this->assertTrue($result1);

        // Call hasSystemAdminAccess on second instance (should have its own cache)
        $result2 = $userInstance2->hasSystemAdminAccess();
        $this->assertTrue($result2);

        // Clear cache on first instance
        $userInstance1->clearSystemAdminAccessCache();

        // Verify that clearing cache on first instance doesn't affect second instance
        // This test verifies that caches are instance-specific, not global
        $result1AfterClear = $userInstance1->hasSystemAdminAccess();
        $result2AfterClear = $userInstance2->hasSystemAdminAccess();

        $this->assertTrue($result1AfterClear);
        $this->assertTrue($result2AfterClear);
    }

    /**
     * Test that admin user cache doesn't leak to regular user
     */
    public function test_admin_cache_does_not_leak_to_regular_user(): void
    {
        // Admin user checks their permissions (should be cached as true)
        $adminResult = $this->systemAdminUser->hasSystemAdminAccess();
        $this->assertTrue($adminResult);

        // Regular user checks their permissions (should be false, not affected by admin cache)
        $regularResult = $this->regularUser->hasSystemAdminAccess();
        $this->assertFalse($regularResult);

        // Verify admin still has cached result
        $adminResultAgain = $this->systemAdminUser->hasSystemAdminAccess();
        $this->assertTrue($adminResultAgain);

        // Verify regular user still gets correct result
        $regularResultAgain = $this->regularUser->hasSystemAdminAccess();
        $this->assertFalse($regularResultAgain);
    }

    /**
     * Test that accessible product IDs are user-specific
     */
    public function test_accessible_product_ids_are_user_specific(): void
    {
        // Admin user gets their accessible product IDs
        $adminProductIds = $this->systemAdminUser->getAccessibleProductIds($this->organisation->id);

        // Regular user gets their accessible product IDs
        $regularProductIds = $this->regularUser->getAccessibleProductIds($this->organisation->id);

        // Both should get the same product IDs for this organization (since both have access)
        // But they should be calculated independently
        $this->assertEquals($adminProductIds, $regularProductIds);

        // Clear admin cache
        $this->systemAdminUser->clearAccessibleProductIdsCache();

        // Regular user should still have their cached result
        $regularProductIdsAgain = $this->regularUser->getAccessibleProductIds($this->organisation->id);
        $this->assertEquals($regularProductIds, $regularProductIdsAgain);
    }

    /**
     * Test that cache doesn't persist across different HTTP requests (simulated)
     */
    public function test_cache_does_not_persist_across_requests(): void
    {
        // Simulate first request - user checks permissions
        $user1 = User::find($this->systemAdminUser->id);
        $result1 = $user1->hasSystemAdminAccess();
        $this->assertTrue($result1);

        // Simulate second request - new User instance is created
        $user2 = User::find($this->systemAdminUser->id);
        
        // Verify this is a new instance (simulating new HTTP request)
        $this->assertNotSame($user1, $user2);

        // The new instance should not have any cached data
        // We can't directly test this, but we can verify the behavior is correct
        $result2 = $user2->hasSystemAdminAccess();
        $this->assertTrue($result2);

        // Both instances should give the same result but have independent caches
        $this->assertEquals($result1, $result2);
    }

    /**
     * Test that role changes are not affected by cache (when using new instance)
     */
    public function test_role_changes_work_with_new_instances(): void
    {
        // Check initial permissions
        $initialResult = $this->regularUser->hasSystemAdminAccess();
        $this->assertFalse($initialResult);

        // Simulate role change (in real scenario, this would happen in a different request)
        $systemRoles = $this->permissionService->createSystemRoles();
        $adminRole = collect($systemRoles)->firstWhere('name', 'admin');
        
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->regularUser->guard_name = 'system';
        $this->regularUser->assignRole($adminRole);

        // Create new instance (simulating new HTTP request after role change)
        $updatedUser = User::find($this->regularUser->id);
        
        // New instance should reflect the role change
        $updatedResult = $updatedUser->hasSystemAdminAccess();
        $this->assertTrue($updatedResult);
    }

    /**
     * Test that organization-specific caching works correctly
     */
    public function test_organization_specific_caching_security(): void
    {
        // Create another organization
        $org2 = Organisation::factory()->create(['code' => 'TEST002']);

        // Get product IDs for first organization
        $org1ProductIds = $this->systemAdminUser->getAccessibleProductIds($this->organisation->id);

        // Get product IDs for second organization
        $org2ProductIds = $this->systemAdminUser->getAccessibleProductIds($org2->id);

        // Results should be organization-specific
        // (They might be the same for system admin, but calculated independently)
        $this->assertIsArray($org1ProductIds);
        $this->assertIsArray($org2ProductIds);

        // Clear cache and verify organization-specific caching
        $this->systemAdminUser->clearAccessibleProductIdsCache();

        // Get product IDs again for first organization
        $org1ProductIdsAgain = $this->systemAdminUser->getAccessibleProductIds($this->organisation->id);
        $this->assertEquals($org1ProductIds, $org1ProductIdsAgain);
    }
}
