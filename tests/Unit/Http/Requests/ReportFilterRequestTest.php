<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Requests;

use App\Http\Requests\Api\V1\ReportFilterRequest;
use App\Models\Organisation;
use App\Models\Product;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Test ReportFilterRequest validation logic, especially product access permissions
 */
final class ReportFilterRequestTest extends TestCase
{
    use RefreshDatabase;

    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $unauthorizedUser;
    private Organisation $organisation1;
    private Organisation $organisation2;
    private Product $org1Product1;
    private Product $org1Product2;
    private Product $org2Product1;
    private Role $ownerRole;
    private Role $memberRole;

    protected function setUp(): void
    {
        parent::setUp();

        // Create system admin user
        $this->systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $systemAdminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdminUser->roles()->attach($systemAdminRole);

        // Create organisations
        $this->organisation1 = Organisation::factory()->create([
            'code' => 'ORG001',
            'name' => 'Organisation 1',
        ]);
        $this->organisation2 = Organisation::factory()->create([
            'code' => 'ORG002',
            'name' => 'Organisation 2',
        ]);

        // Create products
        $this->org1Product1 = Product::create([
            'store_variant_id' => 1001,
            'owner_id' => 'ORG001',
            'sku' => 'ORG1-001',
            'code' => 'org1-product-1',
            'name' => 'Org1 Product 1',
            'slug' => 'org1-product-1',
            'enabled' => true,
            'current_price' => 1000,
        ]);

        $this->org1Product2 = Product::create([
            'store_variant_id' => 1002,
            'owner_id' => 'ORG001',
            'sku' => 'ORG1-002',
            'code' => 'org1-product-2',
            'name' => 'Org1 Product 2',
            'slug' => 'org1-product-2',
            'enabled' => true,
            'current_price' => 2000,
        ]);

        $this->org2Product1 = Product::create([
            'store_variant_id' => 2001,
            'owner_id' => 'ORG002',
            'sku' => 'ORG2-001',
            'code' => 'org2-product-1',
            'name' => 'Org2 Product 1',
            'slug' => 'org2-product-1',
            'enabled' => true,
            'current_price' => 3000,
        ]);

        // Create roles
        $this->ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation1->id,
        ]);

        $this->memberRole = Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation1->id,
        ]);

        // Create users
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->ownerUser->organisations()->attach($this->organisation1->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation1->id);
        $this->ownerUser->assignRole($this->ownerRole);

        $this->memberUser = User::factory()->create(['name' => 'Member User']);
        $this->memberUser->organisations()->attach($this->organisation1->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation1->id);
        $this->memberUser->assignRole($this->memberRole);

        $this->unauthorizedUser = User::factory()->create(['name' => 'Unauthorized User']);
        // This user doesn't belong to any organisation
    }

    public function test_validation_passes_without_product_id(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            // No product_id - should be valid
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());
    }

    /**
     * Test that non-existent product IDs are handled correctly
     */
    public function test_non_existent_product_id_fails_basic_validation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            'product_id' => 99999, // Non-existent product ID
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('product_id'));
        // This should fail at the basic 'exists:products,id' rule level
    }

    /**
     * Test group_by parameter validation with valid values
     */
    public function test_group_by_validation_accepts_valid_values(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $validGroupByValues = ['hour', 'day', 'week', 'month', 'quarter', 'year'];

        foreach ($validGroupByValues as $groupBy) {
            $request = new ReportFilterRequest();
            $request->replace([
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
                'organisation_id' => $this->organisation1->id,
                'group_by' => $groupBy,
            ]);
            $request->setUserResolver(fn() => $this->ownerUser);

            $validator = Validator::make($request->all(), $request->rules());
            $request->withValidator($validator);

            $this->assertTrue($validator->passes(), "group_by value '{$groupBy}' should be valid");
        }
    }

    /**
     * Test group_by parameter validation with invalid values
     */
    public function test_group_by_validation_rejects_invalid_values(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $invalidGroupByValues = ['minute', 'second', 'invalid', 'daily', 'weekly'];

        foreach ($invalidGroupByValues as $groupBy) {
            $request = new ReportFilterRequest();
            $request->replace([
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
                'organisation_id' => $this->organisation1->id,
                'group_by' => $groupBy,
            ]);
            $request->setUserResolver(fn() => $this->ownerUser);

            $validator = Validator::make($request->all(), $request->rules());
            $request->withValidator($validator);

            $this->assertFalse($validator->passes(), "group_by value '{$groupBy}' should be invalid");
            $this->assertTrue($validator->errors()->has('group_by'));
        }
    }

    /**
     * Test group_by parameter defaults to 'day' when not provided
     */
    public function test_group_by_defaults_to_day_when_not_provided(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation1->id,
            // No group_by parameter provided
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        // Call prepareForValidation to set defaults
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('prepareForValidation');
        $method->setAccessible(true);
        $method->invoke($request);

        $this->assertEquals('day', $request->input('group_by'));
    }

    /**
     * Test hour grouping format in processed data
     */
    public function test_hour_grouping_processed_data(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $request = new ReportFilterRequest();
        $request->replace([
            'start_date' => '2024-01-01 00:00:00',
            'end_date' => '2024-01-01 23:59:59',
            'organisation_id' => $this->organisation1->id,
            'group_by' => 'hour',
            'timezone' => 'UTC',
        ]);
        $request->setUserResolver(fn() => $this->ownerUser);

        $validator = Validator::make($request->all(), $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes());

        // Set the validator on the request so getProcessedData() can access validated data
        $reflection = new \ReflectionClass($request);
        $validatorProperty = $reflection->getProperty('validator');
        $validatorProperty->setAccessible(true);
        $validatorProperty->setValue($request, $validator);

        $processedData = $request->getProcessedData();
        $this->assertEquals('hour', $processedData['group_by']);
        $this->assertEquals('UTC', $processedData['timezone']);

        // Verify date formatting for hour grouping
        $this->assertStringContainsString('2024-01-01 00:00:00', $processedData['start_date']);
        $this->assertStringContainsString('2024-01-01 23:59:59', $processedData['end_date']);
    }
}
