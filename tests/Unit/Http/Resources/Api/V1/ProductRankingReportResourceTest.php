<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Resources\Api\V1;

use App\Http\Resources\Api\V1\ProductRankingReportResource;
use Tests\TestCase;

/**
 * Product Ranking Report Resource Test
 * 
 * Tests the ProductRankingReportResource transformation logic
 */
final class ProductRankingReportResourceTest extends TestCase
{
    /**
     * Test that field names match data variable names
     */
    public function test_field_names_match_data_variable_names(): void
    {
        $data = [
            'sales_rankings' => [
                [
                    'rank' => 1,
                    'store_variant_id' => 1001,
                    'product_name' => 'Test Product 1',
                    'product_slug' => 'test-product-1',
                    'product_package' => 'Premium',
                    'total_quantity' => 50,
                    'total_sales' => 149950,
                    'total_sales_formatted' => '1499.50',
                ],
            ],
            'quantity_rankings' => [
                [
                    'rank' => 1,
                    'store_variant_id' => 1002,
                    'product_name' => 'Test Product 2',
                    'product_slug' => 'test-product-2',
                    'product_package' => 'Standard',
                    'total_quantity' => 120,
                    'total_sales' => 119880,
                    'total_sales_formatted' => '1198.80',
                ],
            ],
            'total_products' => 2,
            'limit' => 10,
            'period' => ['start' => '2024-01-01', 'end' => '2024-01-31'],
        ];

        $resource = new ProductRankingReportResource($data);
        $result = $resource->toArray(request());

        // Verify that field names match data variable names
        $this->assertArrayHasKey('sales_rankings', $result);
        $this->assertArrayHasKey('quantity_rankings', $result);
        $this->assertArrayHasKey('sales_ranking_chart', $result);
        $this->assertArrayHasKey('quantity_ranking_chart', $result);
        $this->assertArrayHasKey('summary', $result);

        // Verify sales_rankings structure
        $this->assertCount(1, $result['sales_rankings']);
        $salesRanking = $result['sales_rankings'][0];
        $this->assertEquals(1, $salesRanking['rank']);
        $this->assertEquals(1001, $salesRanking['store_variant_id']);
        $this->assertEquals('Test Product 1', $salesRanking['product_name']);

        // Verify quantity_rankings structure
        $this->assertCount(1, $result['quantity_rankings']);
        $quantityRanking = $result['quantity_rankings'][0];
        $this->assertEquals(1, $quantityRanking['rank']);
        $this->assertEquals(1002, $quantityRanking['store_variant_id']);
        $this->assertEquals('Test Product 2', $quantityRanking['product_name']);

        // Verify summary
        $this->assertEquals(2, $result['summary']['total_products']);
        $this->assertEquals(10, $result['summary']['limit']);
        $this->assertEquals(['start' => '2024-01-01', 'end' => '2024-01-31'], $result['summary']['period']);
    }

    /**
     * Test chart data transformation
     */
    public function test_chart_data_transformation(): void
    {
        $data = [
            'sales_rankings' => [
                [
                    'rank' => 1,
                    'store_variant_id' => 1001,
                    'product_name' => 'Very Long Product Name That Should Be Truncated',
                    'product_slug' => 'long-product',
                    'product_package' => 'Premium',
                    'total_quantity' => 50,
                    'total_sales' => 149950, // $1499.50 in cents
                    'total_sales_formatted' => '1499.50',
                ],
                [
                    'rank' => 2,
                    'store_variant_id' => 1002,
                    'product_name' => 'Short Name',
                    'product_slug' => 'short',
                    'product_package' => 'Standard',
                    'total_quantity' => 120,
                    'total_sales' => 119880, // $1198.80 in cents
                    'total_sales_formatted' => '1198.80',
                ],
            ],
            'quantity_rankings' => [
                [
                    'rank' => 1,
                    'store_variant_id' => 1002,
                    'product_name' => 'Short Name',
                    'product_slug' => 'short',
                    'product_package' => 'Standard',
                    'total_quantity' => 120,
                    'total_sales' => 119880,
                    'total_sales_formatted' => '1198.80',
                ],
            ],
        ];

        $resource = new ProductRankingReportResource($data);
        $result = $resource->toArray(request());

        // Test sales ranking chart
        $salesChart = $result['sales_ranking_chart'];
        $this->assertArrayHasKey('xAxis', $salesChart);
        $this->assertArrayHasKey('series', $salesChart);
        
        $expectedProductNames = ['Very Long Product...', 'Short Name']; // First one should be truncated
        $this->assertEquals($expectedProductNames, $salesChart['xAxis']['data']);
        
        $expectedSalesAmounts = [1499, 1198]; // Converted from cents to dollars
        $this->assertEquals($expectedSalesAmounts, $salesChart['series'][0]['data']);

        // Test quantity ranking chart
        $quantityChart = $result['quantity_ranking_chart'];
        $this->assertArrayHasKey('xAxis', $quantityChart);
        $this->assertArrayHasKey('series', $quantityChart);
        
        $this->assertEquals(['Short Name'], $quantityChart['xAxis']['data']);
        $this->assertEquals([120], $quantityChart['series'][0]['data']);
    }

    /**
     * Test empty data handling
     */
    public function test_empty_data_handling(): void
    {
        $data = [
            'sales_rankings' => [],
            'quantity_rankings' => [],
            'total_products' => 0,
            'limit' => 10,
            'period' => [],
        ];

        $resource = new ProductRankingReportResource($data);
        $result = $resource->toArray(request());

        $this->assertEmpty($result['sales_rankings']);
        $this->assertEmpty($result['quantity_rankings']);
        $this->assertEmpty($result['sales_ranking_chart']['xAxis']['data']);
        $this->assertEmpty($result['quantity_ranking_chart']['xAxis']['data']);
        $this->assertEquals(0, $result['summary']['total_products']);
    }
}
