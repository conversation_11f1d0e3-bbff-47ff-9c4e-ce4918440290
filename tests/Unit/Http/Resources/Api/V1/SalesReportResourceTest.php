<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Resources\Api\V1;

use App\Http\Resources\Api\V1\SalesReportResource;
use Tests\TestCase;

/**
 * Sales Report Resource Test
 * 
 * Tests the SalesReportResource transformation logic, particularly the smart date formatting
 */
final class SalesReportResourceTest extends TestCase
{
    /**
     * Test single year data uses MM/DD format
     */
    public function test_single_year_data_uses_simple_format(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2023-01-01', 'total_sales' => 10000, 'order_count' => 5],
                ['period' => '2023-01-15', 'total_sales' => 15000, 'order_count' => 8],
                ['period' => '2023-02-01', 'total_sales' => 12000, 'order_count' => 6],
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['01/01', '01/15', '02/01'];
        $this->assertEquals($expectedDates, $result['daily_sales_chart']['xAxis']['data']);
        $this->assertEquals($expectedDates, $result['dual_axis_chart']['xAxis']['data']);
    }

    /**
     * Test multi-year data shows years for January 1st
     */
    public function test_multi_year_data_shows_years_for_january_first(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2020-01-01', 'total_sales' => 10000, 'order_count' => 5],
                ['period' => '2020-06-15', 'total_sales' => 15000, 'order_count' => 8],
                ['period' => '2021-01-01', 'total_sales' => 12000, 'order_count' => 6],
                ['period' => '2021-08-26', 'total_sales' => 18000, 'order_count' => 9],
                ['period' => '2022-01-01', 'total_sales' => 14000, 'order_count' => 7],
                ['period' => '2022-08-26', 'total_sales' => 16000, 'order_count' => 8],
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['2020', '06/15', '2021', '08/26', '2022', '08/26'];
        $this->assertEquals($expectedDates, $result['daily_sales_chart']['xAxis']['data']);
        $this->assertEquals($expectedDates, $result['dual_axis_chart']['xAxis']['data']);
    }

    /**
     * Test data conversion from cents to dollars
     */
    public function test_data_conversion_from_cents_to_dollars(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2023-01-01', 'total_sales' => 10000, 'order_count' => 5], // $100.00
                ['period' => '2023-01-02', 'total_sales' => 25050, 'order_count' => 3], // $250.50
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $expectedAmounts = [100, 250]; // Converted to dollars (integer)
        $this->assertEquals($expectedAmounts, $result['daily_sales_chart']['series'][0]['data']);
        $this->assertEquals($expectedAmounts, $result['dual_axis_chart']['series'][0]['data']);
    }

    /**
     * Test dual axis chart includes order counts
     */
    public function test_dual_axis_chart_includes_order_counts(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2023-01-01', 'total_sales' => 10000, 'order_count' => 5],
                ['period' => '2023-01-02', 'total_sales' => 15000, 'order_count' => 8],
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $expectedQuantities = [5, 8];
        $this->assertEquals($expectedQuantities, $result['dual_axis_chart']['series'][1]['data']);
        $this->assertNotEmpty($result['dual_axis_chart']['series'][1]['name']); // Translation key gets resolved
        $this->assertEquals(1, $result['dual_axis_chart']['series'][1]['yAxisIndex']);
    }

    /**
     * Test regional sales chart transformation with aggregation
     */
    public function test_regional_sales_chart_transformation(): void
    {
        $data = [
            'region_data' => [
                ['country' => 'US', 'total_sales' => 50000], // $500.00 -> North America
                ['country' => 'CA', 'total_sales' => 30000], // $300.00 -> North America
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $this->assertArrayHasKey('regional_sales_amount_chart', $result);
        // Should have 1 aggregated record for North America (US + CA)
        $this->assertCount(1, $result['regional_sales_amount_chart']['series'][0]['data']);

        $northAmericaData = $result['regional_sales_amount_chart']['series'][0]['data'][0];
        $this->assertEquals('North America', $northAmericaData['name']);
        $this->assertEquals(800, $northAmericaData['value']); // $500 + $300 = $800
    }

    /**
     * Test hourly data uses HH:MM format for single day
     */
    public function test_hourly_data_single_day_uses_time_format(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2024-01-01 08:00', 'total_sales' => 5000, 'order_count' => 2],
                ['period' => '2024-01-01 14:00', 'total_sales' => 7500, 'order_count' => 3],
                ['period' => '2024-01-01 18:00', 'total_sales' => 10000, 'order_count' => 4],
                ['period' => '2024-01-01 22:00', 'total_sales' => 2500, 'order_count' => 1],
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['08:00', '14:00', '18:00', '22:00'];
        $this->assertEquals($expectedDates, $result['daily_sales_chart']['xAxis']['data']);
        $this->assertEquals($expectedDates, $result['dual_axis_chart']['xAxis']['data']);

        $expectedAmounts = [50, 75, 100, 25]; // Converted from cents to dollars
        $this->assertEquals($expectedAmounts, $result['daily_sales_chart']['series'][0]['data']);
        $this->assertEquals($expectedAmounts, $result['dual_axis_chart']['series'][0]['data']);

        $expectedCounts = [2, 3, 4, 1];
        $this->assertEquals($expectedCounts, $result['dual_axis_chart']['series'][1]['data']);
    }

    /**
     * Test hourly data across multiple days shows dates for 00:00 hours
     */
    public function test_hourly_data_multiple_days_shows_dates_for_midnight(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2024-01-01 00:00', 'total_sales' => 2000, 'order_count' => 1],
                ['period' => '2024-01-01 08:00', 'total_sales' => 5000, 'order_count' => 2],
                ['period' => '2024-01-01 14:00', 'total_sales' => 7500, 'order_count' => 3],
                ['period' => '2024-01-02 00:00', 'total_sales' => 3000, 'order_count' => 2],
                ['period' => '2024-01-02 10:00', 'total_sales' => 6000, 'order_count' => 3],
                ['period' => '2024-01-03 00:00', 'total_sales' => 4000, 'order_count' => 2],
                ['period' => '2024-01-03 16:00', 'total_sales' => 8000, 'order_count' => 4],
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        // 00:00 hours should show dates, other hours should show time
        $expectedDates = ['01/01', '08:00', '14:00', '01/02', '10:00', '01/03', '16:00'];
        $this->assertEquals($expectedDates, $result['daily_sales_chart']['xAxis']['data']);
        $this->assertEquals($expectedDates, $result['dual_axis_chart']['xAxis']['data']);

        $expectedAmounts = [20, 50, 75, 30, 60, 40, 80]; // Converted from cents to dollars
        $this->assertEquals($expectedAmounts, $result['daily_sales_chart']['series'][0]['data']);
        $this->assertEquals($expectedAmounts, $result['dual_axis_chart']['series'][0]['data']);

        $expectedCounts = [1, 2, 3, 2, 3, 2, 4];
        $this->assertEquals($expectedCounts, $result['dual_axis_chart']['series'][1]['data']);
    }

    /**
     * Test empty chart data handling
     */
    public function test_empty_chart_data_handling(): void
    {
        $data = [
            'chart_data' => []
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $this->assertEquals([], $result['daily_sales_chart']['xAxis']['data']);
        $this->assertEquals([], $result['daily_sales_chart']['series'][0]['data']);
        $this->assertEquals([], $result['dual_axis_chart']['xAxis']['data']);
        $this->assertEquals([], $result['dual_axis_chart']['series'][0]['data']);
        $this->assertEquals([], $result['dual_axis_chart']['series'][1]['data']);
    }

    /**
     * Test chart data with zero values for missing dates
     */
    public function test_chart_data_with_zero_values_for_missing_dates(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2023-01-01', 'total_sales' => 0, 'order_count' => 0], // Zero values
                ['period' => '2023-01-02', 'total_sales' => 15000, 'order_count' => 3], // Actual data
                ['period' => '2023-01-03', 'total_sales' => 0, 'order_count' => 0], // Zero values
            ]
        ];

        $resource = new SalesReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['01/01', '01/02', '01/03'];
        $expectedAmounts = [0, 150, 0]; // Converted from cents to dollars
        $expectedCounts = [0, 3, 0];

        $this->assertEquals($expectedDates, $result['daily_sales_chart']['xAxis']['data']);
        $this->assertEquals($expectedAmounts, $result['daily_sales_chart']['series'][0]['data']);
        $this->assertEquals($expectedDates, $result['dual_axis_chart']['xAxis']['data']);
        $this->assertEquals($expectedAmounts, $result['dual_axis_chart']['series'][0]['data']);
        $this->assertEquals($expectedCounts, $result['dual_axis_chart']['series'][1]['data']);
    }
}
