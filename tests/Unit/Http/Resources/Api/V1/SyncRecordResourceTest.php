<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Resources\Api\V1;

use App\Http\Resources\Api\V1\SyncRecordResource;
use App\Models\SyncRecord;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 * Sync Record Resource Test
 *
 * Tests the SyncRecordResource transformation logic.
 *
 * Note: Access control is handled at the controller level via SyncPolicy,
 * so the resource always includes all data fields.
 */
final class SyncRecordResourceTest extends TestCase
{
    private User $testUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->testUser = User::factory()->create();
    }

    /**
     * Test that resource includes all data fields
     */
    public function test_resource_includes_all_data_fields(): void
    {
        $syncRecord = SyncRecord::create([
            'batch_id' => 'test-batch-123',
            'source_table' => 'test_table',
            'source_id' => '1',
            'target_table' => 'target_table',
            'target_id' => '1',
            'status' => 'success',
            'source_data' => ['sensitive' => 'data'],
            'transformed_data' => ['processed' => 'data'],
        ]);

        $request = Request::create('/test');
        $request->setUserResolver(fn() => $this->testUser);

        $resource = new SyncRecordResource($syncRecord);
        $result = $resource->toArray($request);

        // Verify all expected fields are present
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('batch_id', $result);
        $this->assertArrayHasKey('source_table', $result);
        $this->assertArrayHasKey('source_id', $result);
        $this->assertArrayHasKey('target_table', $result);
        $this->assertArrayHasKey('target_id', $result);
        $this->assertArrayHasKey('status', $result);
        $this->assertArrayHasKey('status_label', $result);
        $this->assertArrayHasKey('error_message', $result);
        $this->assertArrayHasKey('source_data', $result);
        $this->assertArrayHasKey('transformed_data', $result);
        $this->assertArrayHasKey('created_at', $result);
        $this->assertArrayHasKey('updated_at', $result);

        // Verify data content
        $this->assertEquals(['sensitive' => 'data'], $result['source_data']);
        $this->assertEquals(['processed' => 'data'], $result['transformed_data']);
        $this->assertEquals('test-batch-123', $result['batch_id']);
        $this->assertEquals('success', $result['status']);
    }

    /**
     * Test that resource transformation doesn't cause N+1 queries
     */
    public function test_resource_transformation_no_n_plus_one_queries(): void
    {
        // Create multiple sync records
        $syncRecords = [];
        for ($i = 1; $i <= 5; $i++) {
            $syncRecords[] = SyncRecord::create([
                'batch_id' => "test-batch-{$i}",
                'source_table' => 'test_table',
                'source_id' => (string) $i,
                'target_table' => 'target_table',
                'target_id' => (string) $i,
                'status' => 'success',
                'source_data' => ['test' => "data{$i}"],
                'transformed_data' => ['processed' => "data{$i}"],
            ]);
        }

        $request = Request::create('/test');
        $request->setUserResolver(fn() => $this->testUser);

        // Enable query logging
        DB::enableQueryLog();

        // Process all records through the resource
        $results = [];
        foreach ($syncRecords as $syncRecord) {
            $resource = new SyncRecordResource($syncRecord);
            $results[] = $resource->toArray($request);
        }

        $queries = DB::getQueryLog();
        DB::disableQueryLog();

        // Since we removed the permission check, there should be no additional queries
        // Only the initial queries from creating the records should exist
        $this->assertEmpty($queries, 'Resource transformation should not generate any additional queries');

        // Verify all records show all data fields
        foreach ($results as $result) {
            $this->assertArrayHasKey('source_data', $result);
            $this->assertArrayHasKey('transformed_data', $result);
        }
    }

    /**
     * Test that resource handles null data gracefully
     */
    public function test_resource_handles_null_data_gracefully(): void
    {
        $syncRecord = SyncRecord::create([
            'batch_id' => 'test-batch-null',
            'source_table' => 'test_table',
            'source_id' => '999',
            'target_table' => 'target_table',
            'target_id' => '999',
            'status' => 'failed',
            'source_data' => null,
            'transformed_data' => null,
            'error_message' => 'Test error',
        ]);

        $request = Request::create('/test');
        $request->setUserResolver(fn() => $this->testUser);

        $resource = new SyncRecordResource($syncRecord);
        $result = $resource->toArray($request);

        $this->assertArrayHasKey('source_data', $result);
        $this->assertArrayHasKey('transformed_data', $result);
        $this->assertNull($result['source_data']);
        $this->assertNull($result['transformed_data']);
        $this->assertEquals('Test error', $result['error_message']);
    }

    /**
     * Test that resource works with unauthenticated request
     */
    public function test_resource_works_with_unauthenticated_request(): void
    {
        $syncRecord = SyncRecord::create([
            'batch_id' => 'test-batch-unauth',
            'source_table' => 'test_table',
            'source_id' => '888',
            'target_table' => 'target_table',
            'target_id' => '888',
            'status' => 'success',
            'source_data' => ['sensitive' => 'data'],
            'transformed_data' => ['processed' => 'data'],
        ]);

        $request = Request::create('/test');
        $request->setUserResolver(fn() => null);

        $resource = new SyncRecordResource($syncRecord);
        $result = $resource->toArray($request);

        // All data should be included since access control is at controller level
        $this->assertArrayHasKey('source_data', $result);
        $this->assertArrayHasKey('transformed_data', $result);
        $this->assertEquals(['sensitive' => 'data'], $result['source_data']);
        $this->assertEquals(['processed' => 'data'], $result['transformed_data']);
    }
}
