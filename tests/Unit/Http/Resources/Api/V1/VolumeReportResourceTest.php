<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Resources\Api\V1;

use App\Http\Resources\Api\V1\VolumeReportResource;
use Tests\TestCase;

/**
 * Volume Report Resource Test
 * 
 * Tests the VolumeReportResource transformation logic, particularly the smart date formatting
 */
final class VolumeReportResourceTest extends TestCase
{
    /**
     * Test multi-year data shows years for January 1st
     */
    public function test_multi_year_data_shows_years_for_january_first(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2020-01-01', 'total_quantity' => 100],
                ['period' => '2020-06-15', 'total_quantity' => 150],
                ['period' => '2021-01-01', 'total_quantity' => 120],
                ['period' => '2021-08-26', 'total_quantity' => 180],
                ['period' => '2022-01-01', 'total_quantity' => 140],
                ['period' => '2022-08-26', 'total_quantity' => 160],
            ]
        ];

        $resource = new VolumeReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['2020', '06/15', '2021', '08/26', '2022', '08/26'];
        $this->assertEquals($expectedDates, $result['daily_quantity_chart']['xAxis']['data']);
    }

    /**
     * Test single year data uses MM/DD format
     */
    public function test_single_year_data_uses_simple_format(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2023-01-01', 'total_quantity' => 100],
                ['period' => '2023-01-15', 'total_quantity' => 150],
                ['period' => '2023-02-01', 'total_quantity' => 120],
            ]
        ];

        $resource = new VolumeReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['01/01', '01/15', '02/01'];
        $this->assertEquals($expectedDates, $result['daily_quantity_chart']['xAxis']['data']);
    }

    /**
     * Test hourly data uses HH:MM format for single day
     */
    public function test_hourly_data_single_day_uses_time_format(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2024-01-01 08:00', 'total_quantity' => 50],
                ['period' => '2024-01-01 14:00', 'total_quantity' => 75],
                ['period' => '2024-01-01 18:00', 'total_quantity' => 100],
                ['period' => '2024-01-01 22:00', 'total_quantity' => 25],
            ]
        ];

        $resource = new VolumeReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['08:00', '14:00', '18:00', '22:00'];
        $this->assertEquals($expectedDates, $result['daily_quantity_chart']['xAxis']['data']);

        $expectedQuantities = [50, 75, 100, 25];
        $this->assertEquals($expectedQuantities, $result['daily_quantity_chart']['series'][0]['data']);
    }

    /**
     * Test hourly data across multiple days shows dates for 00:00 hours
     */
    public function test_hourly_data_multiple_days_shows_dates_for_midnight(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2024-01-01 00:00', 'total_quantity' => 20],
                ['period' => '2024-01-01 08:00', 'total_quantity' => 50],
                ['period' => '2024-01-01 14:00', 'total_quantity' => 75],
                ['period' => '2024-01-02 00:00', 'total_quantity' => 30],
                ['period' => '2024-01-02 10:00', 'total_quantity' => 60],
                ['period' => '2024-01-03 00:00', 'total_quantity' => 40],
                ['period' => '2024-01-03 16:00', 'total_quantity' => 80],
            ]
        ];

        $resource = new VolumeReportResource($data);
        $result = $resource->toArray(request());

        // 00:00 hours should show dates, other hours should show time
        $expectedDates = ['01/01', '08:00', '14:00', '01/02', '10:00', '01/03', '16:00'];
        $this->assertEquals($expectedDates, $result['daily_quantity_chart']['xAxis']['data']);

        $expectedQuantities = [20, 50, 75, 30, 60, 40, 80];
        $this->assertEquals($expectedQuantities, $result['daily_quantity_chart']['series'][0]['data']);
    }
}
