<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Resources\Api\V1;

use App\Http\Resources\Api\V1\SalesReportResource;
use App\Http\Resources\Api\V1\VolumeReportResource;
use Illuminate\Http\Request;
use Tests\TestCase;

final class ReportResourceTest extends TestCase
{
    public function test_sales_report_resource_aggregates_regional_data_correctly(): void
    {
        // Mock data with multiple countries mapping to same regions
        $mockData = [
            'region_data' => [
                ['country' => 'US', 'total_sales' => 100000], // $1000 in cents -> North America
                ['country' => 'CA', 'total_sales' => 50000],  // $500 in cents -> North America  
                ['country' => 'MX', 'total_sales' => 25000],  // $250 in cents -> North America
                ['country' => 'GB', 'total_sales' => 80000],  // $800 in cents -> Europe
                ['country' => 'DE', 'total_sales' => 60000],  // $600 in cents -> Europe
                ['country' => 'FR', 'total_sales' => 40000],  // $400 in cents -> Europe
                ['country' => 'CN', 'total_sales' => 70000],  // $700 in cents -> Asia
                ['country' => 'JP', 'total_sales' => 30000],  // $300 in cents -> Asia
                ['country' => 'XX', 'total_sales' => 20000],  // $200 in cents -> Other (unknown country)
            ]
        ];

        $request = new Request();
        $resource = new SalesReportResource($mockData);
        $result = $resource->toArray($request);

        // Verify the structure
        $this->assertArrayHasKey('regional_sales_amount_chart', $result);
        $chartData = $result['regional_sales_amount_chart']['series'][0]['data'];

        // Extract region names and values
        $regions = array_column($chartData, 'name');
        $values = array_column($chartData, 'value');

        // Should have exactly 4 unique regions (no duplicates)
        $uniqueRegions = array_unique($regions);
        $this->assertCount(4, $uniqueRegions, 'Should have exactly 4 unique regions');
        $this->assertEquals(count($regions), count($uniqueRegions), 'No duplicate region names should exist');

        // Verify aggregated values
        $regionValues = array_combine($regions, $values);
        
        // North America: US($1000) + CA($500) + MX($250) = $1750
        $this->assertEquals(1750, $regionValues['North America']);
        
        // Europe: GB($800) + DE($600) + FR($400) = $1800  
        $this->assertEquals(1800, $regionValues['Europe']);
        
        // Asia: CN($700) + JP($300) = $1000
        $this->assertEquals(1000, $regionValues['Asia']);
        
        // Other: XX($200) = $200
        $this->assertEquals(200, $regionValues['Other']);
    }

    public function test_volume_report_resource_aggregates_regional_data_correctly(): void
    {
        // Mock data with multiple countries mapping to same regions
        $mockData = [
            'region_data' => [
                ['country' => 'US', 'total_quantity' => 100], // North America
                ['country' => 'CA', 'total_quantity' => 50],  // North America  
                ['country' => 'MX', 'total_quantity' => 25],  // North America
                ['country' => 'GB', 'total_quantity' => 80],  // Europe
                ['country' => 'DE', 'total_quantity' => 60],  // Europe
                ['country' => 'FR', 'total_quantity' => 40],  // Europe
                ['country' => 'CN', 'total_quantity' => 70],  // Asia
                ['country' => 'JP', 'total_quantity' => 30],  // Asia
                ['country' => 'XX', 'total_quantity' => 20],  // Other (unknown country)
            ]
        ];

        $request = new Request();
        $resource = new VolumeReportResource($mockData);
        $result = $resource->toArray($request);

        // Verify the structure
        $this->assertArrayHasKey('regional_sales_quantity_chart', $result);
        $chartData = $result['regional_sales_quantity_chart']['series'][0]['data'];

        // Extract region names and values
        $regions = array_column($chartData, 'name');
        $values = array_column($chartData, 'value');

        // Should have exactly 4 unique regions (no duplicates)
        $uniqueRegions = array_unique($regions);
        $this->assertCount(4, $uniqueRegions, 'Should have exactly 4 unique regions');
        $this->assertEquals(count($regions), count($uniqueRegions), 'No duplicate region names should exist');

        // Verify aggregated values
        $regionValues = array_combine($regions, $values);
        
        // North America: US(100) + CA(50) + MX(25) = 175
        $this->assertEquals(175, $regionValues['North America']);
        
        // Europe: GB(80) + DE(60) + FR(40) = 180  
        $this->assertEquals(180, $regionValues['Europe']);
        
        // Asia: CN(70) + JP(30) = 100
        $this->assertEquals(100, $regionValues['Asia']);
        
        // Other: XX(20) = 20
        $this->assertEquals(20, $regionValues['Other']);
    }

    public function test_sales_report_resource_handles_empty_region_data(): void
    {
        $mockData = ['region_data' => []];

        $request = new Request();
        $resource = new SalesReportResource($mockData);
        $result = $resource->toArray($request);

        // Should handle empty data gracefully
        $this->assertArrayHasKey('regional_sales_amount_chart', $result);
        $chartData = $result['regional_sales_amount_chart']['series'][0]['data'];
        $this->assertEmpty($chartData);
    }

    public function test_volume_report_resource_handles_empty_region_data(): void
    {
        $mockData = ['region_data' => []];

        $request = new Request();
        $resource = new VolumeReportResource($mockData);
        $result = $resource->toArray($request);

        // Should handle empty data gracefully
        $this->assertArrayHasKey('regional_sales_quantity_chart', $result);
        $chartData = $result['regional_sales_quantity_chart']['series'][0]['data'];
        $this->assertEmpty($chartData);
    }

    public function test_sales_report_resource_handles_single_region(): void
    {
        $mockData = [
            'region_data' => [
                ['country' => 'US', 'total_sales' => 100000], // $1000 in cents
            ]
        ];

        $request = new Request();
        $resource = new SalesReportResource($mockData);
        $result = $resource->toArray($request);

        $chartData = $result['regional_sales_amount_chart']['series'][0]['data'];
        
        $this->assertCount(1, $chartData);
        $this->assertEquals('North America', $chartData[0]['name']);
        $this->assertEquals(1000, $chartData[0]['value']);
    }

    public function test_volume_report_resource_handles_single_region(): void
    {
        $mockData = [
            'region_data' => [
                ['country' => 'US', 'total_quantity' => 100],
            ]
        ];

        $request = new Request();
        $resource = new VolumeReportResource($mockData);
        $result = $resource->toArray($request);

        $chartData = $result['regional_sales_quantity_chart']['series'][0]['data'];
        
        $this->assertCount(1, $chartData);
        $this->assertEquals('North America', $chartData[0]['name']);
        $this->assertEquals(100, $chartData[0]['value']);
    }
}
