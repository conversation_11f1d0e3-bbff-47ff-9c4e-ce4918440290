<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Resources\Api\V1;

use App\Http\Resources\Api\V1\RefundReportResource;
use Tests\TestCase;

/**
 * Refund Report Resource Test
 * 
 * Tests the RefundReportResource transformation logic, particularly the smart date formatting
 */
final class RefundReportResourceTest extends TestCase
{
    /**
     * Test multi-year data shows years for January 1st
     */
    public function test_multi_year_data_shows_years_for_january_first(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2020-01-01', 'total_refunds' => 10000, 'refunded_orders' => 5],
                ['period' => '2020-06-15', 'total_refunds' => 15000, 'refunded_orders' => 8],
                ['period' => '2021-01-01', 'total_refunds' => 12000, 'refunded_orders' => 6],
                ['period' => '2021-08-26', 'total_refunds' => 18000, 'refunded_orders' => 9],
                ['period' => '2022-01-01', 'total_refunds' => 14000, 'refunded_orders' => 7],
                ['period' => '2022-08-26', 'total_refunds' => 16000, 'refunded_orders' => 8],
            ]
        ];

        $resource = new RefundReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['2020', '06/15', '2021', '08/26', '2022', '08/26'];
        $this->assertEquals($expectedDates, $result['refund_trend_chart']['xAxis']['data']);
    }

    /**
     * Test single year data uses MM/DD format
     */
    public function test_single_year_data_uses_simple_format(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2023-01-01', 'total_refunds' => 10000, 'refunded_orders' => 5],
                ['period' => '2023-01-15', 'total_refunds' => 15000, 'refunded_orders' => 8],
                ['period' => '2023-02-01', 'total_refunds' => 12000, 'refunded_orders' => 6],
            ]
        ];

        $resource = new RefundReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['01/01', '01/15', '02/01'];
        $this->assertEquals($expectedDates, $result['refund_trend_chart']['xAxis']['data']);
    }

    /**
     * Test hourly data uses HH:MM format
     */
    public function test_hourly_data_uses_time_format(): void
    {
        $data = [
            'chart_data' => [
                ['period' => '2024-01-01 08:00', 'total_refunds' => 5000, 'refunded_orders' => 2],
                ['period' => '2024-01-01 14:00', 'total_refunds' => 7500, 'refunded_orders' => 3],
                ['period' => '2024-01-01 18:00', 'total_refunds' => 10000, 'refunded_orders' => 4],
                ['period' => '2024-01-01 22:00', 'total_refunds' => 2500, 'refunded_orders' => 1],
            ]
        ];

        $resource = new RefundReportResource($data);
        $result = $resource->toArray(request());

        $expectedDates = ['08:00', '14:00', '18:00', '22:00'];
        $this->assertEquals($expectedDates, $result['refund_trend_chart']['xAxis']['data']);

        $expectedAmounts = [50, 75, 100, 25]; // Converted from cents to dollars
        $this->assertEquals($expectedAmounts, $result['refund_trend_chart']['series'][0]['data']);

        $expectedCounts = [2, 3, 4, 1];
        $this->assertEquals($expectedCounts, $result['refund_trend_chart']['series'][1]['data']);
    }
}
