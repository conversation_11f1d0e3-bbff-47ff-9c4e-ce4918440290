<?php

declare(strict_types=1);

namespace Tests\Unit\Jobs;

use App\Jobs\SendEmailJob;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

final class SendEmailJobTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    public function test_can_create_send_email_job(): void
    {
        $job = new SendEmailJob(
            '<EMAIL>',
            'Test Subject',
            '<p>Test Content</p>',
            '<PERSON>',
            'test_template',
            ['name' => 'John']
        );

        $this->assertEquals('<EMAIL>', $job->getToEmail());
        $this->assertEquals('Test Subject', $job->getSubject());
        $this->assertEquals('<p>Test Content</p>', $job->getContent());
        $this->assertEquals('<PERSON>', $job->getToName());
        $this->assertEquals('test_template', $job->getTemplateCode());
        $this->assertEquals(['name' => 'John'], $job->getVariables());
    }

    public function test_can_create_job_with_minimal_parameters(): void
    {
        $job = new SendEmailJob(
            '<EMAIL>',
            'Test Subject',
            '<p>Test Content</p>'
        );

        $this->assertEquals('<EMAIL>', $job->getToEmail());
        $this->assertEquals('Test Subject', $job->getSubject());
        $this->assertEquals('<p>Test Content</p>', $job->getContent());
        $this->assertNull($job->getToName());
        $this->assertNull($job->getTemplateCode());
        $this->assertEquals([], $job->getVariables());
    }

    public function test_job_has_correct_configuration(): void
    {
        $job = new SendEmailJob(
            '<EMAIL>',
            'Test Subject',
            '<p>Test Content</p>'
        );

        $this->assertEquals(3, $job->tries);
        $this->assertEquals(60, $job->timeout);
        $this->assertInstanceOf(\DateTime::class, $job->retryUntil());
    }

    public function test_handle_sends_email(): void
    {
        $job = new SendEmailJob(
            '<EMAIL>',
            'Test Subject',
            '<p>Test Content</p>',
            'John Doe'
        );

        // Test that handle method completes without throwing exception
        $this->expectNotToPerformAssertions();
        $job->handle();
    }

    public function test_handle_with_mail_exception_throws_exception(): void
    {
        // Mock Mail to throw exception
        Mail::shouldReceive('html')->andThrow(new \Exception('SMTP connection failed'));

        $job = new SendEmailJob(
            '<EMAIL>',
            'Test Subject',
            '<p>Test Content</p>'
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('SMTP connection failed');

        $job->handle();
    }
}
