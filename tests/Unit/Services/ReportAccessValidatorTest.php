<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\Role;
use App\Models\User;
use App\Services\OrganisationService;
use App\Services\ReportAccessValidator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Test ReportAccessValidator service for access permission validation logic
 */
final class ReportAccessValidatorTest extends TestCase
{
    use RefreshDatabase;

    private ReportAccessValidator $validator;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $unauthorizedUser;
    private Organisation $organisation1;
    private Organisation $organisation2;
    private Product $org1Product1;
    private Product $org1Product2;
    private Product $org2Product1;
    private Role $ownerRole;
    private Role $memberRole;

    protected function setUp(): void
    {
        parent::setUp();

        $this->validator = new ReportAccessValidator(new OrganisationService());

        // Create test organisations
        $this->organisation1 = Organisation::factory()->create([
            'code' => 'ORG001',
            'name' => 'Test Organisation 1',
        ]);

        $this->organisation2 = Organisation::factory()->create([
            'code' => 'ORG002',
            'name' => 'Test Organisation 2',
        ]);

        // Create products for organisations
        $this->org1Product1 = Product::create([
            'store_variant_id' => 1001,
            'owner_id' => 'ORG001',
            'sku' => 'TEST-001',
            'code' => 'test-product-1',
            'name' => 'Test Product 1',
            'slug' => 'test-product-1',
            'enabled' => true,
            'current_price' => 1000,
        ]);

        $this->org1Product2 = Product::create([
            'store_variant_id' => 1002,
            'owner_id' => 'ORG001',
            'sku' => 'TEST-002',
            'code' => 'test-product-2',
            'name' => 'Test Product 2',
            'slug' => 'test-product-2',
            'enabled' => true,
            'current_price' => 2000,
        ]);

        $this->org2Product1 = Product::create([
            'store_variant_id' => 2001,
            'owner_id' => 'ORG002',
            'sku' => 'ORG2-001',
            'code' => 'org2-product-1',
            'name' => 'Org2 Product 1',
            'slug' => 'org2-product-1',
            'enabled' => true,
            'current_price' => 1500,
        ]);

        // Create roles
        $this->ownerRole = Role::create([
            'name' => 'Organisation Owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation1->id,
        ]);

        $this->memberRole = Role::create([
            'name' => 'Organisation Member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation1->id,
        ]);

        // Create users
        $this->systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $systemAdminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdminUser->roles()->attach($systemAdminRole);

        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->ownerUser->organisations()->attach($this->organisation1->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation1->id);
        $this->ownerUser->assignRole($this->ownerRole);

        $this->memberUser = User::factory()->create(['name' => 'Member User']);
        $this->memberUser->organisations()->attach($this->organisation1->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation1->id);
        $this->memberUser->assignRole($this->memberRole);

        $this->unauthorizedUser = User::factory()->create(['name' => 'Unauthorized User']);
    }

    public function test_system_admin_has_access_to_any_organisation(): void
    {
        $hasAccess = $this->validator->hasOrganisationAccess($this->systemAdminUser, $this->organisation1->id);
        $this->assertTrue($hasAccess);

        $hasAccess = $this->validator->hasOrganisationAccess($this->systemAdminUser, $this->organisation2->id);
        $this->assertTrue($hasAccess);
    }

    public function test_owner_has_access_to_own_organisation(): void
    {
        $hasAccess = $this->validator->hasOrganisationAccess($this->ownerUser, $this->organisation1->id);
        $this->assertTrue($hasAccess);
    }

    public function test_member_has_access_to_own_organisation(): void
    {
        $hasAccess = $this->validator->hasOrganisationAccess($this->memberUser, $this->organisation1->id);
        $this->assertTrue($hasAccess);
    }

    public function test_user_does_not_have_access_to_other_organisation(): void
    {
        $hasAccess = $this->validator->hasOrganisationAccess($this->ownerUser, $this->organisation2->id);
        $this->assertFalse($hasAccess);
    }

    public function test_unauthorized_user_does_not_have_access_to_any_organisation(): void
    {
        $hasAccess = $this->validator->hasOrganisationAccess($this->unauthorizedUser, $this->organisation1->id);
        $this->assertFalse($hasAccess);
    }

    public function test_null_user_does_not_have_access(): void
    {
        $hasAccess = $this->validator->hasOrganisationAccess(null, $this->organisation1->id);
        $this->assertFalse($hasAccess);
    }

    public function test_null_organisation_id_returns_false(): void
    {
        $hasAccess = $this->validator->hasOrganisationAccess($this->ownerUser, null);
        $this->assertFalse($hasAccess);
    }

    public function test_validate_organisation_access_passes_for_authorized_user(): void
    {
        $validator = Validator::make([], []);
        
        $this->validator->validateOrganisationAccess($validator, $this->ownerUser, $this->organisation1->id);
        
        $this->assertFalse($validator->errors()->has('organisation_id'));
    }

    public function test_validate_organisation_access_fails_for_unauthorized_user(): void
    {
        $validator = Validator::make([], []);
        
        $this->validator->validateOrganisationAccess($validator, $this->unauthorizedUser, $this->organisation1->id);
        
        $this->assertTrue($validator->errors()->has('organisation_id'));
    }

    public function test_validate_product_access_passes_for_authorized_user(): void
    {
        $validator = Validator::make([], []);
        
        $this->validator->validateProductAccess(
            $validator,
            $this->ownerUser,
            $this->organisation1->id,
            $this->org1Product1->id
        );
        
        $this->assertFalse($validator->errors()->has('product_id'));
    }

    public function test_validate_product_access_fails_for_unauthorized_product(): void
    {
        $validator = Validator::make([], []);
        
        $this->validator->validateProductAccess(
            $validator,
            $this->ownerUser,
            $this->organisation1->id,
            $this->org2Product1->id // Product from different org
        );
        
        $this->assertTrue($validator->errors()->has('product_id'));
    }

    public function test_validate_product_access_skips_validation_when_no_product_id(): void
    {
        $validator = Validator::make([], []);
        
        $this->validator->validateProductAccess($validator, $this->ownerUser, $this->organisation1->id, null);
        
        $this->assertFalse($validator->errors()->has('product_id'));
    }

    public function test_validate_product_access_skips_when_organisation_access_denied(): void
    {
        $validator = Validator::make([], []);
        
        // User doesn't have access to organisation2, so product validation should be skipped
        $this->validator->validateProductAccess(
            $validator,
            $this->ownerUser,
            $this->organisation2->id,
            $this->org2Product1->id
        );
        
        // Should not add product error since organisation access is the primary issue
        $this->assertFalse($validator->errors()->has('product_id'));
    }

    public function test_get_user_accessible_product_ids_returns_correct_ids_for_authorized_user(): void
    {
        $productIds = $this->validator->getUserAccessibleProductIds($this->ownerUser, $this->organisation1->id);

        // Should return primary key IDs, not store_variant_ids
        $expectedIds = [$this->org1Product1->id, $this->org1Product2->id];
        $this->assertEquals($expectedIds, $productIds);
    }

    public function test_get_user_accessible_product_ids_returns_empty_for_unauthorized_user(): void
    {
        $productIds = $this->validator->getUserAccessibleProductIds($this->unauthorizedUser, $this->organisation1->id);
        
        $this->assertEquals([], $productIds);
    }

    public function test_get_user_accessible_product_ids_returns_empty_for_null_organisation(): void
    {
        $productIds = $this->validator->getUserAccessibleProductIds($this->ownerUser, null);
        
        $this->assertEquals([], $productIds);
    }

    public function test_system_admin_can_access_any_organisation_products(): void
    {
        $productIds = $this->validator->getUserAccessibleProductIds($this->systemAdminUser, $this->organisation1->id);
        $expectedIds1 = [$this->org1Product1->id, $this->org1Product2->id];
        $this->assertEquals($expectedIds1, $productIds);

        $productIds = $this->validator->getUserAccessibleProductIds($this->systemAdminUser, $this->organisation2->id);
        $expectedIds2 = [$this->org2Product1->id];
        $this->assertEquals($expectedIds2, $productIds);
    }

    public function test_validate_report_access_validates_both_organisation_and_product(): void
    {
        $validator = Validator::make([], []);
        
        // Test successful validation
        $this->validator->validateReportAccess(
            $validator,
            $this->ownerUser,
            $this->organisation1->id,
            $this->org1Product1->id
        );
        
        $this->assertFalse($validator->errors()->has('organisation_id'));
        $this->assertFalse($validator->errors()->has('product_id'));
    }

    public function test_validate_report_access_fails_organisation_validation(): void
    {
        $validator = Validator::make([], []);
        
        $this->validator->validateReportAccess(
            $validator,
            $this->unauthorizedUser,
            $this->organisation1->id,
            $this->org1Product1->id
        );
        
        $this->assertTrue($validator->errors()->has('organisation_id'));
        // Product validation should be skipped when organisation access fails
        $this->assertFalse($validator->errors()->has('product_id'));
    }

    public function test_validate_report_access_without_product_id(): void
    {
        $validator = Validator::make([], []);
        
        $this->validator->validateReportAccess($validator, $this->ownerUser, $this->organisation1->id);
        
        $this->assertFalse($validator->errors()->has('organisation_id'));
        $this->assertFalse($validator->errors()->has('product_id'));
    }
}
