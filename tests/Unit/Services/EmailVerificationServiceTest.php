<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\EmailTemplate;
use App\Services\EmailVerificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

final class EmailVerificationServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailVerificationService $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Ensure the required email template exists (it should be created by migration)
        $template = EmailTemplate::where('code', 'user_email_verification')->first();
        if (!$template) {
            EmailTemplate::factory()->create([
                'code' => 'user_email_verification',
                'subject' => 'Email Verification Code',
                'content' => '<p>Hello {user_name}, your verification code is: {verification_code}. It expires at {expiry_time}.</p>',
                'is_active' => true,
            ]);
        }

        $this->service = app(EmailVerificationService::class);
        Cache::flush(); // Clean cache before each test
    }

    protected function tearDown(): void
    {
        Cache::flush(); // Clean cache after each test
        parent::tearDown();
    }

    public function test_can_send_verification_code(): void
    {
        $email = '<EMAIL>';

        $code = $this->service->sendVerificationCode($email);

        $this->assertIsString($code);
        $this->assertEquals(6, strlen($code));
        $this->assertMatchesRegularExpression('/^\d{6}$/', $code);

        // Verify code is cached
        $this->assertTrue($this->service->hasValidCode($email));
    }

    public function test_can_verify_correct_code(): void
    {
        $email = '<EMAIL>';
        $code = $this->service->sendVerificationCode($email);

        $result = $this->service->verifyCode($email, $code);

        $this->assertTrue($result);

        // Code should be removed after successful verification
        $this->assertFalse($this->service->hasValidCode($email));
    }

    public function test_cannot_verify_incorrect_code(): void
    {
        $email = '<EMAIL>';
        $this->service->sendVerificationCode($email);

        $result = $this->service->verifyCode($email, '999999');

        $this->assertFalse($result);

        // Code should still exist after failed verification
        $this->assertTrue($this->service->hasValidCode($email));
    }

    public function test_cannot_verify_non_existent_code(): void
    {
        $email = '<EMAIL>';

        $result = $this->service->verifyCode($email, '123456');

        $this->assertFalse($result);
    }

    public function test_has_valid_code_returns_false_for_non_existent_code(): void
    {
        $email = '<EMAIL>';

        $result = $this->service->hasValidCode($email);

        $this->assertFalse($result);
    }

    public function test_verification_codes_are_unique(): void
    {
        $email1 = '<EMAIL>';
        $email2 = '<EMAIL>';

        $code1 = $this->service->sendVerificationCode($email1);
        $code2 = $this->service->sendVerificationCode($email2);

        // While codes could theoretically be the same (1 in 1,000,000 chance),
        // in practice they should be different
        $this->assertNotEquals($code1, $code2);

        // Both should be valid
        $this->assertTrue($this->service->hasValidCode($email1));
        $this->assertTrue($this->service->hasValidCode($email2));
    }

    public function test_can_resend_code_initially(): void
    {
        $email = '<EMAIL>';

        $canResend = $this->service->canResendCode($email);

        $this->assertTrue($canResend);
    }

    public function test_cannot_resend_code_immediately_after_sending(): void
    {
        $email = '<EMAIL>';

        $this->service->sendVerificationCode($email);

        $canResend = $this->service->canResendCode($email);

        $this->assertFalse($canResend);
    }

    public function test_send_verification_code_throws_exception_during_cooldown(): void
    {
        $email = '<EMAIL>';

        $this->service->sendVerificationCode($email);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Please wait before requesting a new verification code.');

        $this->service->sendVerificationCode($email);
    }

    public function test_can_overwrite_existing_code_after_cooldown(): void
    {
        $email = '<EMAIL>';

        $code1 = $this->service->sendVerificationCode($email);

        // Mock cooldown expiry by removing retry lock
        Cache::forget('email_verification_retry:' . $email);

        $code2 = $this->service->sendVerificationCode($email);

        // First code should no longer be valid
        $this->assertFalse($this->service->verifyCode($email, $code1));

        // Second code should be valid
        $this->assertTrue($this->service->verifyCode($email, $code2));
    }

    public function test_successful_verification_clears_resend_cooldown(): void
    {
        $email = '<EMAIL>';

        $code = $this->service->sendVerificationCode($email);

        // Should be in cooldown period
        $this->assertFalse($this->service->canResendCode($email));

        // Verify the code successfully
        $this->assertTrue($this->service->verifyCode($email, $code));

        // After successful verification, should be able to resend immediately
        $this->assertTrue($this->service->canResendCode($email));
    }

    public function test_verification_code_format(): void
    {
        // Generate multiple codes to test format consistency using different emails
        // to avoid retry interval restrictions
        for ($i = 0; $i < 10; $i++) {
            $email = "test{$i}@example.com";
            $code = $this->service->sendVerificationCode($email);

            $this->assertEquals(6, strlen($code));
            $this->assertMatchesRegularExpression('/^\d{6}$/', $code);
            $this->assertGreaterThanOrEqual(0, (int) $code);
            $this->assertLessThanOrEqual(999999, (int) $code);
        }
    }
}
