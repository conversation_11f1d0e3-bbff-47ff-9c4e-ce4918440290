<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ReportService;
use App\Services\SalesReportService;
use App\Services\RefundReportService;
use App\Services\OrderStatusReportService;
use App\Models\Organisation;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;

/**
 * Product Ranking Report Test
 * 
 * Tests the product ranking functionality with both sales and quantity rankings
 */
final class ProductRankingReportTest extends TestCase
{
    use RefreshDatabase;

    private ReportService $reportService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $salesReportService = new SalesReportService();
        $refundReportService = new RefundReportService();
        $orderStatusReportService = new OrderStatusReportService();
        
        $this->reportService = new ReportService(
            $salesReportService,
            $refundReportService,
            $orderStatusReportService
        );
    }

    public function test_product_ranking_report_returns_both_sales_and_quantity_rankings(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);
        
        // Create products
        $product1 = Product::factory()->create([
            'store_variant_id' => 1001,
            'name' => 'High Sales Product',
            'owner_id' => 'TEST001',
        ]);

        $product2 = Product::factory()->create([
            'store_variant_id' => 1002,
            'name' => 'High Volume Product',
            'owner_id' => 'TEST001',
        ]);

        // Create orders with different sales vs quantity patterns
        $order1 = Order::factory()->create([
            'completed_at' => '2024-01-15 10:00:00',
            'state' => 'completed',
        ]);
        
        $order2 = Order::factory()->create([
            'completed_at' => '2024-01-20 10:00:00',
            'state' => 'completed',
        ]);

        // Product 1: High sales value, low quantity
        OrderItem::factory()->create([
            'order_id' => $order1->id,
            'store_variant_id' => 1001,
            'quantity' => 2,
            'quantity_refunded' => 0,
            'total' => 10000, // $100.00
        ]);

        // Product 2: Low sales value, high quantity
        OrderItem::factory()->create([
            'order_id' => $order2->id,
            'store_variant_id' => 1002,
            'quantity' => 10,
            'quantity_refunded' => 0,
            'total' => 5000, // $50.00
        ]);

        // Test the report
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [$product1->id, $product2->id],
            'limit' => 10,
        ];

        $result = $this->reportService->getProductRankingReport($filters);

        // Verify structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('sales_rankings', $result);
        $this->assertArrayHasKey('quantity_rankings', $result);
        $this->assertArrayHasKey('total_products', $result);
        $this->assertArrayHasKey('limit', $result);
        $this->assertArrayHasKey('period', $result);

        // Verify sales rankings (Product 1 should be first)
        $salesRankings = $result['sales_rankings'];
        $this->assertCount(2, $salesRankings);
        $this->assertEquals(1001, $salesRankings[0]['store_variant_id']);
        $this->assertEquals(1, $salesRankings[0]['rank']);
        $this->assertEquals(10000, $salesRankings[0]['total_sales']);
        $this->assertEquals(2, $salesRankings[0]['total_quantity']);

        // Verify quantity rankings (Product 2 should be first)
        $quantityRankings = $result['quantity_rankings'];
        $this->assertCount(2, $quantityRankings);
        $this->assertEquals(1002, $quantityRankings[0]['store_variant_id']);
        $this->assertEquals(1, $quantityRankings[0]['rank']);
        $this->assertEquals(10, $quantityRankings[0]['total_quantity']);
        $this->assertEquals(5000, $quantityRankings[0]['total_sales']);

        // Verify metadata
        $this->assertEquals(2, $result['total_products']);
        $this->assertEquals(10, $result['limit']);
    }

    public function test_product_ranking_report_handles_empty_data(): void
    {
        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => [],
            'limit' => 10,
        ];

        $result = $this->reportService->getProductRankingReport($filters);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('sales_rankings', $result);
        $this->assertArrayHasKey('quantity_rankings', $result);
        $this->assertEmpty($result['sales_rankings']);
        $this->assertEmpty($result['quantity_rankings']);
        $this->assertEquals(0, $result['total_products']);
    }

    public function test_product_ranking_report_respects_limit(): void
    {
        // Create test data with more products than limit
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);

        $productIds = [];
        for ($i = 1; $i <= 5; $i++) {
            $storeVariantId = 1000 + $i;
            $product = Product::factory()->create([
                'store_variant_id' => $storeVariantId,
                'name' => "Product {$i}",
                'owner_id' => 'TEST001',
            ]);
            $productIds[] = $product->id;

            $order = Order::factory()->create([
                'completed_at' => '2024-01-15 10:00:00',
                'state' => 'completed',
            ]);

            OrderItem::factory()->create([
                'order_id' => $order->id,
                'store_variant_id' => $storeVariantId,
                'quantity' => $i,
                'quantity_refunded' => 0,
                'total' => $i * 1000,
            ]);
        }

        $filters = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'product_ids' => $productIds,
            'limit' => 3,
        ];

        $result = $this->reportService->getProductRankingReport($filters);

        // Both rankings should respect the limit
        $this->assertCount(3, $result['sales_rankings']);
        $this->assertCount(3, $result['quantity_rankings']);
        $this->assertEquals(5, $result['total_products']); // Total products found
        $this->assertEquals(3, $result['limit']); // Applied limit
    }
}
