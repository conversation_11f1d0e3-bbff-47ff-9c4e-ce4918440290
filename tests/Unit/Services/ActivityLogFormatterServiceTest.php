<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\User;
use App\Services\ActivityLogFormatterService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Spatie\Activitylog\Models\Activity;
use Tests\TestCase;

final class ActivityLogFormatterServiceTest extends TestCase
{
    use RefreshDatabase;

    private ActivityLogFormatterService $formatter;

    protected function setUp(): void
    {
        parent::setUp();
        $this->formatter = new ActivityLogFormatterService();
        $this->enableActivityLogging();
    }

    public function test_formats_description_in_english(): void
    {
        App::setLocale('en');
        
        $user = User::factory()->create(['name' => 'John Doe']);
        $organisation = Organisation::factory()->create(['name' => 'Test Org']);
        
        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'created',
        ]);
        
        $activity->subject = $organisation;
        $activity->causer = $user;
        
        $result = $this->formatter->formatDescription($activity);
        
        $this->assertStringContainsString('User John Doe', $result);
        $this->assertStringContainsString('created an organisation', $result);
        $this->assertStringContainsString('Test Org', $result);
    }

    public function test_formats_description_in_chinese(): void
    {
        App::setLocale('zh');
        
        $user = User::factory()->create(['name' => '张三']);
        $organisation = Organisation::factory()->create(['name' => '测试组织']);
        
        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'created',
        ]);
        
        $activity->subject = $organisation;
        $activity->causer = $user;
        
        $result = $this->formatter->formatDescription($activity);
        
        $this->assertStringContainsString('用户 张三', $result);
        $this->assertStringContainsString('创建了组织', $result);
        $this->assertStringContainsString('测试组织', $result);
    }

    public function test_formats_invitation_description(): void
    {
        App::setLocale('en');
        
        $user = User::factory()->create(['name' => 'Admin User']);
        $invitation = Invitation::factory()->create([
            'role' => 'member',
            'email_restriction' => '<EMAIL>'
        ]);
        
        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Invitation::class,
            'subject_id' => $invitation->id,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'created',
        ]);
        
        $activity->subject = $invitation;
        $activity->causer = $user;
        
        $result = $this->formatter->formatDescription($activity);
        
        $this->assertStringContainsString('User Admin User', $result);
        $this->assertStringContainsString('created an invitation', $result);
        $this->assertStringContainsString('Role: member', $result);
        $this->assertStringContainsString('Email: <EMAIL>', $result);
    }

    public function test_handles_missing_causer(): void
    {
        App::setLocale('en');
        
        $organisation = Organisation::factory()->create(['name' => 'Test Org']);
        
        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'event' => 'created',
        ]);
        
        $activity->subject = $organisation;
        
        $result = $this->formatter->formatDescription($activity);
        
        $this->assertStringNotContainsString('User', $result);
        $this->assertStringContainsString('created an organisation', $result);
        $this->assertStringContainsString('Test Org', $result);
    }

    public function test_handles_missing_subject(): void
    {
        App::setLocale('en');
        
        $user = User::factory()->create(['name' => 'John Doe']);
        
        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => 999, // Non-existent ID
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'created',
        ]);
        
        $activity->causer = $user;
        
        $result = $this->formatter->formatDescription($activity);
        
        $this->assertStringContainsString('User John Doe', $result);
        $this->assertStringContainsString('created an organisation', $result);
        $this->assertStringNotContainsString('(', $result); // No subject info in parentheses
    }

    public function test_falls_back_to_generic_description(): void
    {
        App::setLocale('en');

        $user = User::factory()->create(['name' => 'John Doe']);

        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'unknown_event',
            'subject_type' => 'App\\Models\\UnknownModel',
            'subject_id' => 1,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'unknown_event',
        ]);

        $activity->causer = $user;
        // Don't set subject since it's an unknown model

        $result = $this->formatter->formatDescription($activity);

        $this->assertStringContainsString('User John Doe', $result);
        $this->assertStringContainsString('performed', $result);
        $this->assertStringContainsString('operation on', $result);
    }

    public function test_formats_user_with_email_fallback(): void
    {
        App::setLocale('en');

        $user = User::factory()->create([
            'name' => 'Test User', // Keep name, we'll test email fallback in the formatter
            'email' => '<EMAIL>'
        ]);

        // Manually set name to null after creation to test email fallback
        $user->name = null;

        $organisation = Organisation::factory()->create(['name' => 'Test Org']);

        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'created',
        ]);

        $activity->subject = $organisation;
        $activity->causer = $user;

        $result = $this->formatter->formatDescription($activity);

        $this->assertStringContainsString('User <EMAIL>', $result);
        $this->assertStringContainsString('created an organisation', $result);
    }

    public function test_formats_different_events(): void
    {
        App::setLocale('en');

        $user = User::factory()->create(['name' => 'Test User']);
        $organisation = Organisation::factory()->create(['name' => 'Test Org']);

        // Test updated event
        $activity = new Activity([
            'log_name' => 'default',
            'description' => 'updated',
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'updated',
        ]);

        $activity->subject = $organisation;
        $activity->causer = $user;

        $result = $this->formatter->formatDescription($activity);

        $this->assertStringContainsString('User Test User', $result);
        $this->assertStringContainsString('updated an organisation', $result);
        $this->assertStringContainsString('Test Org', $result);
    }
}
