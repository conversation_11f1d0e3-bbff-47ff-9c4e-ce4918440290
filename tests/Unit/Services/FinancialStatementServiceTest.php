<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\FinancialStatement;
use App\Models\Order;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\FinancialStatementService;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;

final class FinancialStatementServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private FinancialStatementService $service;
    private PermissionService $permissionService;
    private User $systemAdmin;
    private User $organisationOwner;
    private Organisation $organisation;
    private Organisation $otherOrganisation;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new FinancialStatementService();
        $this->permissionService = app(PermissionService::class);

        // Create organisations
        $this->organisation = Organisation::factory()->create(['code' => 'TEST001']);
        $this->otherOrganisation = Organisation::factory()->create(['code' => 'TEST002']);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $ownerRole = collect($orgRoles)->firstWhere('name', 'owner');

        // Create system admin
        $this->systemAdmin = User::factory()->create();
        $this->permissionService->assignRoleToUser($this->systemAdmin, $systemAdminRole);

        // Create organisation owner
        $this->organisationOwner = User::factory()->create();
        $this->organisationOwner->organisations()->attach($this->organisation->id);
        $this->permissionService->assignRoleToUser($this->organisationOwner, $ownerRole);
    }

    public function test_get_financial_statements_returns_all_for_system_admin(): void
    {
        // Create financial statements for different organisations
        FinancialStatement::factory()->count(3)->create([
            'organisation_id' => $this->organisation->id,
        ]);
        FinancialStatement::factory()->count(2)->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $result = $this->service->getFinancialStatements($this->systemAdmin);

        $this->assertCount(5, $result->items());
    }

    public function test_get_financial_statements_filters_by_organisation_for_non_admin(): void
    {
        // Create financial statements for different organisations
        FinancialStatement::factory()->count(3)->create([
            'organisation_id' => $this->organisation->id,
        ]);
        FinancialStatement::factory()->count(2)->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $result = $this->service->getFinancialStatements($this->organisationOwner);

        $this->assertCount(3, $result->items());

        foreach ($result->items() as $statement) {
            $this->assertEquals($this->organisation->id, $statement->organisation_id);
        }
    }

    public function test_get_financial_statements_filters_by_status(): void
    {
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PUBLISHED,
        ]);

        $result = $this->service->getFinancialStatements(
            $this->systemAdmin,
            status: FinancialStatement::STATUS_PUBLISHED
        );

        $this->assertCount(1, $result->items());
        $this->assertEquals(FinancialStatement::STATUS_PUBLISHED, $result->items()[0]->status);
    }

    public function test_get_financial_statements_filters_by_report_type(): void
    {
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
        ]);
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
        ]);

        $result = $this->service->getFinancialStatements(
            $this->systemAdmin,
            reportType: FinancialStatement::TYPE_QUARTERLY
        );

        $this->assertCount(1, $result->items());
        $this->assertEquals(FinancialStatement::TYPE_QUARTERLY, $result->items()[0]->report_type);
    }

    public function test_get_financial_statements_filters_by_organisation_id(): void
    {
        FinancialStatement::factory()->count(2)->create([
            'organisation_id' => $this->organisation->id,
        ]);
        FinancialStatement::factory()->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $result = $this->service->getFinancialStatements(
            $this->systemAdmin,
            organisationId: $this->organisation->id
        );

        $this->assertCount(2, $result->items());

        foreach ($result->items() as $statement) {
            $this->assertEquals($this->organisation->id, $statement->organisation_id);
        }
    }

    public function test_get_by_organisation_returns_statements_for_specific_organisation(): void
    {
        FinancialStatement::factory()->count(3)->create([
            'organisation_id' => $this->organisation->id,
        ]);
        FinancialStatement::factory()->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $result = $this->service->getByOrganisation($this->organisation->id);

        $this->assertCount(3, $result->items());

        foreach ($result->items() as $statement) {
            $this->assertEquals($this->organisation->id, $statement->organisation_id);
        }
    }

    public function test_get_by_id_returns_financial_statement_with_relations(): void
    {
        $statement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
        ]);

        $result = $this->service->getById($statement->id);

        $this->assertNotNull($result);
        $this->assertEquals($statement->id, $result->id);
        $this->assertTrue($result->relationLoaded('organisation'));
        $this->assertTrue($result->relationLoaded('orders'));
    }

    public function test_get_by_id_returns_null_for_nonexistent_statement(): void
    {
        $result = $this->service->getById(99999);

        $this->assertNull($result);
    }

    public function test_create_generates_financial_statement_with_code_and_title(): void
    {
        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
            'total_amount' => 100000,
            'total_quantity' => 50,
            'total_orders' => 10,
        ];

        $result = $this->service->create($data, $this->systemAdmin);

        $this->assertInstanceOf(FinancialStatement::class, $result);
        $this->assertEquals(FinancialStatement::TYPE_MONTHLY, $result->report_type);
        $this->assertEquals($this->organisation->id, $result->organisation_id);
        $this->assertEquals(FinancialStatement::STATUS_PENDING_AUDIT, $result->status);
        $this->assertNotNull($result->report_code);
        $this->assertNotNull($result->report_title);
    }

    public function test_create_with_orders_attaches_orders(): void
    {
        $orders = Order::factory()->count(3)->create();

        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
            'order_ids' => $orders->pluck('id')->toArray(),
        ];

        $result = $this->service->create($data, $this->systemAdmin);

        $this->assertCount(3, $result->orders);
    }

    public function test_create_validates_order_uniqueness_by_report_type(): void
    {
        $orders = Order::factory()->count(2)->create();

        // Create first financial statement with orders
        $existingStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'organisation_id' => $this->organisation->id,
        ]);
        $existingStatement->orders()->attach($orders->pluck('id')->toArray());

        // Try to create another monthly statement with same orders
        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-02-01',
            'end_date' => '2024-02-29',
            'organisation_id' => $this->organisation->id,
            'order_ids' => $orders->pluck('id')->toArray(),
        ];

        $this->expectException(ValidationException::class);

        $this->service->create($data, $this->systemAdmin);
    }

    public function test_create_allows_same_orders_for_different_report_types(): void
    {
        $orders = Order::factory()->count(2)->create();

        // Create monthly statement with orders
        $monthlyStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'organisation_id' => $this->organisation->id,
        ]);
        $monthlyStatement->orders()->attach($orders->pluck('id')->toArray());

        // Create quarterly statement with same orders (should be allowed)
        $data = [
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
            'start_date' => '2024-01-01',
            'end_date' => '2024-03-31',
            'organisation_id' => $this->organisation->id,
            'order_ids' => $orders->pluck('id')->toArray(),
        ];

        $result = $this->service->create($data, $this->systemAdmin);

        $this->assertInstanceOf(FinancialStatement::class, $result);
        $this->assertCount(2, $result->orders);
    }

    public function test_update_modifies_financial_statement(): void
    {
        $statement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);

        $data = [
            'total_amount' => 200000,
            'total_quantity' => 100,
            'remarks' => 'Updated remarks',
        ];

        $result = $this->service->update($statement, $data, $this->systemAdmin);

        $this->assertEquals(200000, $result->total_amount);
        $this->assertEquals(100, $result->total_quantity);
        $this->assertEquals('Updated remarks', $result->remarks);
    }

    public function test_update_syncs_orders(): void
    {
        $statement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
        ]);

        $initialOrders = Order::factory()->count(2)->create();
        $statement->orders()->attach($initialOrders->pluck('id')->toArray());

        $newOrders = Order::factory()->count(3)->create();

        $data = [
            'order_ids' => $newOrders->pluck('id')->toArray(),
        ];

        $result = $this->service->update($statement, $data, $this->systemAdmin);

        $this->assertCount(3, $result->orders);

        $resultOrderIds = $result->orders->pluck('id')->toArray();
        $this->assertEquals($newOrders->pluck('id')->sort()->values()->toArray(), collect($resultOrderIds)->sort()->values()->toArray());
    }

    public function test_update_regenerates_code_and_title_when_relevant_fields_change(): void
    {
        $statement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
        ]);

        $originalCode = $statement->report_code;
        $originalTitle = $statement->report_title;

        $data = [
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
        ];

        $result = $this->service->update($statement, $data, $this->systemAdmin);

        $this->assertNotEquals($originalCode, $result->report_code);
        $this->assertNotEquals($originalTitle, $result->report_title);
        $this->assertEquals(FinancialStatement::TYPE_QUARTERLY, $result->report_type);
    }

    public function test_publish_changes_status_to_published(): void
    {
        $statement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);

        $result = $this->service->publish($statement);

        $this->assertEquals(FinancialStatement::STATUS_PUBLISHED, $result->status);
    }

    public function test_get_statistics_returns_aggregated_data_for_system_admin(): void
    {
        // Create various financial statements
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'total_amount' => 100000,
            'total_orders' => 10,
            'total_quantity' => 50,
        ]);

        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PUBLISHED,
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
            'total_amount' => 200000,
            'total_orders' => 20,
            'total_quantity' => 100,
        ]);

        FinancialStatement::factory()->create([
            'organisation_id' => $this->otherOrganisation->id,
            'status' => FinancialStatement::STATUS_PUBLISHED,
            'report_type' => FinancialStatement::TYPE_YEARLY,
            'total_amount' => 300000,
            'total_orders' => 30,
            'total_quantity' => 150,
        ]);

        $result = $this->service->getStatistics($this->systemAdmin);

        $this->assertEquals(3, $result['total_statements']);
        $this->assertEquals(1, $result['pending_audit']);
        $this->assertEquals(2, $result['published']);
        $this->assertEquals(1, $result['by_type']['monthly']);
        $this->assertEquals(1, $result['by_type']['quarterly']);
        $this->assertEquals(1, $result['by_type']['yearly']);
        $this->assertEquals(0, $result['by_type']['custom']);
        $this->assertEquals(600000, $result['total_amount']);
        $this->assertEquals(60, $result['total_orders']);
        $this->assertEquals(300, $result['total_quantity']);
    }

    public function test_get_statistics_filters_by_organisation_for_non_admin(): void
    {
        // Create financial statements for different organisations
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'total_amount' => 100000,
        ]);

        FinancialStatement::factory()->create([
            'organisation_id' => $this->otherOrganisation->id,
            'total_amount' => 200000,
        ]);

        $result = $this->service->getStatistics($this->organisationOwner);

        $this->assertEquals(1, $result['total_statements']);
        $this->assertEquals(100000, $result['total_amount']);
    }

    public function test_get_statistics_filters_by_specific_organisation(): void
    {
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'total_amount' => 100000,
        ]);

        FinancialStatement::factory()->create([
            'organisation_id' => $this->otherOrganisation->id,
            'total_amount' => 200000,
        ]);

        $result = $this->service->getStatistics($this->systemAdmin, $this->organisation->id);

        $this->assertEquals(1, $result['total_statements']);
        $this->assertEquals(100000, $result['total_amount']);
    }
}
