<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Jobs\SendEmailJob;
use App\Models\EmailTemplate;
use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use InvalidArgumentException;
use Tests\TestCase;

final class EmailServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailService $emailService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailService = app(EmailService::class);
        Queue::fake();
    }

    public function test_can_send_template_email(): void
    {
        // Create a test template
        $template = EmailTemplate::factory()->create([
            'code' => 'test_template',
            'subject' => 'Test Subject: {name}',
            'content' => 'Hello {name}, this is a test email.',
            'is_active' => true,
        ]);

        $result = $this->emailService->sendTemplateEmail(
            'test_template',
            '<EMAIL>',
            ['name' => '<PERSON>'],
            '<PERSON>'
        );

        $this->assertTrue($result);

        Queue::assertPushed(SendEmailJob::class, function ($job) {
            return $job->getToEmail() === '<EMAIL>' &&
                   $job->getToName() === 'John Doe' &&
                   $job->getTemplateCode() === 'test_template' &&
                   str_contains($job->getSubject(), 'John Doe') &&
                   str_contains($job->getContent(), 'Hello John Doe');
        });
    }

    public function test_throws_exception_for_nonexistent_template(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template with code 'nonexistent' not found or inactive");

        $this->emailService->sendTemplateEmail(
            'nonexistent',
            '<EMAIL>',
            []
        );
    }

    public function test_throws_exception_for_inactive_template(): void
    {
        EmailTemplate::factory()->create([
            'code' => 'inactive_template',
            'is_active' => false,
        ]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template with code 'inactive_template' not found or inactive");

        $this->emailService->sendTemplateEmail(
            'inactive_template',
            '<EMAIL>',
            []
        );
    }

    public function test_throws_exception_for_invalid_template(): void
    {
        EmailTemplate::factory()->create([
            'code' => 'invalid_template',
            'subject' => '', // Empty subject makes template invalid
            'is_active' => true,
        ]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template 'invalid_template' is not valid");

        $this->emailService->sendTemplateEmail(
            'invalid_template',
            '<EMAIL>',
            []
        );
    }

    public function test_throws_exception_for_missing_variables(): void
    {
        EmailTemplate::factory()->create([
            'code' => 'template_with_vars',
            'subject' => 'Hello {name}',
            'content' => 'Your code is {code}',
            'is_active' => true,
        ]);

        $this->expectException(InvalidArgumentException::class);

        $this->emailService->sendTemplateEmail(
            'template_with_vars',
            '<EMAIL>',
            ['name' => 'John'] // Missing 'code' variable
        );
    }

    public function test_can_send_custom_email(): void
    {
        $result = $this->emailService->sendCustomEmail(
            '<EMAIL>',
            'Custom Subject',
            '<p>Custom HTML content</p>',
            'John Doe'
        );

        $this->assertTrue($result);

        Queue::assertPushed(SendEmailJob::class, function ($job) {
            return $job->getToEmail() === '<EMAIL>' &&
                   $job->getToName() === 'John Doe' &&
                   $job->getSubject() === 'Custom Subject' &&
                   $job->getContent() === '<p>Custom HTML content</p>' &&
                   $job->getTemplateCode() === null;
        });
    }

    public function test_throws_exception_for_invalid_email_address(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid email address provided');

        $this->emailService->sendCustomEmail(
            'invalid-email',
            'Subject',
            'Content'
        );
    }

    public function test_throws_exception_for_empty_subject(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Email subject cannot be empty');

        $this->emailService->sendCustomEmail(
            '<EMAIL>',
            '',
            'Content'
        );
    }

    public function test_throws_exception_for_empty_content(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Email content cannot be empty');

        $this->emailService->sendCustomEmail(
            '<EMAIL>',
            'Subject',
            ''
        );
    }

    public function test_custom_email_without_recipient_name(): void
    {
        $result = $this->emailService->sendCustomEmail(
            '<EMAIL>',
            'Subject',
            'Content'
        );

        $this->assertTrue($result);

        Queue::assertPushed(SendEmailJob::class, function ($job) {
            return $job->getToEmail() === '<EMAIL>' &&
                   $job->getToName() === null &&
                   $job->getSubject() === 'Subject' &&
                   $job->getContent() === 'Content';
        });
    }
}
