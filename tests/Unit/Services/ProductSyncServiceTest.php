<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Services\ProductSyncService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Unit tests for ProductSyncService
 *
 * Complex integration tests have been moved to ProductSyncServiceIntegrationTest
 * This file contains only simple unit tests that don't require complex mocking.
 */
final class ProductSyncServiceTest extends TestCase
{
    use RefreshDatabase;

    private ProductSyncService $productSyncService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->productSyncService = app(ProductSyncService::class);
    }

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to ProductSyncServiceIntegrationTest as it requires complex database operations

    public function test_get_sync_statistics_returns_correct_data(): void
    {
        $this->productSyncService = app(ProductSyncService::class);

        // Create test sync logs with explicit timestamps
        $olderTime = now()->subHours(2);
        $newerTime = now()->subHour();

        // Create older sync log first
        $olderSync = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'batch_1',
            'status' => 'completed',
            'started_at' => $olderTime,
            'completed_at' => $olderTime->copy()->addMinutes(30),
        ]);
        $olderSync->created_at = $olderTime;
        $olderSync->save();

        // Create newer sync log
        $newerSync = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'batch_2',
            'status' => 'failed',
            'started_at' => $newerTime,
            'completed_at' => $newerTime->copy()->addMinutes(30),
        ]);
        $newerSync->created_at = $newerTime;
        $newerSync->save();

        $stats = $this->productSyncService->getSyncStatistics();

        $this->assertEquals(2, $stats['total_syncs']);
        $this->assertEquals(1, $stats['successful_syncs']);
        $this->assertEquals(1, $stats['failed_syncs']);
        $this->assertEquals(50.0, $stats['success_rate']);
        $this->assertNotNull($stats['last_sync']);
        $this->assertEquals('batch_2', $stats['last_sync']['batch_id']);
    }

    public function test_cleanup_removes_old_records(): void
    {
        $this->productSyncService = app(ProductSyncService::class);

        // Create old sync log and records
        $oldSyncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'old_batch',
            'status' => 'completed',
            'started_at' => now()->subDays(35),
            'completed_at' => now()->subDays(35),
        ]);
        $oldSyncLog->created_at = now()->subDays(35);
        $oldSyncLog->save();

        $syncRecord = SyncRecord::create([
            'batch_id' => 'old_batch',
            'source_table' => 'sylius_product_variant',
            'source_id' => '1',
            'target_table' => 'products',
            'status' => 'success',
        ]);
        $syncRecord->created_at = now()->subDays(35);
        $syncRecord->save();

        // Create recent sync log (should not be deleted)
        $recentSyncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'recent_batch',
            'status' => 'completed',
            'started_at' => now()->subDays(5),
            'completed_at' => now()->subDays(5),
        ]);
        $recentSyncLog->created_at = now()->subDays(5);
        $recentSyncLog->save();

        $result = $this->productSyncService->cleanup(30);

        $this->assertEquals(1, $result['records_deleted']);
        $this->assertEquals(1, $result['logs_deleted']);

        // Verify old records are deleted
        $this->assertDatabaseMissing('sync_logs', ['batch_id' => 'old_batch']);
        $this->assertDatabaseMissing('sync_records', ['batch_id' => 'old_batch']);

        // Verify recent records are kept
        $this->assertDatabaseHas('sync_logs', ['batch_id' => 'recent_batch']);
    }

    // These mocking methods have been removed as they are no longer needed
    // The tests that used them have been moved to ProductSyncServiceIntegrationTest
}
