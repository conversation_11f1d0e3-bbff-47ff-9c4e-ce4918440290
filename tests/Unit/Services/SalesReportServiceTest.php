<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Organisation;
use App\Models\Product;
use App\Models\Role;
use App\Models\User;
use App\Services\SalesReportService;
use App\Services\RefundReportService;
use App\Services\OrderStatusReportService;
use App\Http\Resources\Api\V1\SalesReportResource;
use App\Http\Resources\Api\V1\VolumeReportResource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class SalesReportServiceTest extends TestCase
{
    use RefreshDatabase;

    private SalesReportService $salesReportService;
    private RefundReportService $refundReportService;
    private OrderStatusReportService $orderStatusReportService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->salesReportService = new SalesReportService();
        $this->refundReportService = new RefundReportService();
        $this->orderStatusReportService = new OrderStatusReportService();
    }

    public function test_grouped_data_does_not_contain_duplicate_dates_for_multiple_countries(): void
    {
        // Create test data with orders from multiple countries on the same date
        $this->createTestOrdersWithMultipleCountries();

        // Create authenticated user
        $user = $this->createAuthenticatedUser();
        Sanctum::actingAs($user);

        // Build query for orders
        $query = Order::query()
            ->whereBetween('completed_at', ['2021-08-26 00:00:00', '2021-08-27 23:59:59']);

        $filters = [
            'start_date' => '2021-08-26',
            'end_date' => '2021-08-27',
            'group_by' => 'day',
            'timezone' => 'UTC',
        ];

        // Generate report
        $result = $this->salesReportService->generateReport($query, $filters);

        // Verify chart_data structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('chart_data', $result);
        $chartData = $result['chart_data'];

        // Extract unique periods from chart data
        $periods = array_unique(array_column($chartData, 'period'));

        // Should have exactly 2 unique dates (2021-08-26 and 2021-08-27)
        $this->assertCount(2, $periods);
        $this->assertContains('2021-08-26', $periods);
        $this->assertContains('2021-08-27', $periods);

        // Verify that each period appears only once in the chart data
        $periodCounts = array_count_values(array_column($chartData, 'period'));
        foreach ($periodCounts as $count) {
            $this->assertEquals(1, $count, 'Each date should appear only once in chart data');
        }
    }

    public function test_grouped_data_aggregates_sales_correctly_across_countries(): void
    {
        // Create test data
        $this->createTestOrdersWithMultipleCountries();

        // Create authenticated user
        $user = $this->createAuthenticatedUser();
        Sanctum::actingAs($user);

        // Build query for orders on 2021-08-26
        $query = Order::query()
            ->whereBetween('completed_at', ['2021-08-26 00:00:00', '2021-08-26 23:59:59']);

        $filters = [
            'start_date' => '2021-08-26',
            'end_date' => '2021-08-26',
            'group_by' => 'day',
            'timezone' => 'UTC',
        ];

        // Generate report
        $result = $this->salesReportService->generateReport($query, $filters);
        $chartData = $result['chart_data'];

        // Should have exactly one record for 2021-08-26
        $this->assertCount(1, $chartData);
        
        $dayData = $chartData[0];
        $this->assertEquals('2021-08-26', $dayData['period']);
        
        // Should aggregate orders from all countries
        // We created 3 orders: 2 from US ($100 + $150) and 1 from CA ($200)
        // Total should be $450 (45000 cents)
        $this->assertEquals(3, $dayData['order_count']);
        $this->assertEquals(45000, $dayData['total_sales']); // $450 in cents
    }

    public function test_grouped_volume_data_does_not_contain_duplicate_dates(): void
    {
        // Create test data
        $this->createTestOrdersWithMultipleCountries();

        // Create authenticated user
        $user = $this->createAuthenticatedUser();
        Sanctum::actingAs($user);

        // Build query for orders
        $query = Order::query()
            ->whereBetween('completed_at', ['2021-08-26 00:00:00', '2021-08-27 23:59:59']);

        $filters = [
            'start_date' => '2021-08-26',
            'end_date' => '2021-08-27',
            'group_by' => 'day',
            'timezone' => 'UTC',
        ];

        // Generate volume report
        $result = $this->salesReportService->generateVolumeReport($query, $filters);

        // Verify chart_data structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('chart_data', $result);
        $chartData = $result['chart_data'];

        // Extract unique periods from chart data
        $periods = array_unique(array_column($chartData, 'period'));

        // Should have exactly 2 unique dates
        $this->assertCount(2, $periods);
        $this->assertContains('2021-08-26', $periods);
        $this->assertContains('2021-08-27', $periods);

        // Verify that each period appears only once
        $periodCounts = array_count_values(array_column($chartData, 'period'));
        foreach ($periodCounts as $count) {
            $this->assertEquals(1, $count, 'Each date should appear only once in volume chart data');
        }
    }

    private function createTestOrdersWithMultipleCountries(): void
    {
        // Create organisation and product
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);
        $product = Product::create([
            'store_variant_id' => 1001,
            'owner_id' => 'TEST001',
            'sku' => 'TEST-SKU-001',
            'code' => 'TEST-CODE-001',
            'name' => 'Test Product',
            'enabled' => true,
            'slug' => 'test-product',
            'current_price' => 5000, // $50 in cents
            'original_price' => 5000,
        ]);

        // Create orders from different countries on the same date
        // Order 1: US, $100, 2021-08-26
        $order1 = Order::factory()->create([
            'total_amount' => 10000, // $100 in cents
            'shipping_country' => 'US',
            'completed_at' => '2021-08-26 10:00:00',
            'payment_state' => 'completed',
        ]);
        OrderItem::factory()->create([
            'order_id' => $order1->id,
            'store_variant_id' => $product->store_variant_id,
            'quantity' => 2,
        ]);

        // Order 2: US, $150, 2021-08-26
        $order2 = Order::factory()->create([
            'total_amount' => 15000, // $150 in cents
            'shipping_country' => 'US',
            'completed_at' => '2021-08-26 14:00:00',
            'payment_state' => 'completed',
        ]);
        OrderItem::factory()->create([
            'order_id' => $order2->id,
            'store_variant_id' => $product->store_variant_id,
            'quantity' => 3,
        ]);

        // Order 3: CA, $200, 2021-08-26
        $order3 = Order::factory()->create([
            'total_amount' => 20000, // $200 in cents
            'shipping_country' => 'CA',
            'completed_at' => '2021-08-26 16:00:00',
            'payment_state' => 'completed',
        ]);
        OrderItem::factory()->create([
            'order_id' => $order3->id,
            'store_variant_id' => $product->store_variant_id,
            'quantity' => 1,
        ]);

        // Order 4: US, $300, 2021-08-27
        $order4 = Order::factory()->create([
            'total_amount' => 30000, // $300 in cents
            'shipping_country' => 'US',
            'completed_at' => '2021-08-27 12:00:00',
            'payment_state' => 'completed',
        ]);
        OrderItem::factory()->create([
            'order_id' => $order4->id,
            'store_variant_id' => $product->store_variant_id,
            'quantity' => 4,
        ]);
    }

    private function createAuthenticatedUser(): User
    {
        // Create system root role
        $systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create system root user
        $systemRootUser = User::factory()->create(['name' => 'System Root']);

        // Assign system role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRootUser->roles()->attach($systemRootRole);

        return $systemRootUser;
    }

    public function test_refund_report_does_not_contain_duplicate_dates(): void
    {
        // Create test data
        $this->createTestOrdersWithMultipleCountries();

        // Create authenticated user
        $user = $this->createAuthenticatedUser();
        Sanctum::actingAs($user);

        // Build query for orders
        $query = Order::query()
            ->whereBetween('completed_at', ['2021-08-26 00:00:00', '2021-08-27 23:59:59']);

        $filters = [
            'start_date' => '2021-08-26',
            'end_date' => '2021-08-27',
            'group_by' => 'day',
            'timezone' => 'UTC',
        ];

        // Generate refund report
        $result = $this->refundReportService->generateReport($query, $filters);

        // Verify chart_data structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('chart_data', $result);
        $chartData = $result['chart_data'];

        // Extract unique periods from chart data
        $periods = array_unique(array_column($chartData, 'period'));

        // Should have exactly 2 unique dates
        $this->assertCount(2, $periods);
        $this->assertContains('2021-08-26', $periods);
        $this->assertContains('2021-08-27', $periods);

        // Verify that each period appears only once
        $periodCounts = array_count_values(array_column($chartData, 'period'));
        foreach ($periodCounts as $count) {
            $this->assertEquals(1, $count, 'Each date should appear only once in refund chart data');
        }
    }

    public function test_order_status_report_does_not_contain_duplicate_dates(): void
    {
        // Create test data
        $this->createTestOrdersWithMultipleCountries();

        // Create authenticated user
        $user = $this->createAuthenticatedUser();
        Sanctum::actingAs($user);

        // Build query for orders
        $query = Order::query()
            ->whereBetween('completed_at', ['2021-08-26 00:00:00', '2021-08-27 23:59:59']);

        $filters = [
            'start_date' => '2021-08-26',
            'end_date' => '2021-08-27',
            'group_by' => 'day',
            'timezone' => 'UTC',
        ];

        // Generate order status report
        $result = $this->orderStatusReportService->generateReport($query, $filters);

        // Verify chart_data structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('chart_data', $result);
        $chartData = $result['chart_data'];

        // Extract unique periods from chart data
        $periods = array_unique(array_column($chartData, 'period'));

        // Should have exactly 2 unique dates
        $this->assertCount(2, $periods);
        $this->assertContains('2021-08-26', $periods);
        $this->assertContains('2021-08-27', $periods);

        // Verify that each period appears only once
        $periodCounts = array_count_values(array_column($chartData, 'period'));
        foreach ($periodCounts as $count) {
            $this->assertEquals(1, $count, 'Each date should appear only once in order status chart data');
        }
    }
}
