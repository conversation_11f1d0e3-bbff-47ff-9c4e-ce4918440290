<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\EmailService;
use App\Services\InvitationService;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Mockery;
use Tests\TestCase;

final class InvitationServiceTest extends TestCase
{
    use RefreshDatabase;

    private InvitationService $invitationService;
    private PermissionService $permissionService;
    private EmailService $emailService;

    protected function setUp(): void
    {
        parent::setUp();

        // Use real services since they are final classes
        $this->permissionService = app(PermissionService::class);
        $this->emailService = app(EmailService::class);

        $this->invitationService = new InvitationService(
            $this->permissionService,
            $this->emailService
        );
    }

    public function test_send_invitation_email_sends_email_successfully(): void
    {
        // Create required email template or use existing one
        \App\Models\EmailTemplate::firstOrCreate(
            ['code' => 'organization_invitation'],
            [
                'name' => 'Organization Invitation',
                'subject' => 'Organization Invitation',
                'content' => '<p>Hello {user_name}, you are invited to join {organization_name} by {inviter_name}. Click {invitation_link} to join. Expires: {expiry_date}</p>',
                'variables' => ['user_name', 'organization_name', 'inviter_name', 'invitation_link', 'expiry_date'],
                'is_active' => true,
                'description' => 'Template for organization invitations',
            ]
        );

        // Create test data
        $organisation = Organisation::factory()->create([
            'name' => 'Test Organization'
        ]);

        $inviter = User::factory()->create([
            'name' => 'John Inviter'
        ]);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'email_restriction' => '<EMAIL>',
            'expires_at' => Carbon::now()->addWeek(),
        ]);

        // Mock Log facade to handle both success and error cases
        Log::shouldReceive('info')
            ->with('Organization invitation email sent', Mockery::type('array'))
            ->zeroOrMoreTimes();

        Log::shouldReceive('error')
            ->with('Failed to send invitation email', Mockery::type('array'))
            ->zeroOrMoreTimes();

        // Call the method - should not throw exception
        $this->invitationService->sendInvitationEmail($invitation, $inviter);

        // If we reach here, the method executed successfully (either sent email or handled error gracefully)
        $this->assertTrue(true);
    }

    public function test_send_invitation_email_handles_exception_gracefully(): void
    {
        // Don't create email template to force an exception

        // Create test data
        $organisation = Organisation::factory()->create([
            'name' => 'Test Organization'
        ]);

        $inviter = User::factory()->create([
            'name' => 'John Inviter'
        ]);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'email_restriction' => '<EMAIL>',
            'expires_at' => Carbon::now()->addWeek(),
        ]);

        // Mock Log facade for error logging
        Log::shouldReceive('error')
            ->once()
            ->with('Failed to send invitation email', Mockery::type('array'));

        // Call the method - should not throw exception even if email fails
        $this->invitationService->sendInvitationEmail($invitation, $inviter);

        // If we reach here, the method handled the exception gracefully
        $this->assertTrue(true);
    }

    // ========================================
    // createInvitation Tests
    // ========================================

    public function test_create_invitation_success_with_system_admin(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();
        $systemAdmin = User::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        $data = [
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'member',
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'email_restriction' => '<EMAIL>',
        ];

        // Call the method
        $invitation = $this->invitationService->createInvitation($data, $systemAdmin);

        // Assertions
        $this->assertInstanceOf(Invitation::class, $invitation);
        $this->assertEquals(Organisation::class, $invitation->model_type);
        $this->assertEquals($organisation->id, $invitation->model_id);
        $this->assertEquals('member', $invitation->role);
        $this->assertEquals($systemAdmin->id, $invitation->created_by_user_id);
        $this->assertEquals('<EMAIL>', $invitation->email_restriction);
        $this->assertEquals(5, $invitation->max_uses);
    }

    public function test_create_invitation_success_with_organisation_admin(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();
        $orgAdmin = User::factory()->create();

        // Create organisation owner role and assign to user
        $ownerRole = $this->permissionService->createRole('owner', 'api', $organisation->id);
        $this->permissionService->assignRoleToUser($orgAdmin, $ownerRole);
        $orgAdmin->organisations()->attach($organisation->id);

        $data = [
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'member',
        ];

        // Call the method
        $invitation = $this->invitationService->createInvitation($data, $orgAdmin);

        // Assertions
        $this->assertInstanceOf(Invitation::class, $invitation);
        $this->assertEquals('member', $invitation->role);
        $this->assertEquals($orgAdmin->id, $invitation->created_by_user_id);
        $this->assertEquals(1, $invitation->max_uses); // Default value
        $this->assertNotNull($invitation->expires_at);
        $this->assertNull($invitation->email_restriction); // Default value
    }

    public function test_create_invitation_throws_validation_exception_for_unauthorized_user(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();
        $unauthorizedUser = User::factory()->create(); // Regular user with no special roles

        $data = [
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'member',
        ];

        // Expect ValidationException
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_no_permission_create'));

        // Call the method
        $this->invitationService->createInvitation($data, $unauthorizedUser);
    }

    public function test_create_invitation_prevents_system_role_creation_root(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();
        $systemAdmin = User::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        $data = [
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'root', // System role
        ];

        // Expect ValidationException
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_cannot_create_system_roles'));

        // Call the method
        $this->invitationService->createInvitation($data, $systemAdmin);
    }

    public function test_create_invitation_prevents_system_role_creation_admin(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();
        $systemAdmin = User::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        $data = [
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'admin', // System role
        ];

        // Expect ValidationException
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_cannot_create_system_roles'));

        // Call the method
        $this->invitationService->createInvitation($data, $systemAdmin);
    }

    // ========================================
    // acceptInvitation Tests
    // ========================================

    public function test_accept_invitation_success(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create([
            'name' => 'Test Organization'
        ]);

        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create the member role for the organization
        $role = $this->permissionService->createRole('member', 'api', $organisation->id);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'member',
            'email_restriction' => '<EMAIL>',
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 1,
            'uses' => 0,
        ]);

        // Call the method
        $result = $this->invitationService->acceptInvitation($invitation, $user);

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(__('errors.invitation_join_success'), $result['message']);
        $this->assertEquals($organisation->id, $result['organization']['id']);
        $this->assertEquals('Test Organization', $result['organization']['name']);
        $this->assertEquals('member', $result['role']);
        $this->assertArrayHasKey('user_roles', $result);

        // Verify user was added to organization
        $this->assertTrue($user->organisations()->where('organisation_id', $organisation->id)->exists());

        // Verify invitation usage was incremented
        $invitation->refresh();
        $this->assertEquals(1, $invitation->uses);
    }

    public function test_accept_invitation_throws_exception_for_email_restriction(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();

        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'email_restriction' => '<EMAIL>',
        ]);

        // Expect ValidationException
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_email_not_allowed'));

        // Call the method
        $this->invitationService->acceptInvitation($invitation, $user);
    }

    public function test_accept_invitation_throws_exception_for_missing_target_model(): void
    {
        // Create test data
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => 999, // Non-existent ID
            'email_restriction' => '<EMAIL>',
        ]);

        // Expect ValidationException
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_resource_not_exist'));

        // Call the method
        $this->invitationService->acceptInvitation($invitation, $user);
    }

    public function test_accept_invitation_throws_exception_for_unsupported_model_type(): void
    {
        // Create test data
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create a User model to use as unsupported model type
        $unsupportedModel = User::factory()->create();

        $invitation = Invitation::factory()->create([
            'model_type' => User::class, // Using User as unsupported model type
            'model_id' => $unsupportedModel->id,
            'email_restriction' => '<EMAIL>',
        ]);

        // Expect ValidationException
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_unsupported_type'));

        // Call the method
        $this->invitationService->acceptInvitation($invitation, $user);
    }

    public function test_accept_invitation_throws_exception_for_missing_role(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();

        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'nonexistent_role',
            'email_restriction' => '<EMAIL>',
        ]);

        // Expect ValidationException for missing role
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_role_not_exist'));

        // Call the method
        $this->invitationService->acceptInvitation($invitation, $user);
    }

    public function test_accept_invitation_adds_user_to_organisation_if_not_member(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create([
            'name' => 'Test Organization'
        ]);

        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create the member role for the organization
        $role = $this->permissionService->createRole('member', 'api', $organisation->id);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'member',
            'email_restriction' => '<EMAIL>',
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 1,
            'uses' => 0,
        ]);

        // Verify user is not a member of the organization yet
        $this->assertFalse($user->organisations()->where('organisation_id', $organisation->id)->exists());

        // Call the method
        $result = $this->invitationService->acceptInvitation($invitation, $user);

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals(__('errors.invitation_join_success'), $result['message']);

        // Verify user was added to organization
        $this->assertTrue($user->organisations()->where('organisation_id', $organisation->id)->exists());

        // Verify invitation usage was incremented
        $invitation->refresh();
        $this->assertEquals(1, $invitation->uses);
    }

    public function test_accept_invitation_handles_general_exception(): void
    {
        // Create test data
        $organisation = Organisation::factory()->create();

        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'nonexistent_role', // This will cause an exception
            'email_restriction' => '<EMAIL>',
        ]);

        // Expect ValidationException for missing role (which is a type of exception)
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_role_not_exist'));

        // Call the method
        $this->invitationService->acceptInvitation($invitation, $user);
    }

    // ========================================
    // validateInvitationForAcceptance Tests
    // ========================================

    public function test_validate_invitation_for_acceptance_passes_for_valid_invitation(): void
    {
        // Create valid invitation
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Should not throw any exception
        $this->invitationService->validateInvitationForAcceptance($invitation);

        // If we reach here, validation passed
        $this->assertTrue(true);
    }

    public function test_validate_invitation_for_acceptance_throws_exception_for_expired_invitation(): void
    {
        // Create expired invitation
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->subDay(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Expect ValidationException for expired invitation
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_expired'));

        // Call the method
        $this->invitationService->validateInvitationForAcceptance($invitation);
    }

    public function test_validate_invitation_for_acceptance_throws_exception_for_usage_limit_reached(): void
    {
        // Create invitation with usage limit reached
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 5,
        ]);

        // Expect ValidationException for usage limit reached
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_usage_limit_reached'));

        // Call the method
        $this->invitationService->validateInvitationForAcceptance($invitation);
    }

    // ========================================
    // validateInvitationForViewing Tests
    // ========================================

    public function test_validate_invitation_for_viewing_passes_for_valid_invitation(): void
    {
        // Create valid invitation
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Should not throw any exception
        $this->invitationService->validateInvitationForViewing($invitation);

        // If we reach here, validation passed
        $this->assertTrue(true);
    }

    public function test_validate_invitation_for_viewing_throws_exception_for_expired_invitation(): void
    {
        // Create expired invitation
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->subDay(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Expect ValidationException for expired invitation
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_expired'));

        // Call the method
        $this->invitationService->validateInvitationForViewing($invitation);
    }

    public function test_validate_invitation_for_viewing_throws_exception_for_usage_limit_reached(): void
    {
        // Create invitation with usage limit reached
        $invitation = Invitation::factory()->create([
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 5,
        ]);

        // Expect ValidationException for usage limit reached
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__('errors.invitation_usage_limit_reached'));

        // Call the method
        $this->invitationService->validateInvitationForViewing($invitation);
    }

    // ========================================
    // getInvitations Tests
    // ========================================

    public function test_get_invitations_returns_paginated_results_for_system_admin(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create multiple invitations
        $invitations = Invitation::factory()->count(20)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
        ]);

        // Call the method
        $result = $this->invitationService->getInvitations($systemAdmin, 10);

        // Assertions
        $this->assertInstanceOf(\Illuminate\Contracts\Pagination\LengthAwarePaginator::class, $result);
        $this->assertEquals(10, $result->perPage());
        $this->assertEquals(20, $result->total());
        $this->assertEquals(2, $result->lastPage());
    }

    public function test_get_invitations_filters_by_created_user_for_non_system_admin(): void
    {
        // Create test data
        $regularUser = User::factory()->create();
        $otherUser = User::factory()->create();
        $organisation = Organisation::factory()->create();

        // Create organisation owner role and assign to user
        $ownerRole = $this->permissionService->createRole('owner', 'api', $organisation->id);
        $this->permissionService->assignRoleToUser($regularUser, $ownerRole);
        $regularUser->organisations()->attach($organisation->id);

        // Create invitations by different users
        $userInvitations = Invitation::factory()->count(5)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'created_by_user_id' => $regularUser->id,
        ]);

        $otherInvitations = Invitation::factory()->count(3)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'created_by_user_id' => $otherUser->id,
        ]);

        // Call the method
        $result = $this->invitationService->getInvitations($regularUser, 15);

        // Assertions - should only return invitations created by the user
        $this->assertInstanceOf(\Illuminate\Contracts\Pagination\LengthAwarePaginator::class, $result);
        $this->assertEquals(5, $result->total()); // Only user's invitations
    }

    public function test_get_invitations_applies_model_type_filter(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create invitations with different model types
        $orgInvitations = Invitation::factory()->count(3)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
        ]);

        $otherInvitations = Invitation::factory()->count(2)->create([
            'model_type' => 'App\Models\OtherModel',
            'model_id' => 1,
        ]);

        // Call the method with model type filter
        $result = $this->invitationService->getInvitations(
            $systemAdmin,
            15,
            Organisation::class
        );

        // Assertions - should only return Organisation invitations
        $this->assertEquals(3, $result->total());
    }

    public function test_get_invitations_applies_model_id_filter(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation1 = Organisation::factory()->create();
        $organisation2 = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create invitations for different organisations
        $org1Invitations = Invitation::factory()->count(4)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation1->id,
        ]);

        $org2Invitations = Invitation::factory()->count(2)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation2->id,
        ]);

        // Call the method with model ID filter
        $result = $this->invitationService->getInvitations(
            $systemAdmin,
            15,
            null,
            $organisation1->id
        );

        // Assertions - should only return organisation1 invitations
        $this->assertEquals(4, $result->total());
    }

    public function test_get_invitations_applies_role_filter(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create invitations with different roles
        $memberInvitations = Invitation::factory()->count(3)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'member',
        ]);

        $ownerInvitations = Invitation::factory()->count(2)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'owner',
        ]);

        // Call the method with role filter
        $result = $this->invitationService->getInvitations(
            $systemAdmin,
            15,
            null,
            null,
            'member'
        );

        // Assertions - should only return member invitations
        $this->assertEquals(3, $result->total());
    }

    public function test_get_invitations_applies_valid_status_filter(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create valid invitations (not expired, not used up)
        $validInvitations = Invitation::factory()->count(3)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Create expired invitations
        $expiredInvitations = Invitation::factory()->count(2)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'expires_at' => Carbon::now()->subDay(),
            'max_uses' => 5,
            'uses' => 1,
        ]);

        // Call the method with valid status filter
        $result = $this->invitationService->getInvitations(
            $systemAdmin,
            15,
            null,
            null,
            null,
            'valid'
        );

        // Assertions - should only return valid invitations
        $this->assertEquals(3, $result->total());
    }

    public function test_get_invitations_applies_expired_status_filter(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create valid invitations
        $validInvitations = Invitation::factory()->count(2)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Create expired invitations
        $expiredInvitations = Invitation::factory()->count(3)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'expires_at' => Carbon::now()->subDay(),
            'max_uses' => 5,
            'uses' => 1,
        ]);

        // Call the method with expired status filter
        $result = $this->invitationService->getInvitations(
            $systemAdmin,
            15,
            null,
            null,
            null,
            'expired'
        );

        // Assertions - should only return expired invitations
        $this->assertEquals(3, $result->total());
    }

    public function test_get_invitations_applies_used_up_status_filter(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create valid invitations
        $validInvitations = Invitation::factory()->count(2)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Create used up invitations
        $usedUpInvitations = Invitation::factory()->count(3)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 5,
        ]);

        // Call the method with used_up status filter
        $result = $this->invitationService->getInvitations(
            $systemAdmin,
            15,
            null,
            null,
            null,
            'used_up'
        );

        // Assertions - should only return used up invitations
        $this->assertEquals(3, $result->total());
    }

    public function test_get_invitations_applies_multiple_filters(): void
    {
        // Create test data
        $systemAdmin = User::factory()->create();
        $organisation1 = Organisation::factory()->create();
        $organisation2 = Organisation::factory()->create();

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($systemAdmin, $adminRole);

        // Create matching invitations (Organisation1, member role, valid)
        $matchingInvitations = Invitation::factory()->count(2)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation1->id,
            'role' => 'member',
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Create non-matching invitations
        $nonMatchingInvitations = Invitation::factory()->count(3)->create([
            'model_type' => Organisation::class,
            'model_id' => $organisation2->id, // Different organisation
            'role' => 'member',
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        // Call the method with multiple filters
        $result = $this->invitationService->getInvitations(
            $systemAdmin,
            15,
            Organisation::class,
            $organisation1->id,
            'member',
            'valid'
        );

        // Assertions - should only return matching invitations
        $this->assertEquals(2, $result->total());
    }
}
