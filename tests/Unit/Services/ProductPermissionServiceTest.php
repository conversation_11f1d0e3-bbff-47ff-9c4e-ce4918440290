<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use App\Services\ProductPermissionService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use InvalidArgumentException;
use Tests\TestCase;

final class ProductPermissionServiceTest extends TestCase
{
    use RefreshDatabase;

    private ProductPermissionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ProductPermissionService();
    }

    public function test_grant_product_access(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();
        $grantedBy = User::factory()->create();
        $expiresAt = Carbon::now()->addMonth();

        $permission = $this->service->grantProductAccess(
            user: $user,
            product: $product,
            permissionType: 'view-reports',
            grantedBy: $grantedBy,
            expiresAt: $expiresAt,
            notes: 'Service test'
        );

        $this->assertInstanceOf(ProductPermission::class, $permission);
        $this->assertEquals($user->id, $permission->user_id);
        $this->assertEquals($product->id, $permission->product_id);
        $this->assertEquals('view-reports', $permission->permission_type);
        $this->assertEquals($grantedBy->id, $permission->granted_by);
        $this->assertEquals('Service test', $permission->notes);
    }

    public function test_grant_product_access_throws_exception_for_non_existent_product(): void
    {
        $user = User::factory()->create();
        $product = new Product(); // Non-existent product

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Product does not exist');

        $this->service->grantProductAccess($user, $product);
    }

    public function test_revoke_product_access(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        // Grant permission first
        $this->service->grantProductAccess($user, $product);
        $this->assertCount(1, $product->productPermissions);

        // Revoke permission
        $revoked = $this->service->revokeProductAccess($user, $product);

        $this->assertTrue($revoked);
        $this->assertCount(0, $product->fresh()->productPermissions);
    }

    public function test_get_user_accessible_products(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        // Grant permissions to some products
        $this->service->grantProductAccess($user, $product1);
        $this->service->grantProductAccess($user, $product2);

        $accessibleProducts = $this->service->getUserAccessibleProducts($user);

        $this->assertCount(2, $accessibleProducts);
        $this->assertTrue($accessibleProducts->contains('id', $product1->id));
        $this->assertTrue($accessibleProducts->contains('id', $product2->id));
        $this->assertFalse($accessibleProducts->contains('id', $product3->id));
    }

    public function test_get_product_authorized_users(): void
    {
        $product = Product::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create();

        // Grant permissions to some users
        $this->service->grantProductAccess($user1, $product);
        $this->service->grantProductAccess($user2, $product);

        $authorizedUsers = $this->service->getProductAuthorizedUsers($product);

        $this->assertCount(2, $authorizedUsers);
        $this->assertTrue($authorizedUsers->contains('id', $user1->id));
        $this->assertTrue($authorizedUsers->contains('id', $user2->id));
        $this->assertFalse($authorizedUsers->contains('id', $user3->id));
    }

    public function test_grant_multiple_product_access(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();
        $grantedBy = User::factory()->create();

        $permissions = $this->service->grantMultipleProductAccess(
            user: $user,
            productIds: [$product1->id, $product2->id, $product3->id],
            permissionType: 'view-reports',
            grantedBy: $grantedBy,
            notes: 'Bulk grant'
        );

        $this->assertCount(3, $permissions);
        $this->assertDatabaseCount('product_permissions', 3);

        foreach ($permissions as $permission) {
            $this->assertEquals($user->id, $permission->user_id);
            $this->assertEquals('view-reports', $permission->permission_type);
            $this->assertEquals($grantedBy->id, $permission->granted_by);
            $this->assertEquals('Bulk grant', $permission->notes);
        }
    }

    public function test_revoke_multiple_product_access(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        // Grant permissions first
        $this->service->grantProductAccess($user, $product1);
        $this->service->grantProductAccess($user, $product2);
        $this->service->grantProductAccess($user, $product3);

        $this->assertDatabaseCount('product_permissions', 3);

        // Revoke multiple permissions
        $revokedCount = $this->service->revokeMultipleProductAccess(
            $user,
            [$product1->id, $product2->id]
        );

        $this->assertEquals(2, $revokedCount);
        $this->assertDatabaseCount('product_permissions', 1);
    }

    public function test_user_has_product_access(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $this->assertFalse($this->service->userHasProductAccess($user, $product));

        $this->service->grantProductAccess($user, $product);

        $this->assertTrue($this->service->userHasProductAccess($user, $product));
    }

    public function test_get_expiring_permissions(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        // Create permission expiring in 3 days
        ProductPermission::create([
            'user_id' => $user1->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->addDays(3),
        ]);

        // Create permission expiring in 10 days (outside default 7-day window)
        ProductPermission::create([
            'user_id' => $user2->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->addDays(10),
        ]);

        // Create never-expiring permission
        ProductPermission::create([
            'user_id' => $user1->id,
            'product_id' => $product3->id,
            'permission_type' => 'view-reports',
            'expires_at' => null,
        ]);

        $expiringPermissions = $this->service->getExpiringPermissions();

        $this->assertCount(1, $expiringPermissions);
        $this->assertEquals($product1->id, $expiringPermissions->first()->product_id);
    }

    public function test_cleanup_expired_permissions(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        // Create expired permission
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDay(),
        ]);

        // Create valid permission
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->addDay(),
        ]);

        $this->assertDatabaseCount('product_permissions', 2);

        $cleanedCount = $this->service->cleanupExpiredPermissions();

        $this->assertEquals(1, $cleanedCount);
        $this->assertDatabaseCount('product_permissions', 1);
    }

    public function test_get_user_permission_stats(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        // Create various permissions
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
        ]);

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'edit-reports',
        ]);

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product3->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->addDays(3), // Expiring soon
        ]);

        $stats = $this->service->getUserPermissionStats($user);

        $this->assertEquals(3, $stats['total_permissions']);
        $this->assertEquals(2, $stats['by_type']['view-reports']);
        $this->assertEquals(1, $stats['by_type']['edit-reports']);
        $this->assertEquals(1, $stats['expiring_soon']);
    }
}
