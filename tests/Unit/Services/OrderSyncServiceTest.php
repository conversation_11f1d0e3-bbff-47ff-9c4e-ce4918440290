<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Contracts\OrderSyncServiceInterface;
use App\Services\OrderSyncService;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Unit tests for OrderSyncService
 *
 * Complex integration tests have been moved to OrderSyncServiceIntegrationTest
 * This file contains only simple unit tests that don't require complex mocking.
 */
final class OrderSyncServiceTest extends TestCase
{
    use RefreshDatabase;

    private OrderSyncServiceInterface $orderSyncService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orderSyncService = app(OrderSyncServiceInterface::class);
    }

    public function test_order_sync_service_can_be_resolved(): void
    {
        $service = app(OrderSyncServiceInterface::class);
        $this->assertInstanceOf(OrderSyncService::class, $service);
    }

    // This test has been moved to OrderSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to OrderSyncServiceIntegrationTest as it requires complex database operations

    public function test_get_sync_statistics_returns_array(): void
    {
        $this->orderSyncService = app(OrderSyncServiceInterface::class);
        $statistics = $this->orderSyncService->getSyncStatistics();

        $this->assertIsArray($statistics);
        $this->assertArrayHasKey('total_syncs', $statistics);
        $this->assertArrayHasKey('successful_syncs', $statistics);
        $this->assertArrayHasKey('failed_syncs', $statistics);
        $this->assertArrayHasKey('success_rate', $statistics);
        $this->assertArrayHasKey('last_sync', $statistics);
    }

    public function test_cleanup_returns_cleanup_results(): void
    {
        // Create some test data first to avoid lock timeout issues
        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test_cleanup_batch',
            'status' => 'completed',
            'started_at' => now()->subDays(2),
            'completed_at' => now()->subDays(2)->addMinutes(30),
            'sync_config' => ['test' => true],
        ]);

        $this->orderSyncService = app(OrderSyncServiceInterface::class);

        // Use a longer retention period to avoid deleting data that might be locked
        $results = $this->orderSyncService->cleanup(30);

        $this->assertIsArray($results);
        $this->assertArrayHasKey('records_deleted', $results);
        $this->assertArrayHasKey('logs_deleted', $results);
        $this->assertArrayHasKey('cutoff_date', $results);

        // Results should be non-negative integers
        $this->assertGreaterThanOrEqual(0, $results['records_deleted']);
        $this->assertGreaterThanOrEqual(0, $results['logs_deleted']);
    }

    // This test has been moved to OrderSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to OrderSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to OrderSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to OrderSyncServiceIntegrationTest as it requires complex database operations

    // This test has been moved to OrderSyncServiceIntegrationTest as it requires complex database operations



    public function test_data_transformation_methods(): void
    {
        $orderSyncService = new OrderSyncService();

        // Test order state mapping
        $reflection = new \ReflectionClass($orderSyncService);
        $mapOrderStateMethod = $reflection->getMethod('mapOrderState');
        $mapOrderStateMethod->setAccessible(true);

        $this->assertEquals('completed', $mapOrderStateMethod->invoke($orderSyncService, 'fulfilled'));
        $this->assertEquals('cancelled', $mapOrderStateMethod->invoke($orderSyncService, 'cancelled'));
        $this->assertEquals('new', $mapOrderStateMethod->invoke($orderSyncService, 'new'));
        $this->assertEquals('unknown_state', $mapOrderStateMethod->invoke($orderSyncService, 'unknown_state'));

        // Test currency conversion
        $convertToMinorUnitsMethod = $reflection->getMethod('convertToMinorUnits');
        $convertToMinorUnitsMethod->setAccessible(true);

        $this->assertEquals(1000, $convertToMinorUnitsMethod->invoke($orderSyncService, 1000));
        $this->assertEquals(0, $convertToMinorUnitsMethod->invoke($orderSyncService, null));
        $this->assertEquals(2500, $convertToMinorUnitsMethod->invoke($orderSyncService, '2500'));
    }

    // This method has been moved to OrderSyncServiceIntegrationTest

    // This method has been moved to OrderSyncServiceIntegrationTest

    // This method has been moved to OrderSyncServiceIntegrationTest

    /**
     * Test that sync records have consistent field structure for both success and failed records.
     * This test verifies the fix for the column count mismatch error.
     */
    public function test_sync_records_have_consistent_field_structure(): void
    {
        // Create a sync log first
        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test-batch-123',
            'status' => 'pending',
            'started_at' => now(),
            'sync_config' => [],
        ]);

        // Test data for sync records with both success and failed records
        $syncRecords = [
            // Success record
            [
                'batch_id' => 'test-batch-123',
                'source_table' => 'sylius_order',
                'source_id' => '1001',
                'target_table' => 'orders',
                'target_id' => null,
                'status' => 'pending',
                'source_data' => json_encode(['order_id' => 1001]),
                'transformed_data' => json_encode(['store_order_id' => 1001]),
                'error_message' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // Failed record
            [
                'batch_id' => 'test-batch-123',
                'source_table' => 'sylius_order',
                'source_id' => '1002',
                'target_table' => 'orders',
                'target_id' => null,
                'status' => 'failed',
                'source_data' => json_encode(['order_id' => 1002]),
                'transformed_data' => null,
                'error_message' => 'Test error message',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // This should not throw a "Column count doesn't match" error
        $this->expectNotToPerformAssertions();
        SyncRecord::insert($syncRecords);
    }

    /**
     * Test that long error messages can be stored without truncation.
     * This test verifies the fix for the string data truncation error.
     */
    public function test_long_error_messages_can_be_stored(): void
    {
        // Create a very long error message
        $longErrorMessage = str_repeat('This is a very long error message that would exceed the TEXT field limit. ', 1000);

        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test-batch-456',
            'status' => 'failed',
            'started_at' => now(),
            'completed_at' => now(),
            'error_message' => $longErrorMessage,
            'sync_config' => [],
        ]);

        $this->assertDatabaseHas('sync_logs', [
            'batch_id' => 'test-batch-456',
            'status' => 'failed',
        ]);

        // Verify the full error message was stored
        $storedLog = SyncLog::where('batch_id', 'test-batch-456')->first();
        $this->assertEquals($longErrorMessage, $storedLog->error_message);
    }

    /**
     * Test that sync records can store long error messages.
     * This test verifies the fix for the string data truncation error in sync_records table.
     */
    public function test_sync_records_can_store_long_error_messages(): void
    {
        $longErrorMessage = str_repeat('This is a very long error message for sync record. ', 500);

        $syncRecord = SyncRecord::create([
            'batch_id' => 'test-batch-789',
            'source_table' => 'sylius_order',
            'source_id' => '2001',
            'target_table' => 'orders',
            'target_id' => null,
            'status' => 'failed',
            'error_message' => $longErrorMessage,
            'source_data' => ['order_id' => 2001],
            'transformed_data' => null,
        ]);

        $this->assertDatabaseHas('sync_records', [
            'batch_id' => 'test-batch-789',
            'source_id' => '2001',
            'status' => 'failed',
        ]);

        // Verify the full error message was stored
        $storedRecord = SyncRecord::where('batch_id', 'test-batch-789')->first();
        $this->assertEquals($longErrorMessage, $storedRecord->error_message);
    }
}
