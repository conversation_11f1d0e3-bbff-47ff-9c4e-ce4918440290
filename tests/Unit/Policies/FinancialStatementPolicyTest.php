<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\FinancialStatement;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Policies\FinancialStatementPolicy;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class FinancialStatementPolicyTest extends TestCase
{
    use RefreshDatabase;

    private FinancialStatementPolicy $policy;
    private PermissionService $permissionService;
    private User $systemAdmin;
    private User $organisationOwner;
    private User $organisationMember;
    private User $unauthorizedUser;
    private Organisation $organisation;
    private Organisation $otherOrganisation;
    private FinancialStatement $financialStatement;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new FinancialStatementPolicy();
        $this->permissionService = app(PermissionService::class);

        // Create organisations
        $this->organisation = Organisation::factory()->create();
        $this->otherOrganisation = Organisation::factory()->create();

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $memberRole = collect($orgRoles)->firstWhere('name', 'member');

        // Create system admin
        $this->systemAdmin = User::factory()->create();
        $this->permissionService->assignRoleToUser($this->systemAdmin, $systemAdminRole);

        // Create organisation owner
        $this->organisationOwner = User::factory()->create();
        $this->organisationOwner->organisations()->attach($this->organisation->id);
        $this->permissionService->assignRoleToUser($this->organisationOwner, $ownerRole);

        // Create organisation member
        $this->organisationMember = User::factory()->create();
        $this->organisationMember->organisations()->attach($this->organisation->id);
        $this->permissionService->assignRoleToUser($this->organisationMember, $memberRole);

        // Create unauthorized user
        $this->unauthorizedUser = User::factory()->create();

        // Create financial statement
        $this->financialStatement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);
    }

    public function test_view_any_allows_system_admin(): void
    {
        $result = $this->policy->viewAny($this->systemAdmin);

        $this->assertTrue($result);
    }

    public function test_view_any_allows_organisation_owner(): void
    {
        $result = $this->policy->viewAny($this->organisationOwner);

        $this->assertTrue($result);
    }

    public function test_view_any_allows_organisation_member(): void
    {
        $result = $this->policy->viewAny($this->organisationMember);

        $this->assertTrue($result);
    }

    public function test_view_any_denies_unauthorized_user(): void
    {
        $result = $this->policy->viewAny($this->unauthorizedUser);

        $this->assertFalse($result);
    }

    public function test_view_allows_system_admin(): void
    {
        $result = $this->policy->view($this->systemAdmin, $this->financialStatement);

        $this->assertTrue($result);
    }

    public function test_view_allows_organisation_owner(): void
    {
        $result = $this->policy->view($this->organisationOwner, $this->financialStatement);

        $this->assertTrue($result);
    }

    public function test_view_allows_organisation_member(): void
    {
        $result = $this->policy->view($this->organisationMember, $this->financialStatement);

        $this->assertTrue($result);
    }

    public function test_view_denies_user_from_different_organisation(): void
    {
        $otherStatement = FinancialStatement::factory()->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $result = $this->policy->view($this->organisationOwner, $otherStatement);

        $this->assertFalse($result);
    }

    public function test_view_denies_unauthorized_user(): void
    {
        $result = $this->policy->view($this->unauthorizedUser, $this->financialStatement);

        $this->assertFalse($result);
    }

    public function test_create_allows_system_admin(): void
    {
        $result = $this->policy->create($this->systemAdmin);

        $this->assertTrue($result);
    }

    public function test_create_denies_organisation_owner(): void
    {
        $result = $this->policy->create($this->organisationOwner);

        $this->assertFalse($result);
    }

    public function test_create_denies_organisation_member(): void
    {
        $result = $this->policy->create($this->organisationMember);

        $this->assertFalse($result);
    }

    public function test_create_denies_unauthorized_user(): void
    {
        $result = $this->policy->create($this->unauthorizedUser);

        $this->assertFalse($result);
    }

    public function test_update_allows_system_admin(): void
    {
        $result = $this->policy->update($this->systemAdmin, $this->financialStatement);

        $this->assertTrue($result);
    }

    public function test_update_denies_organisation_owner(): void
    {
        $result = $this->policy->update($this->organisationOwner, $this->financialStatement);

        $this->assertFalse($result);
    }

    public function test_update_denies_organisation_member(): void
    {
        $result = $this->policy->update($this->organisationMember, $this->financialStatement);

        $this->assertFalse($result);
    }

    public function test_delete_allows_system_admin_for_pending_audit(): void
    {
        $result = $this->policy->delete($this->systemAdmin, $this->financialStatement);

        $this->assertTrue($result);
    }

    public function test_delete_denies_organisation_owner(): void
    {
        $result = $this->policy->delete($this->organisationOwner, $this->financialStatement);

        $this->assertFalse($result);
    }

    public function test_delete_denies_for_published_statement(): void
    {
        $publishedStatement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PUBLISHED,
        ]);

        $result = $this->policy->delete($this->systemAdmin, $publishedStatement);

        $this->assertFalse($result);
    }

    public function test_publish_allows_system_admin(): void
    {
        $result = $this->policy->publish($this->systemAdmin, $this->financialStatement);

        $this->assertTrue($result);
    }

    public function test_publish_denies_organisation_owner(): void
    {
        $result = $this->policy->publish($this->organisationOwner, $this->financialStatement);

        $this->assertFalse($result);
    }

    public function test_publish_denies_organisation_member(): void
    {
        $result = $this->policy->publish($this->organisationMember, $this->financialStatement);

        $this->assertFalse($result);
    }

    public function test_view_by_organisation_allows_system_admin(): void
    {
        $result = $this->policy->viewByOrganisation($this->systemAdmin, $this->organisation->id);

        $this->assertTrue($result);
    }

    public function test_view_by_organisation_allows_organisation_member(): void
    {
        $result = $this->policy->viewByOrganisation($this->organisationOwner, $this->organisation->id);

        $this->assertTrue($result);
    }

    public function test_view_by_organisation_denies_user_from_different_organisation(): void
    {
        $result = $this->policy->viewByOrganisation($this->organisationOwner, $this->otherOrganisation->id);

        $this->assertFalse($result);
    }

    public function test_view_statistics_allows_system_admin(): void
    {
        $result = $this->policy->viewStatistics($this->systemAdmin);

        $this->assertTrue($result);
    }

    public function test_view_statistics_allows_organisation_member(): void
    {
        $result = $this->policy->viewStatistics($this->organisationOwner);

        $this->assertTrue($result);
    }

    public function test_view_statistics_with_organisation_id_allows_member_of_that_organisation(): void
    {
        $result = $this->policy->viewStatistics($this->organisationOwner, $this->organisation->id);

        $this->assertTrue($result);
    }

    public function test_view_statistics_with_organisation_id_denies_non_member(): void
    {
        $result = $this->policy->viewStatistics($this->organisationOwner, $this->otherOrganisation->id);

        $this->assertFalse($result);
    }

    public function test_create_for_organisation_allows_system_admin(): void
    {
        $result = $this->policy->createForOrganisation($this->systemAdmin, $this->organisation->id);

        $this->assertTrue($result);
    }

    public function test_create_for_organisation_denies_organisation_owner(): void
    {
        $result = $this->policy->createForOrganisation($this->organisationOwner, $this->organisation->id);

        $this->assertFalse($result);
    }

    public function test_create_for_organisation_denies_organisation_member(): void
    {
        $result = $this->policy->createForOrganisation($this->organisationMember, $this->organisation->id);

        $this->assertFalse($result);
    }
}
