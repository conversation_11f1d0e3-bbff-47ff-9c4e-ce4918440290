<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\User;
use App\Policies\ProductPolicy;
use App\Services\PermissionService;
use App\Services\ProductPermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Product Policy Test
 * 
 * Tests authorization logic for product viewing operations.
 * Ensures proper access control based on user roles and organization membership.
 */
final class ProductPolicyTest extends TestCase
{
    use RefreshDatabase;

    private ProductPolicy $policy;
    private PermissionService $permissionService;
    private ProductPermissionService $productPermissionService;
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $organisationOwnerUser;
    private User $organisationMemberUser;
    private User $userWithProductPermission;
    private User $regularUser;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;
    private Product $product;
    private Product $anotherProduct;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new ProductPolicy();
        $this->permissionService = app(PermissionService::class);
        $this->productPermissionService = app(ProductPermissionService::class);

        // Create test organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'code' => 'TEST002',
            'status' => 'active',
        ]);

        // Create test products
        $this->product = Product::factory()->create([
            'owner_id' => $this->organisation->code,
            'name' => 'Test Product',
            'code' => 'TEST-PRODUCT-001',
            'enabled' => true,
        ]);

        $this->anotherProduct = Product::factory()->create([
            'owner_id' => $this->anotherOrganisation->code,
            'name' => 'Another Product',
            'code' => 'TEST-PRODUCT-002',
            'enabled' => true,
        ]);

        // Create system users
        $this->systemRootUser = User::factory()->create();
        $rootRole = $this->permissionService->createRole('root', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->guard_name = 'system';
        $this->systemRootUser->assignRole($rootRole);

        $this->systemAdminUser = User::factory()->create();
        $adminRole = $this->permissionService->createRole('admin', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($adminRole);

        // Create organisation users
        $this->organisationOwnerUser = User::factory()->create();
        $this->organisationOwnerUser->organisations()->attach($this->organisation->id);
        $ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->organisationOwnerUser->guard_name = 'api';
        $this->organisationOwnerUser->assignRole($ownerRole);

        $this->organisationMemberUser = User::factory()->create();
        $this->organisationMemberUser->organisations()->attach($this->organisation->id);
        $memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->organisationMemberUser->guard_name = 'api';
        $this->organisationMemberUser->assignRole($memberRole);

        // Create user with only product-level permissions
        $this->userWithProductPermission = User::factory()->create();

        // Create regular user with no permissions
        $this->regularUser = User::factory()->create();
    }

    // ========================================
    // viewAny Tests
    // ========================================

    public function test_viewAny_allows_system_root(): void
    {
        $this->assertTrue($this->policy->viewAny($this->systemRootUser));
    }

    public function test_viewAny_allows_system_admin(): void
    {
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser));
    }

    public function test_viewAny_allows_organisation_owner(): void
    {
        $this->assertTrue($this->policy->viewAny($this->organisationOwnerUser));
    }

    public function test_viewAny_allows_organisation_member(): void
    {
        $this->assertTrue($this->policy->viewAny($this->organisationMemberUser));
    }

    public function test_viewAny_denies_user_with_only_product_permission(): void
    {
        // Grant product permission to user
        $this->productPermissionService->grantProductAccess($this->userWithProductPermission, $this->product);
        
        // User with only product permission should not be able to list all products
        $this->assertFalse($this->policy->viewAny($this->userWithProductPermission));
    }

    public function test_viewAny_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->viewAny($this->regularUser));
    }

    // ========================================
    // view Tests
    // ========================================

    public function test_view_allows_system_root_for_any_product(): void
    {
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->product));
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->anotherProduct));
    }

    public function test_view_allows_system_admin_for_any_product(): void
    {
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->product));
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->anotherProduct));
    }

    public function test_view_allows_organisation_owner_for_own_product(): void
    {
        $this->assertTrue($this->policy->view($this->organisationOwnerUser, $this->product));
    }

    public function test_view_denies_organisation_owner_for_other_product(): void
    {
        $this->assertFalse($this->policy->view($this->organisationOwnerUser, $this->anotherProduct));
    }

    public function test_view_allows_organisation_member_for_own_product(): void
    {
        $this->assertTrue($this->policy->view($this->organisationMemberUser, $this->product));
    }

    public function test_view_denies_organisation_member_for_other_product(): void
    {
        $this->assertFalse($this->policy->view($this->organisationMemberUser, $this->anotherProduct));
    }

    public function test_view_allows_user_with_product_permission(): void
    {
        // Grant product permission to user
        $this->productPermissionService->grantProductAccess($this->userWithProductPermission, $this->product);
        
        $this->assertTrue($this->policy->view($this->userWithProductPermission, $this->product));
    }

    public function test_view_denies_user_without_product_permission(): void
    {
        $this->assertFalse($this->policy->view($this->userWithProductPermission, $this->product));
    }

    public function test_view_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->view($this->regularUser, $this->product));
        $this->assertFalse($this->policy->view($this->regularUser, $this->anotherProduct));
    }
}
