<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\User;
use App\Policies\ActivityLogPolicy;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Activitylog\Models\Activity;
use Tests\TestCase;

/**
 * Activity Log Policy Test
 *
 * Tests the ActivityLogPolicy authorization logic.
 * Verifies that only system administrators can access activity logs.
 * Only tests viewAny method as ActivityLogController only has index endpoint.
 */
final class ActivityLogPolicyTest extends TestCase
{
    use RefreshDatabase;

    private ActivityLogPolicy $policy;
    private User $rootUser;
    private User $adminUser;
    private User $orgOwner;
    private User $regularUser;
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new ActivityLogPolicy();
        $this->permissionService = app(PermissionService::class);

        // Create test users
        $this->rootUser = User::factory()->create(['name' => 'Root User']);
        $this->adminUser = User::factory()->create(['name' => 'Admin User']);
        $this->orgOwner = User::factory()->create(['name' => 'Org Owner']);
        $this->regularUser = User::factory()->create(['name' => 'Regular User']);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $rootRole = collect($systemRoles)->firstWhere('name', 'root');
        $adminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($rootRole);
        
        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($adminRole);

        // Create organisation and assign owner role
        $organisation = Organisation::factory()->create();
        $orgRoles = $this->permissionService->createDefaultRoles($organisation->id);
        $ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->orgOwner->organisations()->attach($organisation->id);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisation->id);
        $this->orgOwner->assignRole($ownerRole);

        // Clear caches
        $this->rootUser->clearSystemAdminAccessCache();
        $this->adminUser->clearSystemAdminAccessCache();
        $this->orgOwner->clearSystemAdminAccessCache();
        $this->regularUser->clearSystemAdminAccessCache();
    }

    public function test_viewAny_allows_root_user(): void
    {
        $this->assertTrue($this->policy->viewAny($this->rootUser));
    }

    public function test_viewAny_allows_admin_user(): void
    {
        $this->assertTrue($this->policy->viewAny($this->adminUser));
    }

    public function test_viewAny_denies_organisation_owner(): void
    {
        $this->assertFalse($this->policy->viewAny($this->orgOwner));
    }

    public function test_viewAny_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->viewAny($this->regularUser));
    }
}
