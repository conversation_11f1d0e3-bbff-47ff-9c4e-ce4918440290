<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use App\Policies\ProductPermissionPolicy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

/**
 * Product Permission Policy Test
 * 
 * Tests authorization logic for product permission management operations.
 */
final class ProductPermissionPolicyTest extends TestCase
{
    use RefreshDatabase;

    private ProductPermissionPolicy $policy;
    private User $rootUser;
    private User $adminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $regularUser;
    private Organisation $organisation;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new ProductPermissionPolicy();

        // Create system roles
        Role::firstOrCreate(['name' => 'root', 'guard_name' => 'system']);
        Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'system']);

        // Create organisation
        $this->organisation = Organisation::factory()->create();

        // Create organisation roles
        Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);
        Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Create product
        $this->product = Product::factory()->ownedBy($this->organisation->code)->create();

        // Create users with different roles
        $this->rootUser = User::factory()->create();
        $this->adminUser = User::factory()->create();
        $this->ownerUser = User::factory()->create();
        $this->memberUser = User::factory()->create();
        $this->regularUser = User::factory()->create();

        // Assign system roles (need to use system guard)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole('root');
        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole('admin');

        // Associate users with organisation and assign roles
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole('owner');
        $this->memberUser->assignRole('member');
    }

    public function test_view_any_allows_system_administrators(): void
    {
        $this->assertTrue($this->policy->viewAny($this->rootUser));
        $this->assertTrue($this->policy->viewAny($this->adminUser));
        $this->assertFalse($this->policy->viewAny($this->ownerUser));
        $this->assertFalse($this->policy->viewAny($this->memberUser));
        $this->assertFalse($this->policy->viewAny($this->regularUser));
    }

    public function test_view_permission_authorization(): void
    {
        $permission = ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        // Load the relationships
        $permission->load('product.organisation');

        // System administrators can view any permission
        $this->assertTrue($this->policy->view($this->rootUser, $permission));
        $this->assertTrue($this->policy->view($this->adminUser, $permission));

        // Organization owners can view permissions for their organization's products
        $this->assertTrue($this->policy->view($this->ownerUser, $permission));

        // Users can view their own permissions
        $this->assertTrue($this->policy->view($this->regularUser, $permission));

        // Other users cannot view the permission
        $this->assertFalse($this->policy->view($this->memberUser, $permission));
    }

    public function test_grant_product_access_authorization(): void
    {
        // System administrators can grant access to any product
        $this->assertTrue($this->policy->grantProductAccess($this->rootUser, $this->product, $this->regularUser));
        $this->assertTrue($this->policy->grantProductAccess($this->adminUser, $this->product, $this->regularUser));

        // Organization owners can grant access to their organization's products
        $this->assertTrue($this->policy->grantProductAccess($this->ownerUser, $this->product, $this->regularUser));

        // Other users cannot grant access
        $this->assertFalse($this->policy->grantProductAccess($this->memberUser, $this->product, $this->regularUser));
        $this->assertFalse($this->policy->grantProductAccess($this->regularUser, $this->product, $this->memberUser));
    }

    public function test_revoke_product_access_authorization(): void
    {
        // System administrators can revoke access from any product
        $this->assertTrue($this->policy->revokeProductAccess($this->rootUser, $this->product, $this->regularUser));
        $this->assertTrue($this->policy->revokeProductAccess($this->adminUser, $this->product, $this->regularUser));

        // Organization owners can revoke access from their organization's products
        $this->assertTrue($this->policy->revokeProductAccess($this->ownerUser, $this->product, $this->regularUser));

        // Other users cannot revoke access
        $this->assertFalse($this->policy->revokeProductAccess($this->memberUser, $this->product, $this->regularUser));
        $this->assertFalse($this->policy->revokeProductAccess($this->regularUser, $this->product, $this->memberUser));
    }

    public function test_view_product_permissions_authorization(): void
    {
        // System administrators can view permissions for any product
        $this->assertTrue($this->policy->viewProductPermissions($this->rootUser, $this->product));
        $this->assertTrue($this->policy->viewProductPermissions($this->adminUser, $this->product));

        // Organization owners can view permissions for their organization's products
        $this->assertTrue($this->policy->viewProductPermissions($this->ownerUser, $this->product));

        // Other users cannot view product permissions
        $this->assertFalse($this->policy->viewProductPermissions($this->memberUser, $this->product));
        $this->assertFalse($this->policy->viewProductPermissions($this->regularUser, $this->product));
    }

    public function test_view_accessible_products_allows_all_authenticated_users(): void
    {
        $this->assertTrue($this->policy->viewAccessibleProducts($this->rootUser));
        $this->assertTrue($this->policy->viewAccessibleProducts($this->adminUser));
        $this->assertTrue($this->policy->viewAccessibleProducts($this->ownerUser));
        $this->assertTrue($this->policy->viewAccessibleProducts($this->memberUser));
        $this->assertTrue($this->policy->viewAccessibleProducts($this->regularUser));
    }

    public function test_view_user_product_permissions_authorization(): void
    {
        // System administrators can view any user's permissions
        $this->assertTrue($this->policy->viewUserProductPermissions($this->rootUser, $this->regularUser));
        $this->assertTrue($this->policy->viewUserProductPermissions($this->adminUser, $this->regularUser));

        // Users can view their own permissions
        $this->assertTrue($this->policy->viewUserProductPermissions($this->regularUser, $this->regularUser));

        // Organization owners can view permissions of users in their organizations
        $this->assertTrue($this->policy->viewUserProductPermissions($this->ownerUser, $this->memberUser));

        // Users cannot view other users' permissions unless they share an organization
        $this->assertFalse($this->policy->viewUserProductPermissions($this->regularUser, $this->memberUser));
    }

    public function test_grant_multiple_product_access_authorization(): void
    {
        $productIds = [$this->product->id];

        // System administrators can grant access to any products
        $this->assertTrue($this->policy->grantMultipleProductAccess($this->rootUser, $productIds, $this->regularUser));
        $this->assertTrue($this->policy->grantMultipleProductAccess($this->adminUser, $productIds, $this->regularUser));

        // Organization owners can grant access to their organization's products
        $this->assertTrue($this->policy->grantMultipleProductAccess($this->ownerUser, $productIds, $this->regularUser));

        // Other users cannot grant multiple access
        $this->assertFalse($this->policy->grantMultipleProductAccess($this->memberUser, $productIds, $this->regularUser));
        $this->assertFalse($this->policy->grantMultipleProductAccess($this->regularUser, $productIds, $this->memberUser));
    }

    public function test_revoke_multiple_product_access_authorization(): void
    {
        $productIds = [$this->product->id];

        // System administrators can revoke access from any products
        $this->assertTrue($this->policy->revokeMultipleProductAccess($this->rootUser, $productIds, $this->regularUser));
        $this->assertTrue($this->policy->revokeMultipleProductAccess($this->adminUser, $productIds, $this->regularUser));

        // Organization owners can revoke access from their organization's products
        $this->assertTrue($this->policy->revokeMultipleProductAccess($this->ownerUser, $productIds, $this->regularUser));

        // Other users cannot revoke multiple access
        $this->assertFalse($this->policy->revokeMultipleProductAccess($this->memberUser, $productIds, $this->regularUser));
        $this->assertFalse($this->policy->revokeMultipleProductAccess($this->regularUser, $productIds, $this->memberUser));
    }

    public function test_cross_organization_access_denied(): void
    {
        // Create another organization and product
        $otherOrganisation = Organisation::factory()->create();
        $otherProduct = Product::factory()->ownedBy($otherOrganisation->code)->create();

        // Organization owner cannot grant access to products from other organizations
        $this->assertFalse($this->policy->grantProductAccess($this->ownerUser, $otherProduct, $this->regularUser));
        $this->assertFalse($this->policy->revokeProductAccess($this->ownerUser, $otherProduct, $this->regularUser));
        $this->assertFalse($this->policy->viewProductPermissions($this->ownerUser, $otherProduct));
    }
}
