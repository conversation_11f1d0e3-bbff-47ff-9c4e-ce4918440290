<?php

declare(strict_types=1);

namespace Tests\Unit\Notifications;

use App\Models\EmailTemplate;
use App\Models\User;
use App\Notifications\EmailNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Notifications\Messages\MailMessage;
use InvalidArgumentException;
use Tests\TestCase;

final class EmailNotificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_email_notification_with_template(): void
    {
        $notification = new EmailNotification(
            'test_template',
            ['name' => 'John Doe'],
        );

        $this->assertEquals(['mail'], $notification->via(new User()));
    }

    public function test_can_create_email_notification_with_custom_content(): void
    {
        $notification = new EmailNotification(
            'test_template',
            [],
            'Custom Subject',
            '<p>Custom Content</p>'
        );

        $this->assertEquals(['mail'], $notification->via(new User()));
    }

    public function test_to_mail_builds_template_mail(): void
    {
        // Create a test template
        $template = EmailTemplate::factory()->create([
            'code' => 'test_template',
            'subject' => 'Hello {name}',
            'content' => '<p>Welcome {name}!</p>',
            'is_active' => true,
        ]);

        $notification = new EmailNotification(
            'test_template',
            ['name' => 'John Doe']
        );

        $user = User::factory()->create();
        $mailMessage = $notification->toMail($user);

        $this->assertInstanceOf(MailMessage::class, $mailMessage);
        $this->assertEquals('Hello John Doe', $mailMessage->subject);
    }

    public function test_to_mail_builds_custom_mail(): void
    {
        $notification = new EmailNotification(
            'test_template',
            [],
            'Custom Subject',
            '<p>Custom Content</p>'
        );

        $user = User::factory()->create();
        $mailMessage = $notification->toMail($user);

        $this->assertInstanceOf(MailMessage::class, $mailMessage);
        $this->assertEquals('Custom Subject', $mailMessage->subject);
    }

    public function test_to_mail_throws_exception_for_nonexistent_template(): void
    {
        $notification = new EmailNotification('nonexistent_template');

        $user = User::factory()->create();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template with code 'nonexistent_template' not found or inactive");

        $notification->toMail($user);
    }

    public function test_to_mail_throws_exception_for_inactive_template(): void
    {
        EmailTemplate::factory()->create([
            'code' => 'inactive_template',
            'is_active' => false,
        ]);

        $notification = new EmailNotification('inactive_template');

        $user = User::factory()->create();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template with code 'inactive_template' not found or inactive");

        $notification->toMail($user);
    }

    public function test_to_mail_throws_exception_for_invalid_template(): void
    {
        EmailTemplate::factory()->create([
            'code' => 'invalid_template',
            'subject' => '', // Empty subject makes template invalid
            'is_active' => true,
        ]);

        $notification = new EmailNotification('invalid_template');

        $user = User::factory()->create();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template 'invalid_template' is not valid");

        $notification->toMail($user);
    }

    public function test_to_mail_throws_exception_for_missing_variables(): void
    {
        EmailTemplate::factory()->create([
            'code' => 'template_with_vars',
            'subject' => 'Hello {name}',
            'content' => 'Your code is {code}',
            'is_active' => true,
        ]);

        $notification = new EmailNotification(
            'template_with_vars',
            ['name' => 'John'] // Missing 'code' variable
        );

        $user = User::factory()->create();

        $this->expectException(InvalidArgumentException::class);

        $notification->toMail($user);
    }



    public function test_to_array_returns_notification_data(): void
    {
        $notification = new EmailNotification(
            'test_template',
            ['name' => 'John Doe'],
            'Custom Subject'
        );

        $user = User::factory()->create();
        $array = $notification->toArray($user);

        $this->assertEquals('test_template', $array['template_code']);
        $this->assertEquals(['name' => 'John Doe'], $array['variables']);
        $this->assertEquals('Custom Subject', $array['custom_subject']);
        $this->assertArrayHasKey('sent_at', $array);
    }

    public function test_notification_has_correct_configuration(): void
    {
        $notification = new EmailNotification('test_template');

        $this->assertEquals(3, $notification->tries);
        $this->assertEquals(60, $notification->timeout);
        $this->assertInstanceOf(\DateTime::class, $notification->retryUntil());
    }
}
