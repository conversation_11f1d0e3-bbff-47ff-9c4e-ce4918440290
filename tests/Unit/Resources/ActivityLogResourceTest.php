<?php

declare(strict_types=1);

namespace Tests\Unit\Resources;

use App\Http\Resources\Api\V1\ActivityLogResource;
use App\Models\Organisation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Spatie\Activitylog\Models\Activity;
use Tests\TestCase;

final class ActivityLogResourceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->enableActivityLogging();
    }

    public function test_activity_log_resource_structure(): void
    {
        $user = User::factory()->create(['name' => '<PERSON> Doe']);
        $organisation = Organisation::factory()->create(['name' => 'Test Organisation']);

        $activity = new Activity([
            'id' => 1,
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'created',
            'properties' => ['key' => 'value'],
            'batch_uuid' => 'test-uuid',
            'created_at' => now(),
        ]);

        $activity->setRelation('subject', $organisation);
        $activity->setRelation('causer', $user);

        $request = Request::create('/');
        $resource = new ActivityLogResource($activity);
        $array = $resource->toArray($request);

        // Check that all expected fields are present
        $this->assertArrayHasKey('id', $array);
        $this->assertArrayHasKey('log_name', $array);
        $this->assertArrayHasKey('description', $array);
        $this->assertArrayHasKey('formatted_description', $array);
        $this->assertArrayHasKey('subject_type', $array);
        $this->assertArrayHasKey('subject_id', $array);
        $this->assertArrayHasKey('causer_type', $array);
        $this->assertArrayHasKey('causer_id', $array);
        $this->assertArrayHasKey('properties', $array);
        $this->assertArrayHasKey('event', $array);
        $this->assertArrayHasKey('batch_uuid', $array);
        $this->assertArrayHasKey('created_at', $array);

        // Check field values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals('default', $array['log_name']);
        $this->assertEquals('created', $array['description']);
        $this->assertEquals(Organisation::class, $array['subject_type']);
        $this->assertEquals($organisation->id, $array['subject_id']);
        $this->assertEquals(User::class, $array['causer_type']);
        $this->assertEquals($user->id, $array['causer_id']);
        $this->assertNotNull($array['properties']);
        $this->assertEquals('created', $array['event']);
        $this->assertEquals('test-uuid', $array['batch_uuid']);
        $this->assertNotNull($array['created_at']);

        // Check that formatted_description is different from description
        $this->assertNotEquals($array['description'], $array['formatted_description']);
        $this->assertNotEmpty($array['formatted_description']);
    }

    public function test_activity_log_resource_includes_subject_when_loaded(): void
    {
        $user = User::factory()->create(['name' => 'John Doe', 'email' => '<EMAIL>']);
        $organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'status' => 'active'
        ]);

        $activity = new Activity([
            'id' => 1,
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'causer_type' => User::class,
            'causer_id' => $user->id,
            'event' => 'created',
            'properties' => [],
            'batch_uuid' => null,
            'created_at' => now(),
        ]);

        $activity->setRelation('subject', $organisation);
        $activity->setRelation('causer', $user);

        $request = Request::create('/');
        $resource = new ActivityLogResource($activity);
        $array = $resource->toArray($request);

        // Check subject formatting
        $this->assertArrayHasKey('subject', $array);
        $this->assertEquals($organisation->id, $array['subject']['id']);
        $this->assertEquals('Organisation', $array['subject']['type']);
        $this->assertEquals('Test Organisation', $array['subject']['name']);
        $this->assertEquals('TEST001', $array['subject']['code']);
        $this->assertEquals('active', $array['subject']['status']);

        // Check causer formatting
        $this->assertArrayHasKey('causer', $array);
        $this->assertEquals($user->id, $array['causer']['id']);
        $this->assertEquals('John Doe', $array['causer']['name']);
        $this->assertEquals('<EMAIL>', $array['causer']['email']);
    }

    public function test_activity_log_resource_handles_null_relations(): void
    {
        // Create activity with null relations
        $activity = new Activity([
            'id' => 1,
            'log_name' => 'default',
            'description' => 'created',
            'subject_type' => Organisation::class,
            'subject_id' => 999, // Non-existent ID
            'causer_type' => User::class,
            'causer_id' => 999, // Non-existent ID
            'event' => 'created',
            'properties' => [],
            'batch_uuid' => null,
            'created_at' => now(),
        ]);

        // Set relations to null to simulate missing records
        $activity->setRelation('subject', null);
        $activity->setRelation('causer', null);

        $request = Request::create('/');
        $resource = new ActivityLogResource($activity);
        $array = $resource->toArray($request);

        // Relations should be null when records don't exist
        $this->assertNull($array['subject']);
        $this->assertNull($array['causer']);

        // But IDs and types should still be present
        $this->assertEquals(Organisation::class, $array['subject_type']);
        $this->assertEquals(999, $array['subject_id']);
        $this->assertEquals(User::class, $array['causer_type']);
        $this->assertEquals(999, $array['causer_id']);

        // Should still have formatted description
        $this->assertNotEmpty($array['formatted_description']);
    }
}
