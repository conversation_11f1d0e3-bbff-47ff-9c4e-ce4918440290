<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\FinancialStatement;
use App\Models\FinancialStatementOrder;
use App\Models\Order;
use App\Models\Organisation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class FinancialStatementOrderTest extends TestCase
{
    use RefreshDatabase;

    public function test_financial_statement_order_can_be_created(): void
    {
        $financialStatement = FinancialStatement::factory()->create();
        $order = Order::factory()->create();

        $financialStatementOrder = FinancialStatementOrder::factory()->create([
            'financial_statement_id' => $financialStatement->id,
            'order_id' => $order->id,
        ]);

        $this->assertDatabaseHas('financial_statement_orders', [
            'financial_statement_id' => $financialStatement->id,
            'order_id' => $order->id,
        ]);

        $this->assertEquals($financialStatement->id, $financialStatementOrder->financial_statement_id);
        $this->assertEquals($order->id, $financialStatementOrder->order_id);
    }

    public function test_financial_statement_order_belongs_to_financial_statement(): void
    {
        $financialStatement = FinancialStatement::factory()->create();
        $financialStatementOrder = FinancialStatementOrder::factory()->create([
            'financial_statement_id' => $financialStatement->id,
        ]);

        $this->assertInstanceOf(FinancialStatement::class, $financialStatementOrder->financialStatement);
        $this->assertEquals($financialStatement->id, $financialStatementOrder->financialStatement->id);
    }

    public function test_financial_statement_order_belongs_to_order(): void
    {
        $order = Order::factory()->create();
        $financialStatementOrder = FinancialStatementOrder::factory()->create([
            'order_id' => $order->id,
        ]);

        $this->assertInstanceOf(Order::class, $financialStatementOrder->order);
        $this->assertEquals($order->id, $financialStatementOrder->order->id);
    }

    public function test_unique_constraint_prevents_duplicate_entries(): void
    {
        $financialStatement = FinancialStatement::factory()->create();
        $order = Order::factory()->create();

        // Create first record
        FinancialStatementOrder::factory()->create([
            'financial_statement_id' => $financialStatement->id,
            'order_id' => $order->id,
        ]);

        // Attempt to create duplicate should fail
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        FinancialStatementOrder::factory()->create([
            'financial_statement_id' => $financialStatement->id,
            'order_id' => $order->id,
        ]);
    }

    public function test_by_financial_statement_scope(): void
    {
        $financialStatement1 = FinancialStatement::factory()->create();
        $financialStatement2 = FinancialStatement::factory()->create();
        
        FinancialStatementOrder::factory()->count(3)->create([
            'financial_statement_id' => $financialStatement1->id,
        ]);
        
        FinancialStatementOrder::factory()->count(2)->create([
            'financial_statement_id' => $financialStatement2->id,
        ]);

        $statement1Orders = FinancialStatementOrder::byFinancialStatement($financialStatement1->id)->get();
        $statement2Orders = FinancialStatementOrder::byFinancialStatement($financialStatement2->id)->get();

        $this->assertCount(3, $statement1Orders);
        $this->assertCount(2, $statement2Orders);
    }

    public function test_by_order_scope(): void
    {
        $order1 = Order::factory()->create();
        $order2 = Order::factory()->create();
        
        FinancialStatementOrder::factory()->count(2)->create([
            'order_id' => $order1->id,
        ]);
        
        FinancialStatementOrder::factory()->create([
            'order_id' => $order2->id,
        ]);

        $order1Records = FinancialStatementOrder::byOrder($order1->id)->get();
        $order2Records = FinancialStatementOrder::byOrder($order2->id)->get();

        $this->assertCount(2, $order1Records);
        $this->assertCount(1, $order2Records);
    }

    public function test_factory_methods(): void
    {
        $financialStatement = FinancialStatement::factory()->create();
        $order = Order::factory()->create();

        // Test forFinancialStatement method
        $record1 = FinancialStatementOrder::factory()
            ->forFinancialStatement($financialStatement)
            ->create();
        
        $this->assertEquals($financialStatement->id, $record1->financial_statement_id);

        // Test forOrder method
        $record2 = FinancialStatementOrder::factory()
            ->forOrder($order)
            ->create();
        
        $this->assertEquals($order->id, $record2->order_id);

        // Test forStatementAndOrder method
        $record3 = FinancialStatementOrder::factory()
            ->forStatementAndOrder($financialStatement->id, $order->id)
            ->create();
        
        $this->assertEquals($financialStatement->id, $record3->financial_statement_id);
        $this->assertEquals($order->id, $record3->order_id);
    }

    public function test_cascade_delete_when_financial_statement_deleted(): void
    {
        $financialStatement = FinancialStatement::factory()->create();
        $order = Order::factory()->create();
        
        $financialStatementOrder = FinancialStatementOrder::factory()->create([
            'financial_statement_id' => $financialStatement->id,
            'order_id' => $order->id,
        ]);

        $this->assertDatabaseHas('financial_statement_orders', [
            'id' => $financialStatementOrder->id,
        ]);

        // Delete financial statement
        $financialStatement->delete();

        // Verify the pivot record is also deleted
        $this->assertDatabaseMissing('financial_statement_orders', [
            'id' => $financialStatementOrder->id,
        ]);
    }

    public function test_cascade_delete_when_order_deleted(): void
    {
        $financialStatement = FinancialStatement::factory()->create();
        $order = Order::factory()->create();
        
        $financialStatementOrder = FinancialStatementOrder::factory()->create([
            'financial_statement_id' => $financialStatement->id,
            'order_id' => $order->id,
        ]);

        $this->assertDatabaseHas('financial_statement_orders', [
            'id' => $financialStatementOrder->id,
        ]);

        // Delete order
        $order->delete();

        // Verify the pivot record is also deleted
        $this->assertDatabaseMissing('financial_statement_orders', [
            'id' => $financialStatementOrder->id,
        ]);
    }
}
