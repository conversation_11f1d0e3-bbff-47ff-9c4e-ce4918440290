<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class ProductTest extends TestCase
{
    use RefreshDatabase;

    public function test_product_permissions_relationship(): void
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
        ]);

        $this->assertCount(1, $product->productPermissions);
        $this->assertEquals($user->id, $product->productPermissions->first()->user_id);
    }

    public function test_grant_access_to_user(): void
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();
        $grantedBy = User::factory()->create();
        $expiresAt = Carbon::now()->addMonth();

        $permission = $product->grantAccessToUser(
            user: $user,
            permissionType: 'view-reports',
            grantedBy: $grantedBy,
            expiresAt: $expiresAt,
            notes: 'Test grant'
        );

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $grantedBy->id,
            'notes' => 'Test grant',
        ]);

        $this->assertEquals($user->id, $permission->user_id);
        $this->assertEquals($product->id, $permission->product_id);
        $this->assertEquals('view-reports', $permission->permission_type);
        $this->assertEquals($grantedBy->id, $permission->granted_by);
        $this->assertEquals('Test grant', $permission->notes);
    }

    public function test_grant_access_updates_existing_permission(): void
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        // Create initial permission
        $product->grantAccessToUser($user, 'view-reports', null, null, 'Initial grant');

        // Update the same permission
        $updatedPermission = $product->grantAccessToUser(
            user: $user,
            permissionType: 'view-reports',
            grantedBy: null,
            expiresAt: Carbon::now()->addMonth(),
            notes: 'Updated grant'
        );

        // Should only have one permission record
        $this->assertCount(1, $product->productPermissions);
        $this->assertEquals('Updated grant', $updatedPermission->notes);
        $this->assertNotNull($updatedPermission->expires_at);
    }

    public function test_revoke_access_from_user(): void
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        // Grant permission first
        $product->grantAccessToUser($user, 'view-reports');

        $this->assertCount(1, $product->productPermissions);

        // Revoke permission
        $revoked = $product->revokeAccessFromUser($user, 'view-reports');

        $this->assertTrue($revoked);
        $this->assertCount(0, $product->fresh()->productPermissions);
    }

    public function test_revoke_access_returns_false_when_no_permission_exists(): void
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        $revoked = $product->revokeAccessFromUser($user, 'view-reports');

        $this->assertFalse($revoked);
    }

    public function test_get_authorized_users(): void
    {
        $product = Product::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create();

        // Grant permissions to users
        $product->grantAccessToUser($user1, 'view-reports');
        $product->grantAccessToUser($user2, 'view-reports');
        $product->grantAccessToUser($user3, 'edit-reports'); // Different permission type

        $authorizedUsers = $product->getAuthorizedUsers('view-reports');

        $this->assertCount(2, $authorizedUsers);
        $this->assertTrue($authorizedUsers->contains('id', $user1->id));
        $this->assertTrue($authorizedUsers->contains('id', $user2->id));
        $this->assertFalse($authorizedUsers->contains('id', $user3->id));
    }

    public function test_get_authorized_users_excludes_expired_permissions(): void
    {
        $product = Product::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Grant valid permission
        $product->grantAccessToUser($user1, 'view-reports');

        // Grant expired permission
        ProductPermission::create([
            'user_id' => $user2->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDay(),
        ]);

        $authorizedUsers = $product->getAuthorizedUsers('view-reports');

        $this->assertCount(1, $authorizedUsers);
        $this->assertTrue($authorizedUsers->contains('id', $user1->id));
        $this->assertFalse($authorizedUsers->contains('id', $user2->id));
    }

    public function test_user_has_access(): void
    {
        $product = Product::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $product->grantAccessToUser($user1, 'view-reports');

        $this->assertTrue($product->userHasAccess($user1, 'view-reports'));
        $this->assertFalse($product->userHasAccess($user2, 'view-reports'));
        $this->assertFalse($product->userHasAccess($user1, 'edit-reports'));
    }

    public function test_get_valid_permissions(): void
    {
        $product = Product::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $grantedBy = User::factory()->create();

        // Create valid permission
        $product->grantAccessToUser($user1, 'view-reports', $grantedBy);

        // Create expired permission
        ProductPermission::create([
            'user_id' => $user2->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDay(),
        ]);

        $validPermissions = $product->getValidPermissions();

        $this->assertCount(1, $validPermissions);
        $this->assertEquals($user1->id, $validPermissions->first()->user->id);
        $this->assertEquals($grantedBy->id, $validPermissions->first()->grantedBy->id);
    }
}
