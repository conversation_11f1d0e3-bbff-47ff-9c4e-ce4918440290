<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\EmailTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_email_template(): void
    {
        $template = EmailTemplate::factory()->create([
            'name' => 'Test Template',
            'code' => 'test_template',
            'subject' => 'Hello {user_name}',
            'content' => '<p>Welcome {user_name} to {app_name}!</p>',
            'variables' => ['user_name', 'app_name'],
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('email_templates', [
            'name' => 'Test Template',
            'code' => 'test_template',
            'is_active' => true,
        ]);

        $this->assertEquals(['user_name', 'app_name'], $template->variables);
        $this->assertTrue($template->is_active);
    }

    public function test_can_render_template_with_variables(): void
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {user_name}',
            'content' => '<p>Welcome {user_name} to {app_name}!</p>',
            'variables' => ['user_name', 'app_name'],
        ]);

        $rendered = $template->render([
            'user_name' => 'John Doe',
            'app_name' => 'JAST Partner Portal',
        ]);

        $this->assertEquals('Hello John Doe', $rendered['subject']);
        $this->assertEquals('<p>Welcome John Doe to JAST Partner Portal!</p>', $rendered['content']);
    }

    public function test_throws_exception_when_missing_required_variables(): void
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {user_name}',
            'content' => '<p>Welcome {user_name} to {app_name}!</p>',
            'variables' => ['user_name', 'app_name'],
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Missing required variables: app_name');

        $template->render(['user_name' => 'John Doe']);
    }

    public function test_can_get_required_variables(): void
    {
        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {user_name}',
            'content' => '<p>Welcome {user_name} to {app_name}! Your code is {verification_code}.</p>',
        ]);

        $requiredVariables = $template->getRequiredVariables();

        $this->assertContains('user_name', $requiredVariables);
        $this->assertContains('app_name', $requiredVariables);
        $this->assertContains('verification_code', $requiredVariables);
        $this->assertCount(3, $requiredVariables);
    }

    public function test_active_scope_filters_active_templates(): void
    {
        // Get initial count of active templates (from migration)
        $initialActiveCount = EmailTemplate::active()->count();

        EmailTemplate::factory()->create(['is_active' => true]);
        EmailTemplate::factory()->create(['is_active' => false]);

        $activeTemplates = EmailTemplate::active()->get();

        // Should have initial count + 1 new active template
        $this->assertCount($initialActiveCount + 1, $activeTemplates);
        $this->assertTrue($activeTemplates->every(fn($template) => $template->is_active));
    }

    public function test_by_code_scope_finds_template_by_code(): void
    {
        $template = EmailTemplate::factory()->create(['code' => 'test_code']);
        EmailTemplate::factory()->create(['code' => 'other_code']);

        $foundTemplate = EmailTemplate::byCode('test_code')->first();

        $this->assertNotNull($foundTemplate);
        $this->assertEquals('test_code', $foundTemplate->code);
    }

    public function test_is_valid_method(): void
    {
        $validTemplate = EmailTemplate::factory()->create([
            'name' => 'Valid Template',
            'code' => 'valid_code',
            'subject' => 'Test Subject',
            'content' => 'Test Content',
            'is_active' => true,
        ]);

        $invalidTemplate = EmailTemplate::factory()->create([
            'name' => '',
            'is_active' => false,
        ]);

        $this->assertTrue($validTemplate->isValid());
        $this->assertFalse($invalidTemplate->isValid());
    }
}
