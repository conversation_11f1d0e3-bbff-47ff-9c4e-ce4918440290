<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class ProductPermissionTest extends TestCase
{
    use RefreshDatabase;

    public function test_product_permission_can_be_created(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $permission = ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $user->id,
            'notes' => 'Test permission'
        ]);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
        ]);

        $this->assertTrue($permission->isValid());
    }

    public function test_product_permission_relationships(): void
    {
        $user = User::factory()->create();
        $grantedBy = User::factory()->create();
        $product = Product::factory()->create();

        $permission = ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $grantedBy->id,
        ]);

        $this->assertEquals($user->id, $permission->user->id);
        $this->assertEquals($product->id, $permission->product->id);
        $this->assertEquals($grantedBy->id, $permission->grantedBy->id);
    }

    public function test_permission_expiration_validation(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        // Create expired permission
        $expiredPermission = ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDay(),
        ]);

        // Create valid permission
        $validPermission = ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->addDay(),
        ]);

        // Create never-expiring permission
        $neverExpiringPermission = ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product3->id,
            'permission_type' => 'view-reports',
            'expires_at' => null,
        ]);

        $this->assertFalse($expiredPermission->isValid());
        $this->assertTrue($validPermission->isValid());
        $this->assertTrue($neverExpiringPermission->isValid());
    }

    public function test_valid_scope(): void
    {
        $user = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        // Create expired permission
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDay(),
        ]);

        // Create valid permission
        ProductPermission::create([
            'user_id' => $user->id,
            'product_id' => $product2->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->addDay(),
        ]);

        $validPermissions = ProductPermission::valid()->get();
        $this->assertCount(1, $validPermissions);
    }

    public function test_scopes(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        ProductPermission::create([
            'user_id' => $user1->id,
            'product_id' => $product1->id,
            'permission_type' => 'view-reports',
        ]);

        ProductPermission::create([
            'user_id' => $user2->id,
            'product_id' => $product2->id,
            'permission_type' => 'edit-reports',
        ]);

        $viewPermissions = ProductPermission::ofType('view-reports')->get();
        $user1Permissions = ProductPermission::forUser($user1->id)->get();
        $product1Permissions = ProductPermission::forProduct($product1->id)->get();

        $this->assertCount(1, $viewPermissions);
        $this->assertCount(1, $user1Permissions);
        $this->assertCount(1, $product1Permissions);
    }
}
