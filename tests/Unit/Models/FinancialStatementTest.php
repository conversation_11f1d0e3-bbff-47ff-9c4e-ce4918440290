<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Models\FinancialStatement;
use App\Models\FinancialStatementOrder;
use App\Models\Order;
use App\Models\Organisation;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class FinancialStatementTest extends TestCase
{
    use RefreshDatabase;

    public function test_financial_statement_can_be_created(): void
    {
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);
        
        $financialStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'report_title' => 'Test Monthly Report',
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31',
            'organisation_id' => $organisation->id,
            'total_amount' => 150000, // $1500.00
            'total_quantity' => 100,
            'total_orders' => 25,
        ]);

        $this->assertDatabaseHas('financial_statements', [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'report_title' => 'Test Monthly Report',
            'organisation_id' => $organisation->id,
            'total_amount' => 150000,
            'total_quantity' => 100,
            'total_orders' => 25,
        ]);

        $this->assertEquals(FinancialStatement::TYPE_MONTHLY, $financialStatement->report_type);
        $this->assertEquals('Test Monthly Report', $financialStatement->report_title);
        $this->assertEquals($organisation->id, $financialStatement->organisation_id);
    }

    public function test_financial_statement_belongs_to_organisation(): void
    {
        $organisation = Organisation::factory()->create();
        $financialStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
        ]);

        $this->assertInstanceOf(Organisation::class, $financialStatement->organisation);
        $this->assertEquals($organisation->id, $financialStatement->organisation->id);
    }

    public function test_financial_statement_has_many_orders_through_pivot(): void
    {
        $financialStatement = FinancialStatement::factory()->create();
        $orders = Order::factory()->count(3)->create();

        foreach ($orders as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $financialStatement->id,
                'order_id' => $order->id,
            ]);
        }

        $this->assertCount(3, $financialStatement->orders);
        $this->assertInstanceOf(Order::class, $financialStatement->orders->first());
    }

    public function test_generate_report_code_for_monthly_report(): void
    {
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);
        $financialStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2025-01-01',
            'organisation_id' => $organisation->id,
        ]);

        $financialStatement->load('organisation');
        $reportCode = $financialStatement->generateReportCode();

        $this->assertEquals('TEST001202501m', $reportCode);
    }

    public function test_generate_report_code_for_quarterly_report(): void
    {
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);
        $financialStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
            'start_date' => '2025-01-01', // Q1
            'organisation_id' => $organisation->id,
        ]);

        $financialStatement->load('organisation');
        $reportCode = $financialStatement->generateReportCode();

        $this->assertEquals('TEST001202501q', $reportCode);
    }

    public function test_generate_report_code_for_yearly_report(): void
    {
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);
        $financialStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_YEARLY,
            'start_date' => '2025-01-01',
            'organisation_id' => $organisation->id,
        ]);

        $financialStatement->load('organisation');
        $reportCode = $financialStatement->generateReportCode();

        $this->assertEquals('TEST0012025y', $reportCode);
    }

    public function test_generate_report_code_for_custom_report(): void
    {
        $organisation = Organisation::factory()->create(['code' => 'TEST001']);
        $financialStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_CUSTOM,
            'start_date' => '2025-01-15',
            'end_date' => '2025-02-14',
            'organisation_id' => $organisation->id,
        ]);

        $financialStatement->load('organisation');
        $reportCode = $financialStatement->generateReportCode();

        $this->assertEquals('TEST00120250115_20250214c', $reportCode);
    }

    public function test_generate_report_title(): void
    {
        $organisation = Organisation::factory()->create(['name' => 'Test Company']);
        $financialStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31',
            'organisation_id' => $organisation->id,
        ]);

        $financialStatement->load('organisation');
        $title = $financialStatement->generateReportTitle();

        $this->assertEquals('Test Company 2025年1月 销售报表', $title);
    }

    public function test_get_period_description_for_different_types(): void
    {
        // Monthly
        $monthlyStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31',
        ]);
        $this->assertEquals('2025年1月', $monthlyStatement->getPeriodDescription());

        // Quarterly
        $quarterlyStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
            'start_date' => '2025-01-01',
            'end_date' => '2025-03-31',
        ]);
        $this->assertEquals('2025年第1季度', $quarterlyStatement->getPeriodDescription());

        // Yearly
        $yearlyStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_YEARLY,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
        ]);
        $this->assertEquals('2025年', $yearlyStatement->getPeriodDescription());

        // Custom
        $customStatement = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_CUSTOM,
            'start_date' => '2025-01-15',
            'end_date' => '2025-02-14',
        ]);
        $this->assertEquals('2025年1月15日 - 2025年2月14日', $customStatement->getPeriodDescription());
    }

    public function test_validate_monthly_date_range(): void
    {
        // Valid monthly range
        $validMonthly = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31',
        ]);
        $this->assertTrue($validMonthly->validateDateRange());

        // Invalid monthly range (not full month)
        $invalidMonthly = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2025-01-05',
            'end_date' => '2025-01-25',
        ]);
        $this->assertFalse($invalidMonthly->validateDateRange());
    }

    public function test_validate_quarterly_date_range(): void
    {
        // Valid quarterly range (Q1)
        $validQuarterly = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
            'start_date' => '2025-01-01',
            'end_date' => '2025-03-31',
        ]);
        $this->assertTrue($validQuarterly->validateDateRange());

        // Invalid quarterly range
        $invalidQuarterly = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
            'start_date' => '2025-01-15',
            'end_date' => '2025-03-15',
        ]);
        $this->assertFalse($invalidQuarterly->validateDateRange());
    }

    public function test_validate_yearly_date_range(): void
    {
        // Valid yearly range
        $validYearly = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_YEARLY,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
        ]);
        $this->assertTrue($validYearly->validateDateRange());

        // Invalid yearly range
        $invalidYearly = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_YEARLY,
            'start_date' => '2025-02-01',
            'end_date' => '2025-11-30',
        ]);
        $this->assertFalse($invalidYearly->validateDateRange());
    }

    public function test_validate_custom_date_range(): void
    {
        // Valid custom range
        $validCustom = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_CUSTOM,
            'start_date' => '2025-01-15',
            'end_date' => '2025-02-14',
        ]);
        $this->assertTrue($validCustom->validateDateRange());

        // Invalid custom range (start > end)
        $invalidCustom = FinancialStatement::factory()->create([
            'report_type' => FinancialStatement::TYPE_CUSTOM,
            'start_date' => '2025-02-14',
            'end_date' => '2025-01-15',
        ]);
        $this->assertFalse($invalidCustom->validateDateRange());
    }

    public function test_total_amount_in_currency_attribute(): void
    {
        $financialStatement = FinancialStatement::factory()->create([
            'total_amount' => 150000, // $1500.00
        ]);

        $this->assertEquals(1500.00, $financialStatement->total_amount_in_currency);
    }

    public function test_report_type_check_methods(): void
    {
        $monthlyStatement = FinancialStatement::factory()->monthly()->create();
        $this->assertTrue($monthlyStatement->isMonthly());
        $this->assertFalse($monthlyStatement->isQuarterly());
        $this->assertFalse($monthlyStatement->isYearly());
        $this->assertFalse($monthlyStatement->isCustom());

        $quarterlyStatement = FinancialStatement::factory()->quarterly()->create();
        $this->assertFalse($quarterlyStatement->isMonthly());
        $this->assertTrue($quarterlyStatement->isQuarterly());
        $this->assertFalse($quarterlyStatement->isYearly());
        $this->assertFalse($quarterlyStatement->isCustom());
    }

    public function test_scopes(): void
    {
        $organisation = Organisation::factory()->create();

        FinancialStatement::factory()->monthly()->create(['organisation_id' => $organisation->id]);
        FinancialStatement::factory()->quarterly()->create(['organisation_id' => $organisation->id]);
        FinancialStatement::factory()->yearly()->create();

        // Test byType scope
        $monthlyReports = FinancialStatement::byType(FinancialStatement::TYPE_MONTHLY)->get();
        $this->assertCount(1, $monthlyReports);

        // Test byOrganisation scope
        $orgReports = FinancialStatement::byOrganisation($organisation->id)->get();
        $this->assertCount(2, $orgReports);
    }

    public function test_financial_statement_has_default_pending_audit_status(): void
    {
        $organisation = Organisation::factory()->create();

        $financialStatement = FinancialStatement::factory()
            ->pendingAudit()
            ->create([
                'organisation_id' => $organisation->id,
            ]);

        $this->assertEquals(FinancialStatement::STATUS_PENDING_AUDIT, $financialStatement->status);
        $this->assertTrue($financialStatement->isPendingAudit());
        $this->assertFalse($financialStatement->isPublished());
    }

    public function test_financial_statement_can_be_published(): void
    {
        $organisation = Organisation::factory()->create();

        $financialStatement = FinancialStatement::factory()
            ->published()
            ->create([
                'organisation_id' => $organisation->id,
            ]);

        $this->assertEquals(FinancialStatement::STATUS_PUBLISHED, $financialStatement->status);
        $this->assertTrue($financialStatement->isPublished());
        $this->assertFalse($financialStatement->isPendingAudit());
    }

    public function test_status_scopes_work_correctly(): void
    {
        $organisation = Organisation::factory()->create();

        $pendingStatement = FinancialStatement::factory()
            ->pendingAudit()
            ->create([
                'organisation_id' => $organisation->id,
            ]);

        $publishedStatement = FinancialStatement::factory()
            ->published()
            ->create([
                'organisation_id' => $organisation->id,
            ]);

        $pendingResults = FinancialStatement::pendingAudit()->get();
        $publishedResults = FinancialStatement::published()->get();

        $this->assertTrue($pendingResults->contains($pendingStatement));
        $this->assertFalse($pendingResults->contains($publishedStatement));

        $this->assertTrue($publishedResults->contains($publishedStatement));
        $this->assertFalse($publishedResults->contains($pendingStatement));
    }

    public function test_validate_unique_orders_by_report_type_passes_with_no_conflicts(): void
    {
        $organisation = Organisation::factory()->create();
        $orders = Order::factory()->count(3)->create();

        $financialStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
        ]);

        // Associate orders with the financial statement
        foreach ($orders as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $financialStatement->id,
                'order_id' => $order->id,
            ]);
        }

        $this->assertTrue($financialStatement->validateUniqueOrdersByReportType());
    }

    public function test_validate_unique_orders_by_report_type_fails_with_conflicts(): void
    {
        $organisation = Organisation::factory()->create();
        $orders = Order::factory()->count(3)->create();

        // Create first financial statement with orders
        $firstStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
        ]);

        foreach ($orders as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $firstStatement->id,
                'order_id' => $order->id,
            ]);
        }

        // Create second financial statement with same report type and overlapping orders
        $secondStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
        ]);

        // Associate the same orders with the second statement
        foreach ($orders->take(2) as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $secondStatement->id,
                'order_id' => $order->id,
            ]);
        }

        $this->assertFalse($secondStatement->validateUniqueOrdersByReportType());
    }

    public function test_validate_unique_orders_allows_same_orders_in_different_report_types(): void
    {
        $organisation = Organisation::factory()->create();
        $orders = Order::factory()->count(3)->create();

        // Create monthly financial statement
        $monthlyStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
        ]);

        foreach ($orders as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $monthlyStatement->id,
                'order_id' => $order->id,
            ]);
        }

        // Create quarterly financial statement with same orders (should be allowed)
        $quarterlyStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
        ]);

        foreach ($orders as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $quarterlyStatement->id,
                'order_id' => $order->id,
            ]);
        }

        $this->assertTrue($monthlyStatement->validateUniqueOrdersByReportType());
        $this->assertTrue($quarterlyStatement->validateUniqueOrdersByReportType());
    }

    public function test_get_conflicting_statements_returns_correct_results(): void
    {
        $organisation = Organisation::factory()->create();
        $orders = Order::factory()->count(3)->create();

        // Create first financial statement
        $firstStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'report_title' => 'First Monthly Report',
        ]);

        foreach ($orders as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $firstStatement->id,
                'order_id' => $order->id,
            ]);
        }

        // Create second financial statement with overlapping orders
        $secondStatement = FinancialStatement::factory()->create([
            'organisation_id' => $organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'report_title' => 'Second Monthly Report',
        ]);

        foreach ($orders->take(2) as $order) {
            FinancialStatementOrder::factory()->create([
                'financial_statement_id' => $secondStatement->id,
                'order_id' => $order->id,
            ]);
        }

        $conflicts = $secondStatement->getConflictingStatements();

        $this->assertCount(1, $conflicts);
        $this->assertEquals($firstStatement->id, $conflicts->first()->id);
    }
}
