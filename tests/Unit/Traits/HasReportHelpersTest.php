<?php

declare(strict_types=1);

namespace Tests\Unit\Traits;

use App\Traits\HasReportHelpers;
use Tests\TestCase;

final class HasReportHelpersTest extends TestCase
{
    use HasReportHelpers;

    /**
     * Test getDateFormat method returns correct MySQL date format strings
     */
    public function test_get_date_format_returns_correct_formats(): void
    {
        // Test all supported grouping types
        $this->assertEquals('%Y-%m-%d %H:00', $this->getDateFormat('hour'));
        $this->assertEquals('%Y-%m-%d', $this->getDateFormat('day'));
        $this->assertEquals('%Y-%u', $this->getDateFormat('week'));
        $this->assertEquals('%Y-%m', $this->getDateFormat('month'));
        $this->assertEquals('%Y-Q%q', $this->getDateFormat('quarter'));
        $this->assertEquals('%Y', $this->getDateFormat('year'));
    }

    /**
     * Test getDateFormat method returns default format for unknown grouping types
     */
    public function test_get_date_format_returns_default_for_unknown_types(): void
    {
        // Test unknown grouping types return default format
        $this->assertEquals('%Y-%m-%d', $this->getDateFormat('unknown'));
        $this->assertEquals('%Y-%m-%d', $this->getDateFormat('invalid'));
        $this->assertEquals('%Y-%m-%d', $this->getDateFormat(''));
    }

    /**
     * Test that the trait method is accessible from classes that use it
     */
    public function test_trait_method_is_accessible(): void
    {
        // This test verifies that the trait method can be called
        // and returns the expected type
        $result = $this->getDateFormat('day');

        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    /**
     * Test fillMissingPeriods method fills gaps in daily data
     */
    public function test_fill_missing_periods_daily(): void
    {
        $originalData = collect([
            ['period' => '2023-01-01', 'total_sales' => 1000],
            ['period' => '2023-01-03', 'total_sales' => 2000], // Missing 2023-01-02
        ]);

        $zeroTemplate = ['total_sales' => 0];

        $result = $this->fillMissingPeriods(
            $originalData,
            '2023-01-01',
            '2023-01-03',
            'day',
            'UTC',
            $zeroTemplate
        );

        $this->assertCount(3, $result);
        $this->assertEquals('2023-01-01', $result[0]['period']);
        $this->assertEquals(1000, $result[0]['total_sales']);
        $this->assertEquals('2023-01-02', $result[1]['period']);
        $this->assertEquals(0, $result[1]['total_sales']); // Filled with zero
        $this->assertEquals('2023-01-03', $result[2]['period']);
        $this->assertEquals(2000, $result[2]['total_sales']);
    }

    /**
     * Test generatePeriodRange method for daily periods
     */
    public function test_generate_period_range_daily(): void
    {
        $periods = $this->generatePeriodRange('2023-01-01', '2023-01-03', 'day', 'UTC');

        $expected = ['2023-01-01', '2023-01-02', '2023-01-03'];
        $this->assertEquals($expected, $periods);
    }

    /**
     * Test generatePeriodRange method for hourly periods
     */
    public function test_generate_period_range_hourly(): void
    {
        $periods = $this->generatePeriodRange('2023-01-01', '2023-01-01', 'hour', 'UTC');

        // Should generate 24 hours for a single day
        $this->assertCount(24, $periods);
        $this->assertEquals('2023-01-01 00:00', $periods[0]);
        $this->assertEquals('2023-01-01 23:00', $periods[23]);
    }

    /**
     * Test formatPeriodForDatabase method
     */
    public function test_format_period_for_database(): void
    {
        $date = \Carbon\Carbon::parse('2023-01-15 14:30:45');

        $this->assertEquals('2023-01-15', $this->formatPeriodForDatabase($date, 'day'));
        $this->assertEquals('2023-01-15 14:00', $this->formatPeriodForDatabase($date, 'hour'));
        $this->assertEquals('2023-01', $this->formatPeriodForDatabase($date, 'month'));
        $this->assertEquals('2023', $this->formatPeriodForDatabase($date, 'year'));
        $this->assertEquals('2023-Q1', $this->formatPeriodForDatabase($date, 'quarter'));
    }
}
