<?php

declare(strict_types=1);

namespace Tests\Feature\Services;

use App\Models\Product;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Services\ProductSyncService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Tests\Traits\CreatesStoreTestTables;

/**
 * Integration tests for ProductSyncService
 * 
 * These tests use real database operations instead of mocking to test
 * the complete integration between the service and database layers.
 */
final class ProductSyncServiceIntegrationTest extends TestCase
{
    use RefreshDatabase, CreatesStoreTestTables;

    private ProductSyncService $productSyncService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->productSyncService = app(ProductSyncService::class);

        // Create test store tables for integration testing
        $this->createStoreTestTables();

        // Clean up any existing test data
        $this->cleanupStoreTestData();
    }

    protected function tearDown(): void
    {
        // Clean up test data after each test
        $this->cleanupStoreTestData();
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Product::truncate();
        SyncLog::truncate();
        SyncRecord::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        parent::tearDown();
    }

    public function test_sync_creates_sync_log_with_range_info(): void
    {
        $config = ['incremental' => false, 'batch_size' => 50];

        $syncLog = $this->productSyncService->sync($config);

        $this->assertInstanceOf(SyncLog::class, $syncLog);
        $this->assertEquals('product_sync', $syncLog->sync_type);
        $this->assertNotNull($syncLog->batch_id);
        $this->assertNotNull($syncLog->started_at);

        // Check that sync_config includes original config plus range info
        $syncConfig = $syncLog->sync_config;
        $this->assertEquals(50, $syncConfig['batch_size']);
        $this->assertFalse($syncConfig['incremental']);
        $this->assertArrayHasKey('sync_start_time', $syncConfig);
        $this->assertArrayHasKey('sync_end_time', $syncConfig);
        $this->assertEquals('full', $syncConfig['sync_type_detail']);
    }

    public function test_sync_saves_incremental_range_info(): void
    {
        // Create a previous successful sync to enable incremental sync
        SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'previous_batch',
            'status' => 'completed',
            'started_at' => now()->subHours(2),
            'completed_at' => now()->subHours(1),
        ]);

        $config = ['incremental' => true, 'batch_size' => 25];

        $syncLog = $this->productSyncService->sync($config);

        $this->assertInstanceOf(SyncLog::class, $syncLog);

        // Check that sync_config includes incremental range info
        $syncConfig = $syncLog->sync_config;
        $this->assertEquals(25, $syncConfig['batch_size']);
        $this->assertTrue($syncConfig['incremental']);
        $this->assertArrayHasKey('sync_start_time', $syncConfig);
        $this->assertArrayHasKey('sync_end_time', $syncConfig);
        $this->assertEquals('incremental', $syncConfig['sync_type_detail']);

        // sync_start_time should be the completion time of the previous sync
        $this->assertNotEquals('1970-01-01 00:00:00', $syncConfig['sync_start_time']);
    }

    public function test_sync_processes_products_successfully(): void
    {
        // Create test product data in the store database
        $this->createTestStoreData([
            [
                'variant_id' => 1,
                'variant_code' => 'TEST-001-V1',
                'variant_enabled' => 1,
                'variant_updated_at' => '2023-01-01 00:00:00',
                'variant_name' => 'Test Variant',
                'product_id' => 1,
                'owner_id' => 1,
                'sku' => 'TEST-001',
                'slug' => 'test-product',
                'release_date' => '2023-01-01 00:00:00',
                'product_updated_at' => '2023-01-01 00:00:00',
                'price' => 1000,
                'original_price' => 1200,
                'minimum_price' => 800,
                'lowest_price_before_discount' => 900,
                'package' => '/images/test-package.jpg'
            ]
        ]);

        $syncLog = $this->productSyncService->sync();

        $this->assertEquals('completed', $syncLog->status);
        $this->assertGreaterThanOrEqual(1, $syncLog->total_records);
        $this->assertGreaterThanOrEqual(1, $syncLog->success_records);
        $this->assertEquals(0, $syncLog->failed_records);

        // Check that product was created in database
        $product = Product::where('store_variant_id', 1)->first();
        $this->assertNotNull($product);
        $this->assertEquals('TEST-001-V1', $product->code);
        $this->assertEquals('Test Variant', $product->name);
        $this->assertTrue($product->enabled);
        $this->assertEquals(1000, $product->current_price);
        $this->assertEquals('/images/test-package.jpg', $product->package);

        // Check that sync record was created
        $syncRecord = SyncRecord::where('batch_id', $syncLog->batch_id)
            ->where('source_id', '1')
            ->first();
        $this->assertNotNull($syncRecord);
        $this->assertEquals('sylius_product_variant', $syncRecord->source_table);
        $this->assertEquals('products', $syncRecord->target_table);
        $this->assertEquals('success', $syncRecord->status);
    }

    public function test_sync_skips_unchanged_products(): void
    {
        // Create existing product with specific timestamps
        $testTime = '2023-01-01 00:00:00';
        $existingProduct = Product::create([
            'store_variant_id' => 1,
            'owner_id' => 1,
            'sku' => 'TEST-001',
            'code' => 'TEST-001-V1',
            'name' => 'Test Variant',
            'slug' => 'test-product',
            'enabled' => true,
            'current_price' => 1000,
            'store_product_updated_at' => $testTime,
            'store_variant_updated_at' => $testTime,
        ]);

        // Create test product data in the store database with same update time
        $this->createTestStoreData([
            [
                'variant_id' => 1,
                'variant_code' => 'TEST-001-V1',
                'variant_enabled' => 1,
                'variant_updated_at' => $testTime, // Same as existing
                'variant_name' => 'Test Variant',
                'product_id' => 1,
                'owner_id' => 1,
                'sku' => 'TEST-001',
                'slug' => 'test-product',
                'release_date' => $testTime,
                'product_updated_at' => $testTime, // Same as existing
                'price' => 1000,
                'original_price' => null,
                'minimum_price' => null,
                'lowest_price_before_discount' => null,
                'package' => null
            ]
        ]);

        $syncLog = $this->productSyncService->sync();

        $this->assertEquals('completed', $syncLog->status);
        $this->assertGreaterThanOrEqual(1, $syncLog->total_records);

        // Debug: Check what actually happened
        $syncRecord = SyncRecord::where('batch_id', $syncLog->batch_id)
            ->where('source_id', '1')
            ->first();

        // If the record failed, show the error message for debugging
        if ($syncRecord && $syncRecord->status === 'failed') {
            $this->fail('Record failed with error: ' . $syncRecord->error_message .
                       '. Success records: ' . $syncLog->success_records .
                       ', Failed records: ' . $syncLog->failed_records);
        }

        $this->assertEquals(0, $syncLog->success_records);
        $this->assertEquals(0, $syncLog->failed_records);

        // Check that sync record was marked as skipped
        $this->assertNotNull($syncRecord);
        $this->assertEquals('skipped', $syncRecord->status);
    }



    /**
     * Create test data in the store database for integration testing.
     */
    private function createTestStoreData(array $products): void
    {
        // Create test products in the store database
        foreach ($products as $product) {
            // Check if product already exists, if so, use existing ID
            $existingProduct = DB::connection('store')->table('sylius_product')
                ->where('code', $product['sku'] ?? 'TEST-SKU')
                ->first();

            if ($existingProduct) {
                $productId = $existingProduct->id;
                // Update existing product
                DB::connection('store')->table('sylius_product')
                    ->where('id', $productId)
                    ->update([
                        'owner_id' => $product['owner_id'] ?? 1,
                        'sku' => $product['sku'] ?? 'TEST-SKU',
                        'release_date' => $product['release_date'] ?? now(),
                        'enabled' => $product['product_enabled'] ?? true,
                        'updated_at' => $product['product_updated_at'] ?? now(),
                    ]);
            } else {
                // Insert new product
                $productId = DB::connection('store')->table('sylius_product')->insertGetId([
                    'code' => $product['sku'] ?? 'TEST-SKU',
                    'owner_id' => $product['owner_id'] ?? 1,
                    'sku' => $product['sku'] ?? 'TEST-SKU',
                    'release_date' => $product['release_date'] ?? now(),
                    'enabled' => $product['product_enabled'] ?? true,
                    'created_at' => $product['product_updated_at'] ?? now(),
                    'updated_at' => $product['product_updated_at'] ?? now(),
                ]);
            }

            // Insert or update product translation
            $existingProductTranslation = DB::connection('store')->table('sylius_product_translation')
                ->where('translatable_id', $productId)
                ->where('locale', 'en_US')
                ->first();

            if ($existingProductTranslation) {
                DB::connection('store')->table('sylius_product_translation')
                    ->where('translatable_id', $productId)
                    ->where('locale', 'en_US')
                    ->update([
                        'name' => $product['variant_name'] ?? 'Test Product',
                        'slug' => $product['slug'] ?? 'test-product',
                        'description' => $product['product_description'] ?? 'Test product description',
                        'updated_at' => now(),
                    ]);
            } else {
                DB::connection('store')->table('sylius_product_translation')->insert([
                    'translatable_id' => $productId,
                    'locale' => 'en_US',
                    'name' => $product['variant_name'] ?? 'Test Product',
                    'slug' => $product['slug'] ?? 'test-product',
                    'description' => $product['product_description'] ?? 'Test product description',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Check if variant already exists, if so, use existing ID
            $existingVariant = DB::connection('store')->table('sylius_product_variant')
                ->where('code', $product['variant_code'] ?? 'TEST-VARIANT')
                ->first();

            if ($existingVariant) {
                $variantId = $existingVariant->id;
                // Update existing variant
                DB::connection('store')->table('sylius_product_variant')
                    ->where('id', $variantId)
                    ->update([
                        'product_id' => $productId,
                        'enabled' => $product['variant_enabled'] ?? true,
                        'price' => $product['price'] ?? 1000,
                        'original_price' => $product['original_price'] ?? null,
                        'package' => $product['package'] ?? null,
                        'updated_at' => $product['variant_updated_at'] ?? now(),
                    ]);
            } else {
                // Insert new variant
                $variantId = DB::connection('store')->table('sylius_product_variant')->insertGetId([
                    'product_id' => $productId,
                    'code' => $product['variant_code'] ?? 'TEST-VARIANT',
                    'enabled' => $product['variant_enabled'] ?? true,
                    'price' => $product['price'] ?? 1000,
                    'original_price' => $product['original_price'] ?? null,
                    'package' => $product['package'] ?? null,
                    'created_at' => $product['variant_updated_at'] ?? now(),
                    'updated_at' => $product['variant_updated_at'] ?? now(),
                ]);
            }

            // Insert or update variant translation
            $existingVariantTranslation = DB::connection('store')->table('sylius_product_variant_translation')
                ->where('translatable_id', $variantId)
                ->where('locale', 'en_US')
                ->first();

            if ($existingVariantTranslation) {
                DB::connection('store')->table('sylius_product_variant_translation')
                    ->where('translatable_id', $variantId)
                    ->where('locale', 'en_US')
                    ->update([
                        'name' => $product['variant_name'] ?? 'Test Variant',
                        'updated_at' => now(),
                    ]);
            } else {
                DB::connection('store')->table('sylius_product_variant_translation')->insert([
                    'translatable_id' => $variantId,
                    'locale' => 'en_US',
                    'name' => $product['variant_name'] ?? 'Test Variant',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Insert or update channel pricing
            $existingChannelPricing = DB::connection('store')->table('sylius_channel_pricing')
                ->where('product_variant_id', $variantId)
                ->first();

            if ($existingChannelPricing) {
                $channelPricingId = $existingChannelPricing->id;
                DB::connection('store')->table('sylius_channel_pricing')
                    ->where('id', $channelPricingId)
                    ->update([
                        'price' => $product['price'] ?? 1000,
                        'original_price' => $product['original_price'] ?? null,
                        'minimum_price' => $product['minimum_price'] ?? null,
                        'lowest_price_before_discount' => $product['lowest_price_before_discount'] ?? null,
                        'updated_at' => now(),
                    ]);
            } else {
                $channelPricingId = DB::connection('store')->table('sylius_channel_pricing')->insertGetId([
                    'product_variant_id' => $variantId,
                    'price' => $product['price'] ?? 1000,
                    'original_price' => $product['original_price'] ?? null,
                    'minimum_price' => $product['minimum_price'] ?? null,
                    'lowest_price_before_discount' => $product['lowest_price_before_discount'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Insert pricing log entry (optional)
            DB::connection('store')->table('sylius_channel_pricing_log_entry')->insert([
                'channel_pricing_id' => $channelPricingId,
                'price' => $product['price'] ?? 1000,
                'original_price' => $product['original_price'] ?? null,
                'logged_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Insert product image (optional)
            if (isset($product['package'])) {
                DB::connection('store')->table('sylius_product_image')->insert([
                    'owner_id' => $productId,
                    'type' => 'TAIL_PACKAGE_THUMBNAIL_PRODUCT',
                    'path' => $product['package'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
