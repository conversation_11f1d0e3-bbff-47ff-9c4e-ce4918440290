<?php

declare(strict_types=1);

namespace Tests\Feature\Services;

use App\Models\SyncLog;
use App\Services\SyncProgressService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

/**
 * Integration tests for SyncProgressService
 * 
 * These tests use real database operations and cache to test
 * the complete integration between the service and storage layers.
 */
final class SyncProgressServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private SyncProgressService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new SyncProgressService();
        
        // Clear cache before each test
        Cache::flush();
    }

    public function test_data_consistency_check_with_real_database(): void
    {
        // This test verifies data consistency checks work with real database operations
        // Unlike the unit test, this uses actual database records

        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        // Create a real sync log in the database
        $syncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => $batchId,
            'status' => 'completed',
            'started_at' => now(),
            'completed_at' => now(),
            'sync_config' => [],
        ]);

        // Initialize progress with the real sync_log_id
        $this->service->initializeProgress($jobId, $batchId);
        $progressData = Cache::get('sync_progress:' . $jobId);
        $progressData['sync_log_id'] = $syncLog->id;
        $progressData['status'] = 'validating';
        Cache::put('sync_progress:' . $jobId, $progressData, 3600);

        // Add to active jobs
        $activeJobs = Cache::get('sync:active_jobs', []);
        $activeJobs[$jobId] = ['batch_id' => $batchId];
        Cache::put('sync:active_jobs', $activeJobs, 3600);

        // With real database, this should work correctly
        $hasActiveJobs = $this->service->hasActiveSyncJobs();

        // Job should be considered active since sync_log exists
        $this->assertTrue($hasActiveJobs);

        // Progress data should still exist
        $progress = $this->service->getProgress($jobId);
        $this->assertNotNull($progress);
        $this->assertEquals('validating', $progress['status']);
        $this->assertEquals($syncLog->id, $progress['sync_log_id']);
    }

    public function test_data_consistency_check_with_missing_sync_log(): void
    {
        // This test verifies that jobs with non-existent sync_log_id are cleaned up

        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';

        // Initialize progress with a non-existent sync_log_id
        $this->service->initializeProgress($jobId, $batchId);
        $progressData = Cache::get('sync_progress:' . $jobId);
        $progressData['sync_log_id'] = 99999; // Non-existent ID
        $progressData['status'] = 'validating';
        Cache::put('sync_progress:' . $jobId, $progressData, 3600);

        // Add to active jobs
        $activeJobs = Cache::get('sync:active_jobs', []);
        $activeJobs[$jobId] = ['batch_id' => $batchId];
        Cache::put('sync:active_jobs', $activeJobs, 3600);

        // In production environment (not testing), this should trigger cleanup
        // For this integration test, we'll temporarily change the environment
        $originalEnv = app()->environment();
        app()->instance('env', 'production');

        try {
            $hasActiveJobs = $this->service->hasActiveSyncJobs();

            // Job should be cleaned up due to data inconsistency
            $this->assertFalse($hasActiveJobs);

            // Progress data should be marked as failed
            $progress = $this->service->getProgress($jobId);
            if ($progress) {
                $this->assertEquals('failed', $progress['status']);
                // The error message might vary, but the status should be failed
                $this->assertEquals('failed', $progress['status']);
            }
        } finally {
            // Restore original environment
            app()->instance('env', $originalEnv);
        }
    }

    public function test_integration_with_real_sync_log_lifecycle(): void
    {
        // This test verifies the complete lifecycle with real database operations

        $jobId = 'test-lifecycle-job';
        $batchId = 'test-lifecycle-batch';

        // Step 1: Initialize progress
        $this->service->initializeProgress($jobId, $batchId);
        $progress = $this->service->getProgress($jobId);
        $this->assertEquals('pending', $progress['status']);

        // Step 2: Create a real sync log
        $syncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => $batchId,
            'status' => 'processing',
            'started_at' => now(),
            'sync_config' => [],
        ]);

        // Step 3: Mark sync completed with real sync_log_id
        $completionData = [
            'sync_log_id' => $syncLog->id,
            'total_records' => 1000,
            'success_records' => 950,
            'failed_records' => 50,
        ];
        $this->service->markSyncCompleted($jobId, $completionData);

        $progress = $this->service->getProgress($jobId);
        $this->assertEquals('validating', $progress['status']);
        $this->assertEquals($syncLog->id, $progress['sync_log_id']);

        // Step 4: Complete validation
        $validationData = [
            'validation_results' => ['status' => 'passed'],
        ];
        $this->service->markValidationCompleted($jobId, $validationData);

        $progress = $this->service->getProgress($jobId);
        $this->assertEquals('completed', $progress['status']);

        // Step 5: Update the sync log to completed
        $syncLog->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);

        // Verify the sync log exists and is completed
        $this->assertDatabaseHas('sync_logs', [
            'id' => $syncLog->id,
            'batch_id' => $batchId,
            'status' => 'completed',
        ]);

        // Verify no active jobs remain
        $this->assertFalse($this->service->hasActiveSyncJobs());
    }

    public function test_batch_progress_tracking_with_database(): void
    {
        // Test batch progress tracking with real database operations

        $batchId = 'test-batch-tracking';

        // Create multiple jobs for the same batch
        $job1Id = 'job-1';
        $job2Id = 'job-2';

        $this->service->initializeProgress($job1Id, $batchId);
        $this->service->initializeProgress($job2Id, $batchId);

        // Create sync logs for both jobs
        $syncLog1 = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => $batchId,
            'status' => 'processing',
            'started_at' => now(),
            'sync_config' => [],
        ]);

        $syncLog2 = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => $batchId . '_2', // Use different batch_id to avoid unique constraint violation
            'status' => 'processing',
            'started_at' => now(),
            'sync_config' => [],
        ]);

        // Complete first job
        $this->service->markSyncCompleted($job1Id, ['sync_log_id' => $syncLog1->id]);
        $this->service->markValidationCompleted($job1Id, []);

        // Batch should still be active because job2 is still running
        $this->assertTrue($this->service->isBatchActive($batchId));

        // Complete second job
        $this->service->markSyncCompleted($job2Id, ['sync_log_id' => $syncLog2->id]);
        $this->service->markValidationCompleted($job2Id, []);

        // Now batch should be inactive
        $this->assertFalse($this->service->isBatchActive($batchId));

        // Verify both sync logs exist
        $this->assertDatabaseHas('sync_logs', ['id' => $syncLog1->id]);
        $this->assertDatabaseHas('sync_logs', ['id' => $syncLog2->id]);
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
