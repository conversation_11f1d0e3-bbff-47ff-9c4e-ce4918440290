<?php

declare(strict_types=1);

namespace Tests\Feature\Services;

use App\Jobs\SendEmailJob;
use App\Models\EmailTemplate;
use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use InvalidArgumentException;
use Tests\TestCase;

/**
 * Integration tests for EmailService
 * 
 * These tests verify the integration between EmailService, EmailTemplate model,
 * and the queue system for sending emails.
 */
final class EmailServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private EmailService $emailService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailService = app(EmailService::class);
        Queue::fake();
    }

    public function test_can_send_template_email_with_real_template(): void
    {
        // Create a test template in the database
        $template = EmailTemplate::factory()->create([
            'code' => 'test_template',
            'subject' => 'Test Subject: {name}',
            'content' => 'Hello {name}, this is a test email.',
            'is_active' => true,
        ]);

        $result = $this->emailService->sendTemplateEmail(
            'test_template',
            '<EMAIL>',
            ['name' => 'John Doe'],
            'John Doe'
        );

        $this->assertTrue($result);

        // Verify the job was pushed to the queue with correct parameters
        Queue::assertPushed(SendEmailJob::class, function ($job) {
            return $job->getToEmail() === '<EMAIL>' &&
                   $job->getToName() === 'John Doe' &&
                   $job->getTemplateCode() === 'test_template' &&
                   str_contains($job->getSubject(), 'John Doe') &&
                   str_contains($job->getContent(), 'Hello John Doe');
        });

        // Verify the template exists in the database
        $this->assertDatabaseHas('email_templates', [
            'code' => 'test_template',
            'is_active' => true,
        ]);
    }

    public function test_template_email_with_multiple_variables(): void
    {
        // Create a template with multiple variables
        $template = EmailTemplate::factory()->create([
            'code' => 'multi_var_template',
            'subject' => 'Welcome {name} to {company}',
            'content' => 'Hello {name}, welcome to {company}. Your ID is {user_id}.',
            'is_active' => true,
        ]);

        $result = $this->emailService->sendTemplateEmail(
            'multi_var_template',
            '<EMAIL>',
            [
                'name' => 'Jane Doe',
                'company' => 'ACME Corp',
                'user_id' => '12345'
            ],
            'Jane Doe'
        );

        $this->assertTrue($result);

        // Verify all variables were replaced correctly
        Queue::assertPushed(SendEmailJob::class, function ($job) {
            return $job->getSubject() === 'Welcome Jane Doe to ACME Corp' &&
                   $job->getContent() === 'Hello Jane Doe, welcome to ACME Corp. Your ID is 12345.';
        });
    }

    public function test_throws_exception_for_nonexistent_template(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template with code 'nonexistent' not found or inactive");

        $this->emailService->sendTemplateEmail(
            'nonexistent',
            '<EMAIL>',
            []
        );
    }

    public function test_throws_exception_for_inactive_template(): void
    {
        // Create an inactive template in the database
        EmailTemplate::factory()->create([
            'code' => 'inactive_template',
            'is_active' => false,
        ]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Email template with code 'inactive_template' not found or inactive");

        $this->emailService->sendTemplateEmail(
            'inactive_template',
            '<EMAIL>',
            []
        );
    }

    public function test_can_send_custom_email(): void
    {
        $result = $this->emailService->sendCustomEmail(
            '<EMAIL>',
            'Custom Subject',
            'Custom Content',
            'John Doe'
        );

        $this->assertTrue($result);

        Queue::assertPushed(SendEmailJob::class, function ($job) {
            return $job->getToEmail() === '<EMAIL>' &&
                   $job->getToName() === 'John Doe' &&
                   $job->getSubject() === 'Custom Subject' &&
                   $job->getContent() === 'Custom Content';
        });
    }

    public function test_template_email_integration_with_database(): void
    {
        // Test that the service correctly integrates with the database
        // by creating and using templates

        $template1 = EmailTemplate::factory()->create([
            'code' => 'welcome_email',
            'subject' => 'Welcome {name}!',
            'content' => 'Welcome to our platform, {name}!',
            'is_active' => true,
        ]);

        $template2 = EmailTemplate::factory()->create([
            'code' => 'goodbye_email',
            'subject' => 'Goodbye {name}',
            'content' => 'Thank you for using our service, {name}.',
            'is_active' => true,
        ]);

        // Send emails using both templates
        $result1 = $this->emailService->sendTemplateEmail(
            'welcome_email',
            '<EMAIL>',
            ['name' => 'Alice'],
            'Alice'
        );

        $result2 = $this->emailService->sendTemplateEmail(
            'goodbye_email',
            '<EMAIL>',
            ['name' => 'Bob'],
            'Bob'
        );

        $this->assertTrue($result1);
        $this->assertTrue($result2);

        // Verify both jobs were pushed
        Queue::assertPushed(SendEmailJob::class, 2);

        // Verify templates exist in database
        $this->assertDatabaseHas('email_templates', ['code' => 'welcome_email']);
        $this->assertDatabaseHas('email_templates', ['code' => 'goodbye_email']);
    }
}
