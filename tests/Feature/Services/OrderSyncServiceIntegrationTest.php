<?php

declare(strict_types=1);

namespace Tests\Feature\Services;

use App\Contracts\OrderSyncServiceInterface;
use App\Services\OrderSyncService;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use Tests\Traits\CreatesStoreTestTables;

/**
 * Integration tests for OrderSyncService
 * 
 * These tests use real database operations instead of mocking to test
 * the complete integration between the service and database layers.
 */
final class OrderSyncServiceIntegrationTest extends TestCase
{
    use RefreshDatabase, CreatesStoreTestTables;

    private OrderSyncServiceInterface $orderSyncService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orderSyncService = app(OrderSyncServiceInterface::class);

        // Create test store tables for integration testing
        $this->createStoreTestTables();

        // Clean up any existing test data
        $this->cleanupStoreTestData();
    }

    public function test_order_sync_service_can_be_resolved(): void
    {
        $service = app(OrderSyncServiceInterface::class);
        $this->assertInstanceOf(OrderSyncService::class, $service);
    }

    public function test_sync_creates_sync_log(): void
    {
        $syncLog = $this->orderSyncService->sync(['batch_size' => 50]);

        // Assert sync log was created
        $this->assertInstanceOf(SyncLog::class, $syncLog);
        $this->assertEquals('order_sync', $syncLog->sync_type);
        $this->assertEquals('completed', $syncLog->status);
        $this->assertNotNull($syncLog->batch_id);
        $this->assertNotNull($syncLog->started_at);
        $this->assertNotNull($syncLog->completed_at);

        // Check sync config was stored
        $this->assertIsArray($syncLog->sync_config);
        $this->assertEquals(50, $syncLog->sync_config['batch_size']);

        // Check that sync log exists in database
        $dbSyncLog = SyncLog::where('batch_id', $syncLog->batch_id)->first();
        $this->assertNotNull($dbSyncLog);
        $this->assertEquals('order_sync', $dbSyncLog->sync_type);
        $this->assertEquals('completed', $dbSyncLog->status);
    }

    public function test_get_sync_statistics_returns_array(): void
    {
        $statistics = $this->orderSyncService->getSyncStatistics();

        $this->assertIsArray($statistics);
        $this->assertArrayHasKey('total_syncs', $statistics);
        $this->assertArrayHasKey('successful_syncs', $statistics);
        $this->assertArrayHasKey('failed_syncs', $statistics);
        $this->assertArrayHasKey('success_rate', $statistics);
        $this->assertArrayHasKey('last_sync', $statistics);
    }

    public function test_cleanup_returns_cleanup_results(): void
    {
        // Create some test data first
        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test_cleanup_batch',
            'status' => 'completed',
            'started_at' => now()->subDays(2),
            'completed_at' => now()->subDays(2)->addMinutes(30),
            'sync_config' => ['test' => true],
        ]);

        // Use a longer retention period to avoid deleting data that might be locked
        $results = $this->orderSyncService->cleanup(30);

        $this->assertIsArray($results);
        $this->assertArrayHasKey('records_deleted', $results);
        $this->assertArrayHasKey('logs_deleted', $results);
        $this->assertArrayHasKey('cutoff_date', $results);

        // Results should be non-negative integers
        $this->assertGreaterThanOrEqual(0, $results['records_deleted']);
        $this->assertGreaterThanOrEqual(0, $results['logs_deleted']);
    }

    public function test_re_sync_requires_existing_batch(): void
    {
        // Create a sync log first
        $syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'test_batch_123',
            'status' => 'completed',
            'started_at' => now(),
            'sync_config' => ['test' => true],
        ]);

        $reSyncLog = $this->orderSyncService->reSync($syncLog->batch_id);

        $this->assertInstanceOf(SyncLog::class, $reSyncLog);
        $this->assertNotEquals($syncLog->batch_id, $reSyncLog->batch_id);
    }

    public function test_incremental_sync_uses_last_sync_time(): void
    {
        // Create a previous successful sync
        SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'previous_batch',
            'status' => 'completed',
            'started_at' => now()->subHour(),
            'completed_at' => now()->subHour()->addMinutes(30),
            'sync_config' => ['sync_type_detail' => 'full'],
        ]);

        $syncLog = $this->orderSyncService->sync(['incremental' => true, 'batch_size' => 25]);

        $this->assertEquals('completed', $syncLog->status);

        // Check sync config contains range info
        $syncConfig = $syncLog->sync_config;
        $this->assertEquals(25, $syncConfig['batch_size']);
        $this->assertTrue($syncConfig['incremental']);
        $this->assertArrayHasKey('sync_start_time', $syncConfig);
        $this->assertArrayHasKey('sync_end_time', $syncConfig);
        $this->assertEquals('incremental', $syncConfig['sync_type_detail']);

        // sync_start_time should be the completion time of the previous sync
        $this->assertNotEquals('1970-01-01 00:00:00', $syncConfig['sync_start_time']);
    }

    public function test_data_transformation_methods(): void
    {
        $orderSyncService = new OrderSyncService();

        // Test order state mapping
        $reflection = new \ReflectionClass($orderSyncService);
        $mapOrderStateMethod = $reflection->getMethod('mapOrderState');
        $mapOrderStateMethod->setAccessible(true);

        $this->assertEquals('completed', $mapOrderStateMethod->invoke($orderSyncService, 'fulfilled'));
        $this->assertEquals('cancelled', $mapOrderStateMethod->invoke($orderSyncService, 'cancelled'));
        $this->assertEquals('new', $mapOrderStateMethod->invoke($orderSyncService, 'new'));
        $this->assertEquals('unknown_state', $mapOrderStateMethod->invoke($orderSyncService, 'unknown_state'));

        // Test currency conversion
        $convertToMinorUnitsMethod = $reflection->getMethod('convertToMinorUnits');
        $convertToMinorUnitsMethod->setAccessible(true);

        $this->assertEquals(1000, $convertToMinorUnitsMethod->invoke($orderSyncService, 1000));
        $this->assertEquals(0, $convertToMinorUnitsMethod->invoke($orderSyncService, null));
        $this->assertEquals(2500, $convertToMinorUnitsMethod->invoke($orderSyncService, '2500'));
    }


}
