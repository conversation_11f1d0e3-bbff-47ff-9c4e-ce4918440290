<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Organisation;
use App\Models\Product;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Test DatabaseSeeder functionality
 */
final class DatabaseSeederTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable activity logging for seeder tests to improve performance
        // and avoid generating test-related activity logs
        $this->disableActivityLogging();
    }

    /**
     * Test that DatabaseSeeder creates test user with correct role assignment
     */
    public function test_database_seeder_creates_test_user_with_correct_roles(): void
    {
        // Run the seeder
        $this->seed();

        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($testUser, 'Test user should be created');
        $this->assertEquals('Test User', $testUser->name);

        // Check user is associated with organizations
        $organisations = $testUser->organisations;
        $this->assertCount(2, $organisations, 'Test user should belong to 2 organisations');

        // Find the active organisation (Tech Solutions Ltd)
        $activeOrg = Organisation::where('name', 'Tech Solutions Ltd')->first();
        $this->assertNotNull($activeOrg, 'Active organisation should exist');
        $this->assertEquals('active', $activeOrg->status);

        // Check if user belongs to the active organisation
        $this->assertTrue(
            $testUser->belongsToOrganisation($activeOrg->id),
            'Test user should belong to active organisation'
        );

        // Set team context for the active organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($activeOrg->id);

        // Check if user has owner role in the active organisation
        $hasOwnerRole = $testUser->hasRole('owner');
        $this->assertTrue($hasOwnerRole, 'Test user should have owner role in active organisation');

        // Verify the role is specifically for this organisation
        $ownerRole = $testUser->roles()
            ->where('roles.name', 'owner')
            ->where('roles.organisation_id', $activeOrg->id)
            ->first();
        
        $this->assertNotNull($ownerRole, 'Owner role should be assigned for the specific organisation');
        $this->assertEquals($activeOrg->id, $ownerRole->organisation_id);
        $this->assertEquals('api', $ownerRole->guard_name);
    }

    /**
     * Test that test user doesn't have owner role in other organisations
     */
    public function test_test_user_does_not_have_owner_role_in_other_organisations(): void
    {
        // Run the seeder
        $this->seed();

        $testUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($testUser);

        // Find the pending organisation (Healthcare Innovations)
        $pendingOrg = Organisation::where('name', 'Healthcare Innovations')->first();
        $this->assertNotNull($pendingOrg);

        // Set team context for the pending organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($pendingOrg->id);

        // Check if user does NOT have owner role in the pending organisation
        $hasOwnerRole = $testUser->hasRole('owner');
        $this->assertFalse($hasOwnerRole, 'Test user should NOT have owner role in pending organisation');
    }

    /**
     * Test that only one owner role exists per organisation
     */
    public function test_only_one_owner_role_per_organisation(): void
    {
        // Run the seeder
        $this->seed();

        $activeOrg = Organisation::where('name', 'Tech Solutions Ltd')->first();
        $this->assertNotNull($activeOrg);

        // Count users with owner role in this organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($activeOrg->id);

        $ownersCount = User::whereHas('roles', function ($query) use ($activeOrg) {
            $query->where('roles.name', 'owner')
                ->where('roles.organisation_id', $activeOrg->id);
        })->count();

        $this->assertEquals(1, $ownersCount, 'There should be exactly one owner per organisation');
    }

    /**
     * Test that ProductSeeder creates the expected products
     */
    public function test_product_seeder_creates_expected_products(): void
    {
        // Run the seeder
        $this->seed();

        // Check that products are created
        $this->assertDatabaseCount('products', 9);

        // Check specific products exist
        $this->assertDatabaseHas('products', [
            'sku' => 'GAME-001',
            'name' => 'Adventure Quest Deluxe',
            'store_variant_id' => 1001,
            'enabled' => true,
        ]);

        $this->assertDatabaseHas('products', [
            'sku' => 'GAME-003',
            'name' => 'Mystery Mansion Collector Edition',
            'store_variant_id' => 1003,
            'enabled' => false, // Disabled product
        ]);

        // Check product with null owner_id
        $this->assertDatabaseHas('products', [
            'sku' => 'GAME-004',
            'name' => 'Racing Legends Standard',
            'owner_id' => null,
        ]);

        // Verify price data is properly stored
        $product = Product::where('sku', 'GAME-001')->first();
        $this->assertNotNull($product);
        $this->assertEquals(2999, $product->current_price);
        $this->assertEquals(3999, $product->original_price);
        $this->assertIsArray($product->price_history);
        $this->assertCount(2, $product->price_history);
    }

    /**
     * Test that SyncLogSeeder creates sync logs with proper relationships
     */
    public function test_sync_log_seeder_creates_sync_logs_with_records(): void
    {
        // Count existing records before seeding
        $initialSyncLogsCount = SyncLog::count();
        $initialSyncRecordsCount = SyncRecord::count();

        // Run the seeder
        $this->seed();

        // Check that sync logs are created (should add 2 more)
        $this->assertEquals($initialSyncLogsCount + 2, SyncLog::count());

        // Check completed sync log
        $completedLog = SyncLog::where('status', 'completed')
            ->where('sync_type', 'product_sync')
            ->orderBy('created_at', 'desc')
            ->first();
        $this->assertNotNull($completedLog);
        $this->assertEquals('product_sync', $completedLog->sync_type);
        $this->assertEquals(5, $completedLog->total_records);
        $this->assertEquals(5, $completedLog->processed_records);
        $this->assertEquals(3, $completedLog->success_records);
        $this->assertEquals(2, $completedLog->failed_records);
        $this->assertNotNull($completedLog->started_at);
        $this->assertNotNull($completedLog->completed_at);
        $this->assertIsArray($completedLog->summary);
        $this->assertIsArray($completedLog->sync_config);

        // Check failed sync log
        $failedLog = SyncLog::where('status', 'failed')
            ->where('sync_type', 'product_sync')
            ->orderBy('created_at', 'desc')
            ->first();
        $this->assertNotNull($failedLog);
        $this->assertEquals('product_sync', $failedLog->sync_type);
        $this->assertEquals(3, $failedLog->total_records);
        $this->assertEquals(1, $failedLog->processed_records);
        $this->assertEquals(0, $failedLog->success_records);
        $this->assertEquals(1, $failedLog->failed_records);
        $this->assertNotNull($failedLog->error_message);
        $this->assertNull($failedLog->completed_at);

        // Check sync records are created (should add 6 more: 5 from first batch + 1 from second batch)
        $this->assertEquals($initialSyncRecordsCount + 6, SyncRecord::count());

        // Check sync records relationship
        $completedLogRecords = $completedLog->records;
        $this->assertCount(5, $completedLogRecords);

        $failedLogRecords = $failedLog->records;
        $this->assertCount(1, $failedLogRecords);

        // Check that validation_results are properly set
        $this->assertNotNull($completedLog->validation_results);
        $this->assertIsArray($completedLog->validation_results);
        $this->assertEquals('passed', $completedLog->validation_results['overall_status']);
        $this->assertEquals($completedLog->id, $completedLog->validation_results['sync_log_id']);
        $this->assertArrayHasKey('data_integrity', $completedLog->validation_results);

        $this->assertNotNull($failedLog->validation_results);
        $this->assertIsArray($failedLog->validation_results);
        $this->assertEquals('error', $failedLog->validation_results['overall_status']);
        $this->assertEquals($failedLog->id, $failedLog->validation_results['sync_log_id']);
    }

    /**
     * Test that sync records have proper status distribution and error messages
     */
    public function test_sync_records_have_proper_status_and_error_handling(): void
    {
        // Count existing records before seeding
        $initialSuccessCount = SyncRecord::where('status', 'success')->count();
        $initialFailedCount = SyncRecord::where('status', 'failed')->count();

        // Run the seeder
        $this->seed();

        // Check success records (should add 3 more)
        $successRecords = SyncRecord::where('status', 'success')->get();
        $this->assertEquals($initialSuccessCount + 3, $successRecords->count());

        // Get the newly created success records by checking for specific source_ids from the seeder
        $newSuccessRecords = SyncRecord::where('status', 'success')
            ->whereIn('source_id', ['1001', '1002', '1004'])
            ->get();
        $this->assertCount(3, $newSuccessRecords);

        foreach ($newSuccessRecords as $record) {
            $this->assertNotNull($record->source_data);
            $this->assertNotNull($record->transformed_data);
            $this->assertNotNull($record->target_id);
            $this->assertNull($record->error_message);
        }

        // Check failed records (should add 3 more)
        $failedRecords = SyncRecord::where('status', 'failed')->get();
        $this->assertEquals($initialFailedCount + 3, $failedRecords->count());

        // Get the newly created failed records by checking for specific source_ids from the seeder
        $newFailedRecords = SyncRecord::where('status', 'failed')
            ->whereIn('source_id', ['1003', '1005', '2001'])
            ->get();
        $this->assertCount(3, $newFailedRecords);

        foreach ($newFailedRecords as $record) {
            $this->assertNotNull($record->source_data);
            $this->assertNull($record->target_id);
            $this->assertNotNull($record->error_message);
        }

        // Check specific failed record error messages
        $this->assertDatabaseHas('sync_records', [
            'source_id' => '1003',
            'status' => 'failed',
            'error_message' => 'Validation failed: Product name exceeds maximum length limit',
        ]);

        $this->assertDatabaseHas('sync_records', [
            'source_id' => '1005',
            'status' => 'failed',
            'error_message' => 'Database constraint violation: Duplicate SKU code detected',
        ]);
    }

    /**
     * Test that sync data is properly structured for SyncController debugging
     */
    public function test_sync_data_supports_controller_debugging(): void
    {
        // Run the seeder
        $this->seed();

        // Test data structure matches SyncController index method expectations
        $logs = SyncLog::with(['records' => function($query) {
            $query->where('status', 'failed')->limit(5);
        }])->orderBy('created_at', 'desc')->get();

        $this->assertCount(2, $logs);

        // Check that completed log has failed records for debugging
        $completedLog = $logs->where('status', 'completed')->first();
        $this->assertNotNull($completedLog);
        $this->assertTrue($completedLog->failed_records > 0);

        $failedRecordsForDebugging = $completedLog->records;
        $this->assertGreaterThan(0, $failedRecordsForDebugging->count());
        $this->assertLessThanOrEqual(5, $failedRecordsForDebugging->count());
    }

    /**
     * Test that OrderSeeder creates orders with different states and regions
     */
    public function test_order_seeder_creates_diverse_orders(): void
    {
        // Run the seeder
        $this->seed();

        // Check total orders created
        $totalOrders = Order::count();
        $this->assertGreaterThanOrEqual(8, $totalOrders, 'Should create at least 8 orders');

        // Check different order states exist
        $completedOrders = Order::where('state', 'completed')->count();
        $cancelledOrders = Order::where('state', 'cancelled')->count();

        $this->assertGreaterThan(0, $completedOrders, 'Should have completed orders');
        $this->assertGreaterThan(0, $cancelledOrders, 'Should have cancelled orders');

        // Check different shipping countries exist
        $countries = Order::distinct('shipping_country')->pluck('shipping_country')->toArray();
        $expectedCountries = ['US', 'GB', 'DE', 'CA', 'FR', 'AU'];

        foreach ($expectedCountries as $country) {
            $this->assertContains($country, $countries, "Should have orders from {$country}");
        }

        // Check different currencies exist
        $currencies = Order::distinct('currency_code')->pluck('currency_code')->toArray();
        $expectedCurrencies = ['USD', 'GBP', 'EUR', 'CAD'];

        foreach ($expectedCurrencies as $currency) {
            $this->assertContains($currency, $currencies, "Should have orders in {$currency}");
        }
    }

    /**
     * Test that OrderSeeder creates orders with proper refund data
     */
    public function test_order_seeder_creates_refund_data(): void
    {
        // Run the seeder
        $this->seed();

        // Check refunded orders exist
        $refundedOrders = Order::whereNotNull('refund_total')
            ->where('refund_total', '>', 0)
            ->get();

        $this->assertGreaterThan(0, $refundedOrders->count(), 'Should have refunded orders');

        // Check refund statuses
        $successfulRefunds = Order::where('refund_status', 'success')->count();
        $this->assertGreaterThan(0, $successfulRefunds, 'Should have successful refunds');

        // Check partial vs full refunds
        $partialRefunds = Order::whereNotNull('refund_total')
            ->whereColumn('refund_total', '<', 'total_amount')
            ->count();

        $fullRefunds = Order::whereNotNull('refund_total')
            ->whereColumn('refund_total', '=', 'total_amount')
            ->count();

        $this->assertGreaterThan(0, $partialRefunds, 'Should have partial refunds');
        $this->assertGreaterThan(0, $fullRefunds, 'Should have full refunds');
    }

    /**
     * Test that OrderSeeder creates order items with proper relationships
     */
    public function test_order_seeder_creates_order_items_with_relationships(): void
    {
        // Run the seeder
        $this->seed();

        // Check total order items created
        $totalOrderItems = OrderItem::count();
        $this->assertGreaterThan(0, $totalOrderItems, 'Should create order items');

        // Check all order items have valid order relationships
        $orphanedItems = OrderItem::whereDoesntHave('order')->count();
        $this->assertEquals(0, $orphanedItems, 'All order items should belong to an order');

        // Check order items have required fields
        $itemsWithoutProduct = OrderItem::whereNull('product_name')->count();
        $this->assertEquals(0, $itemsWithoutProduct, 'All order items should have product names');

        $itemsWithoutVariant = OrderItem::where('store_variant_id', 0)->count();
        $this->assertEquals(0, $itemsWithoutVariant, 'All order items should have variant IDs');

        // Check pricing calculations are consistent
        $inconsistentItems = OrderItem::whereRaw('units_total != quantity * unit_price')->count();
        $this->assertEquals(0, $inconsistentItems, 'Units total should equal quantity × unit price');

        // Check refunded quantities are valid
        $invalidRefunds = OrderItem::where('quantity_refunded', '>', OrderItem::raw('quantity'))->count();
        $this->assertEquals(0, $invalidRefunds, 'Refunded quantity should not exceed ordered quantity');
    }

    /**
     * Test that OrderSeeder creates time-distributed orders for analytics
     */
    public function test_order_seeder_creates_time_distributed_orders(): void
    {
        // Run the seeder (RefreshDatabase already ensures clean state)
        $this->seed();

        // Debug: Check all orders and their completion dates
        $allOrders = Order::all(['id', 'store_order_id', 'completed_at']);
        $this->assertGreaterThan(0, $allOrders->count(), 'Should have orders created by seeder');

        // Check orders from different time periods
        $recentOrders = Order::whereDate('completed_at', '>=', today()->subDays(1))->count();
        $thisWeekOrders = Order::whereBetween('completed_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ])->count();
        $thisMonthOrders = Order::whereMonth('completed_at', now()->month)->count();

        // Debug output if assertions fail
        if ($recentOrders === 0) {
            $this->fail(
                'Should have recent orders (today or yesterday). All orders: ' .
                $allOrders->pluck('completed_at')->implode(', ') .
                '. Today is: ' . today()->toDateString()
            );
        }

        $this->assertGreaterThan(0, $recentOrders, 'Should have recent orders (today or yesterday)');
        $this->assertGreaterThan(0, $thisWeekOrders, 'Should have orders from this week');
        $this->assertGreaterThan(0, $thisMonthOrders, 'Should have orders from this month');

        // Check orders have realistic completion times
        $futureOrders = Order::where('completed_at', '>', now())->count();
        $this->assertEquals(0, $futureOrders, 'Should not have orders completed in the future');
    }

    /**
     * Test that OrderSeeder supports analytics requirements
     */
    public function test_order_seeder_supports_analytics_requirements(): void
    {
        // Run the seeder
        $this->seed();

        // Test daily sales data availability
        $dailySales = Order::selectRaw('DATE(completed_at) as date, SUM(total_amount) as total_sales')
            ->where('state', 'completed')
            ->groupBy('date')
            ->get();

        $this->assertGreaterThan(0, $dailySales->count(), 'Should have daily sales data');

        // Test regional sales data availability
        $regionalSales = Order::selectRaw('shipping_country, SUM(total_amount) as total_sales, COUNT(*) as order_count')
            ->where('state', 'completed')
            ->whereNotNull('shipping_country')
            ->groupBy('shipping_country')
            ->get();

        $this->assertGreaterThan(0, $regionalSales->count(), 'Should have regional sales data');

        // Test product statistics availability
        $productStats = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
            ->selectRaw('product_name, SUM(quantity) as total_quantity, SUM(total) as total_revenue')
            ->where('orders.state', 'completed')
            ->groupBy('product_name')
            ->get();

        $this->assertGreaterThan(0, $productStats->count(), 'Should have product statistics data');

        // Test refund statistics availability
        $refundStats = Order::selectRaw('COUNT(*) as refunded_orders, SUM(refund_total) as total_refunds')
            ->where('refund_status', 'success')
            ->whereNotNull('refund_total')
            ->first();

        $this->assertGreaterThan(0, $refundStats->refunded_orders, 'Should have refund statistics data');
        $this->assertGreaterThan(0, $refundStats->total_refunds, 'Should have refund amount data');
    }
}
