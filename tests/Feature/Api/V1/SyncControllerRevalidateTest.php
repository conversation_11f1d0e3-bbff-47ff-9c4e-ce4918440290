<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Jobs\OrderSyncValidationJob;
use App\Models\Organisation;
use App\Models\SyncLog;
use App\Models\User;
use App\Services\PermissionService;
use App\Services\SyncProgressService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class SyncControllerRevalidateTest extends TestCase
{
    use RefreshDatabase;

    private User $systemAdmin;
    private User $regularUser;
    private SyncProgressService $progressService;
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        // Clean up any existing cache data
        Cache::flush();

        $this->permissionService = app(PermissionService::class);
        $this->progressService = app(SyncProgressService::class);

        // Create test organisation
        $organisation = Organisation::factory()->create();

        // Create system admin user
        $this->systemAdmin = User::factory()->create();
        $adminRole = $this->permissionService->createRole('admin', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdmin->guard_name = 'system';
        $this->systemAdmin->assignRole($adminRole);

        // Create regular user with organisation membership
        $this->regularUser = User::factory()->create();
        $this->regularUser->organisations()->attach($organisation);
        $memberRole = $this->permissionService->createRole('member', 'api', $organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisation->id);
        $this->regularUser->guard_name = 'api';
        $this->regularUser->assignRole($memberRole);
    }

    public function test_revalidate_requires_authentication(): void
    {
        $syncLog = SyncLog::factory()->create(['sync_type' => 'order_sync']);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(401);
    }

    public function test_revalidate_requires_system_admin_permission(): void
    {
        Sanctum::actingAs($this->regularUser);
        $syncLog = SyncLog::factory()->create(['sync_type' => 'order_sync']);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(403);
    }

    public function test_revalidate_rejects_non_order_sync(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'product_sync',
            'status' => 'completed',
        ]);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(400)
            ->assertJsonPath('success', false)
            ->assertJsonPath('message', 'Invalid sync type for revalidation')
            ->assertJsonPath('errors.sync_type', 'product_sync')
            ->assertJsonPath('errors.expected', 'order_sync');

        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_revalidate_rejects_incomplete_sync(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'processing',
        ]);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(400)
            ->assertJsonPath('success', false)
            ->assertJsonPath('message', 'Sync must be completed before revalidation')
            ->assertJsonPath('errors.current_status', 'processing');

        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_revalidate_rejects_sync_without_validation_results(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'validation_results' => null,
        ]);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(400)
            ->assertJsonPath('success', false)
            ->assertJsonPath('message', 'No validation results found for this sync')
            ->assertJsonPath('errors.sync_log_id', $syncLog->id);

        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_revalidate_rejects_already_passed_validation(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'validation_results' => [
                'overall_status' => 'passed',
                'validation_completed_at' => now(),
            ],
        ]);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(400)
            ->assertJsonPath('success', false)
            ->assertJsonPath('message', 'Validation has already passed')
            ->assertJsonPath('errors.current_status', 'passed');

        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_revalidate_rejects_pending_validation(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'validation_results' => [
                'overall_status' => 'pending',
                'validation_started_at' => now(),
            ],
        ]);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(400)
            ->assertJsonPath('success', false)
            ->assertJsonPath('message', 'Validation is not eligible for retry')
            ->assertJsonPath('errors.current_status', 'pending');

        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_revalidate_rejects_when_validation_already_running(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'test-batch-123',
            'validation_results' => [
                'overall_status' => 'failed',
                'validation_completed_at' => now(),
            ],
        ]);

        // Mock active validation job
        Cache::put('sync_progress:job-123', [
            'batch_id' => 'test-batch-123',
            'status' => 'validating',
            'started_at' => now()->subMinutes(10)->toISOString(),
        ], 3600);

        Cache::put('sync:active_jobs', ['job-123' => ['batch_id' => 'test-batch-123']], 3600);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(409)
            ->assertJsonPath('success', false)
            ->assertJsonPath('message', 'Validation is already running for this sync')
            ->assertJsonPath('errors.batch_id', 'test-batch-123');

        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_revalidate_successfully_queues_validation_for_failed_validation(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'test-batch-456',
            'validation_results' => [
                'overall_status' => 'failed',
                'validation_completed_at' => now()->subHour(),
                'issues' => ['High mismatch rate: 10%'],
            ],
        ]);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonPath('message', 'Revalidation job queued successfully')
            ->assertJsonStructure([
                'data' => [
                    'sync_log_id',
                    'batch_id',
                    'validation_job_id',
                    'status',
                    'message',
                ],
            ])
            ->assertJsonPath('data.sync_log_id', $syncLog->id)
            ->assertJsonPath('data.batch_id', 'test-batch-456')
            ->assertJsonPath('data.status', 'queued');

        Queue::assertPushed(OrderSyncValidationJob::class, function ($job) use ($syncLog) {
            return $job->syncLogId === $syncLog->id &&
                   $job->batchId === 'test-batch-456';
        });

        // Check that validation results were updated
        $syncLog->refresh();
        $validationResults = $syncLog->validation_results;
        $this->assertEquals('pending', $validationResults['overall_status']);
        $this->assertArrayHasKey('revalidation_started_at', $validationResults);
        $this->assertArrayHasKey('revalidation_job_id', $validationResults);
    }

    public function test_revalidate_successfully_queues_validation_for_error_validation(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $syncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'test-batch-789',
            'validation_results' => [
                'overall_status' => 'error',
                'error_message' => 'Database connection failed',
                'validation_failed_at' => now()->subHour(),
            ],
        ]);

        $response = $this->postJson("/api/v1/sync/revalidate/{$syncLog->id}");

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonPath('message', 'Revalidation job queued successfully');

        Queue::assertPushed(OrderSyncValidationJob::class, function ($job) use ($syncLog) {
            return $job->syncLogId === $syncLog->id &&
                   $job->batchId === 'test-batch-789';
        });
    }

    public function test_batch_revalidate_requires_authentication(): void
    {
        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => [1, 2, 3]
        ]);

        $response->assertStatus(401);
    }

    public function test_batch_revalidate_requires_system_admin_permission(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => [1, 2, 3]
        ]);

        $response->assertStatus(403);
    }

    public function test_batch_revalidate_validates_input(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Test missing sync_log_ids
        $response = $this->postJson('/api/v1/sync/batch-revalidate', []);
        $response->assertStatus(422);

        // Test empty array
        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => []
        ]);
        $response->assertStatus(422);

        // Test too many IDs (over 50)
        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => range(1, 51)
        ]);
        $response->assertStatus(422);

        // Test invalid ID format
        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => ['invalid', 'ids']
        ]);
        $response->assertStatus(422);
    }

    public function test_batch_revalidate_handles_mixed_results(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        // Create sync logs with different statuses
        $validSyncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'valid-batch-123',
            'validation_results' => [
                'overall_status' => 'failed',
                'validation_completed_at' => now()->subHour(),
                'issues' => ['High mismatch rate: 10%'],
            ],
        ]);

        $invalidSyncLog = SyncLog::factory()->create([
            'sync_type' => 'product_sync', // Wrong type
            'status' => 'completed',
            'batch_id' => 'invalid-batch-456',
        ]);

        $passedSyncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'passed-batch-789',
            'validation_results' => [
                'overall_status' => 'passed',
                'validation_completed_at' => now()->subHour(),
            ],
        ]);

        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => [$validSyncLog->id, $invalidSyncLog->id, $passedSyncLog->id]
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonPath('message', 'Batch revalidation completed')
            ->assertJsonStructure([
                'data' => [
                    'successful',
                    'failed',
                    'total_requested',
                    'successful_count',
                    'failed_count',
                ],
            ])
            ->assertJsonPath('data.total_requested', 3)
            ->assertJsonPath('data.successful_count', 1)
            ->assertJsonPath('data.failed_count', 2);

        // Check successful revalidation
        $responseData = $response->json();
        $this->assertCount(1, $responseData['data']['successful']);
        $this->assertEquals($validSyncLog->id, $responseData['data']['successful'][0]['sync_log_id']);

        // Check failed revalidations
        $this->assertCount(2, $responseData['data']['failed']);
        $failedIds = array_column($responseData['data']['failed'], 'sync_log_id');
        $this->assertContains($invalidSyncLog->id, $failedIds);
        $this->assertContains($passedSyncLog->id, $failedIds);

        // Verify only one job was dispatched
        Queue::assertPushed(OrderSyncValidationJob::class, 1);
        Queue::assertPushed(OrderSyncValidationJob::class, function ($job) use ($validSyncLog) {
            return $job->syncLogId === $validSyncLog->id;
        });
    }

    public function test_batch_revalidate_all_failed(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        // Create sync logs that are not eligible for revalidation
        $syncLog1 = SyncLog::factory()->create([
            'sync_type' => 'product_sync', // Wrong type
            'status' => 'completed',
        ]);

        $syncLog2 = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'processing', // Not completed
        ]);

        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => [$syncLog1->id, $syncLog2->id]
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonPath('message', 'All batch revalidation requests failed')
            ->assertJsonPath('data.total_requested', 2)
            ->assertJsonPath('data.successful_count', 0)
            ->assertJsonPath('data.failed_count', 2);

        Queue::assertNotPushed(OrderSyncValidationJob::class);
    }

    public function test_batch_revalidate_handles_nonexistent_sync_log(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $validSyncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'validation_results' => [
                'overall_status' => 'failed',
                'validation_completed_at' => now()->subHour(),
            ],
        ]);

        $response = $this->postJson('/api/v1/sync/batch-revalidate', [
            'sync_log_ids' => [$validSyncLog->id, 99999] // 99999 doesn't exist
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.total_requested', 2)
            ->assertJsonPath('data.successful_count', 1)
            ->assertJsonPath('data.failed_count', 1);

        $responseData = $response->json();
        $failedItem = $responseData['data']['failed'][0];
        $this->assertEquals(99999, $failedItem['sync_log_id']);
        $this->assertEquals('not_found', $failedItem['reason']);

        Queue::assertPushed(OrderSyncValidationJob::class, 1);
    }

    public function test_revalidation_candidates_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/sync/revalidation-candidates');

        $response->assertStatus(401);
    }

    public function test_revalidation_candidates_requires_system_admin_permission(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/sync/revalidation-candidates');

        $response->assertStatus(403);
    }

    public function test_revalidation_candidates_returns_eligible_sync_logs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create various sync logs
        $eligibleSyncLog1 = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'eligible-batch-1',
            'completed_at' => now()->subHours(2),
            'validation_results' => [
                'overall_status' => 'failed',
                'validation_completed_at' => now()->subHours(2),
                'issues' => ['High mismatch rate: 10%'],
            ],
        ]);

        $eligibleSyncLog2 = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'eligible-batch-2',
            'completed_at' => now()->subHours(1),
            'validation_results' => [
                'overall_status' => 'error',
                'error_message' => 'Database connection failed',
                'validation_failed_at' => now()->subHours(1),
            ],
        ]);

        // Create ineligible sync logs
        SyncLog::factory()->create([
            'sync_type' => 'product_sync', // Wrong type
            'status' => 'completed',
            'validation_results' => ['overall_status' => 'failed'],
        ]);

        SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'processing', // Not completed
            'validation_results' => ['overall_status' => 'failed'],
        ]);

        SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'validation_results' => ['overall_status' => 'passed'], // Already passed
        ]);

        SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'validation_results' => null, // No validation results
        ]);

        $response = $this->getJson('/api/v1/sync/revalidation-candidates');

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonPath('message', 'Revalidation candidates retrieved successfully')
            ->assertJsonStructure([
                'data' => [
                    'sync_logs' => [
                        'data' => [
                            '*' => [
                                'id',
                                'sync_type',
                                'batch_id',
                                'status',
                                'validation_results',
                                'completed_at',
                            ]
                        ]
                    ],
                    'meta' => [
                        'current_page',
                        'per_page',
                        'total',
                        'last_page',
                    ],
                    'summary' => [
                        'total_candidates',
                        'failed_validations',
                        'error_validations',
                    ],
                ],
            ]);

        $responseData = $response->json();

        // Should return 2 eligible sync logs
        $this->assertEquals(2, $responseData['data']['summary']['total_candidates']);
        $this->assertEquals(1, $responseData['data']['summary']['failed_validations']);
        $this->assertEquals(1, $responseData['data']['summary']['error_validations']);

        // Check that the returned logs are the eligible ones
        $returnedIds = array_column($responseData['data']['sync_logs']['data'], 'id');
        $this->assertContains($eligibleSyncLog1->id, $returnedIds);
        $this->assertContains($eligibleSyncLog2->id, $returnedIds);
    }

    public function test_revalidation_candidates_excludes_active_validation_jobs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $syncLogWithActiveValidation = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'active-validation-batch',
            'validation_results' => [
                'overall_status' => 'failed',
                'validation_completed_at' => now()->subHour(),
            ],
        ]);

        $syncLogWithoutActiveValidation = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'completed',
            'batch_id' => 'no-active-validation-batch',
            'validation_results' => [
                'overall_status' => 'failed',
                'validation_completed_at' => now()->subHour(),
            ],
        ]);

        // Mock active validation job for the first sync log
        Cache::put('sync_progress:validation-job-123', [
            'batch_id' => 'active-validation-batch',
            'status' => 'validating',
            'started_at' => now()->subMinutes(10)->toISOString(),
        ], 3600);

        Cache::put('sync:active_jobs', [
            'validation-job-123' => ['batch_id' => 'active-validation-batch']
        ], 3600);

        $response = $this->getJson('/api/v1/sync/revalidation-candidates');

        $response->assertStatus(200);

        $responseData = $response->json();

        // Should return only 1 eligible sync log (excluding the one with active validation)
        $this->assertEquals(1, $responseData['data']['summary']['total_candidates']);

        // Check that only the sync log without active validation is returned
        $returnedIds = array_column($responseData['data']['sync_logs']['data'], 'id');
        $this->assertNotContains($syncLogWithActiveValidation->id, $returnedIds);
        $this->assertContains($syncLogWithoutActiveValidation->id, $returnedIds);
    }

    public function test_revalidation_candidates_supports_pagination(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create multiple eligible sync logs
        for ($i = 1; $i <= 5; $i++) {
            SyncLog::factory()->create([
                'sync_type' => 'order_sync',
                'status' => 'completed',
                'batch_id' => "batch-{$i}",
                'completed_at' => now()->subHours($i),
                'validation_results' => [
                    'overall_status' => 'failed',
                    'validation_completed_at' => now()->subHours($i),
                ],
            ]);
        }

        // Test pagination
        $response = $this->getJson('/api/v1/sync/revalidation-candidates?per_page=2&page=1');

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.per_page', 2)
            ->assertJsonPath('data.meta.current_page', 1);

        $responseData = $response->json();
        $this->assertCount(2, $responseData['data']['sync_logs']['data']);
    }
}
