<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\FinancialStatement;
use App\Models\Order;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class FinancialStatementControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private PermissionService $permissionService;
    private User $systemAdmin;
    private User $organisationOwner;
    private User $organisationMember;
    private User $unauthorizedUser;
    private Organisation $organisation;
    private Organisation $otherOrganisation;
    private FinancialStatement $financialStatement;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create organisations
        $this->organisation = Organisation::factory()->create(['code' => 'TEST001']);
        $this->otherOrganisation = Organisation::factory()->create(['code' => 'TEST002']);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $memberRole = collect($orgRoles)->firstWhere('name', 'member');

        // Create system admin
        $this->systemAdmin = User::factory()->create();
        $this->permissionService->assignRoleToUser($this->systemAdmin, $systemAdminRole);

        // Create organisation owner
        $this->organisationOwner = User::factory()->create();
        $this->organisationOwner->organisations()->attach($this->organisation->id);
        $this->permissionService->assignRoleToUser($this->organisationOwner, $ownerRole);

        // Create organisation member
        $this->organisationMember = User::factory()->create();
        $this->organisationMember->organisations()->attach($this->organisation->id);
        $this->permissionService->assignRoleToUser($this->organisationMember, $memberRole);

        // Create unauthorized user
        $this->unauthorizedUser = User::factory()->create();

        // Create a financial statement (monthly to avoid conflicts with quarterly test)
        $this->financialStatement = FinancialStatement::factory()->monthly()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);
    }

    public function test_index_returns_financial_statements_for_system_admin(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create additional financial statements
        FinancialStatement::factory()->count(3)->create([
            'organisation_id' => $this->organisation->id,
        ]);
        FinancialStatement::factory()->count(2)->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $response = $this->getJson('/api/v1/financial-statements');

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'report_type',
                            'report_title',
                            'report_code',
                            'start_date',
                            'end_date',
                            'organisation_id',
                            'status',
                            'total_amount',
                            'total_quantity',
                            'total_orders',
                            'organisation',
                        ],
                    ],
                    'meta',
                ],
            ]);

        // System admin should see all financial statements
        $this->assertCount(6, $response->json('data.data')); // 4 created + 1 in setUp + 1 additional
    }

    public function test_index_returns_only_organisation_financial_statements_for_owner(): void
    {
        Sanctum::actingAs($this->organisationOwner);

        // Create additional financial statements
        FinancialStatement::factory()->count(2)->create([
            'organisation_id' => $this->organisation->id,
        ]);
        FinancialStatement::factory()->count(3)->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $response = $this->getJson('/api/v1/financial-statements');

        $response->assertOk();

        // Organisation owner should only see their organisation's financial statements
        $data = $response->json('data.data');
        $this->assertCount(3, $data); // 2 created + 1 in setUp

        foreach ($data as $statement) {
            $this->assertEquals($this->organisation->id, $statement['organisation_id']);
        }
    }

    public function test_index_filters_by_status(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PUBLISHED,
        ]);

        $response = $this->getJson('/api/v1/financial-statements?status=' . FinancialStatement::STATUS_PUBLISHED);

        $response->assertOk();

        $data = $response->json('data.data');
        $this->assertCount(1, $data);
        $this->assertEquals(FinancialStatement::STATUS_PUBLISHED, $data[0]['status']);
    }

    public function test_index_filters_by_report_type(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create a quarterly financial statement
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
        ]);

        // Create a monthly financial statement (should not be returned)
        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'report_type' => FinancialStatement::TYPE_MONTHLY,
        ]);

        $response = $this->getJson('/api/v1/financial-statements?report_type=' . FinancialStatement::TYPE_QUARTERLY);

        $response->assertOk();

        $data = $response->json('data.data');
        $this->assertCount(1, $data);
        $this->assertEquals(FinancialStatement::TYPE_QUARTERLY, $data[0]['report_type']);
    }

    public function test_index_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/financial-statements');

        $response->assertUnauthorized();
    }

    public function test_index_denies_access_to_unauthorized_user(): void
    {
        Sanctum::actingAs($this->unauthorizedUser);

        $response = $this->getJson('/api/v1/financial-statements');

        $response->assertForbidden();
    }

    public function test_store_creates_financial_statement_for_system_admin(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
            'total_amount' => 100000, // $1000.00 in cents
            'total_quantity' => 50,
            'total_orders' => 10,
            'remarks' => 'Test monthly report',
        ];

        $response = $this->postJson('/api/v1/financial-statements', $data);

        $response->assertCreated()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'report_type',
                    'report_code',
                    'report_title',
                    'organisation',
                ],
            ]);

        $this->assertDatabaseHas('financial_statements', [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);
    }

    public function test_store_denies_access_to_organisation_owner(): void
    {
        Sanctum::actingAs($this->organisationOwner);

        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ];

        $response = $this->postJson('/api/v1/financial-statements', $data);

        $response->assertForbidden();
    }

    public function test_store_denies_access_to_organisation_member(): void
    {
        Sanctum::actingAs($this->organisationMember);

        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ];

        $response = $this->postJson('/api/v1/financial-statements', $data);

        $response->assertForbidden();
    }

    public function test_store_validates_required_fields(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson('/api/v1/financial-statements', []);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['report_type', 'start_date', 'end_date', 'organisation_id']);
    }

    public function test_store_validates_date_range(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-01-31',
            'end_date' => '2024-01-01', // End date before start date
            'organisation_id' => $this->organisation->id,
        ];

        $response = $this->postJson('/api/v1/financial-statements', $data);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['end_date']);
    }

    public function test_store_with_orders(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $orders = Order::factory()->count(3)->create();

        $data = [
            'report_type' => FinancialStatement::TYPE_MONTHLY,
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
            'order_ids' => $orders->pluck('id')->toArray(),
        ];

        $response = $this->postJson('/api/v1/financial-statements', $data);

        $response->assertCreated();

        // Get the created financial statement from the response
        $responseData = $response->json('data');
        $financialStatement = FinancialStatement::with('orders')->find($responseData['id']);

        $this->assertCount(3, $financialStatement->orders);
    }

    public function test_show_returns_financial_statement_for_authorized_user(): void
    {
        Sanctum::actingAs($this->organisationOwner);

        $response = $this->getJson("/api/v1/financial-statements/{$this->financialStatement->id}");

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'report_type',
                    'report_title',
                    'organisation',
                    'product_sales',
                ],
            ]);
    }

    public function test_show_returns_product_sales_data_with_correct_structure(): void
    {
        // Create test data with products and orders
        $product1 = \App\Models\Product::factory()->create([
            'name' => 'Test Game 1',
            'slug' => 'test-game-1',
            'package' => 'Standard Edition',
            'store_variant_id' => 1001,
            'owner_id' => $this->organisation->code,
        ]);

        $product2 = \App\Models\Product::factory()->create([
            'name' => 'Test Game 2',
            'slug' => 'test-game-2',
            'package' => 'Deluxe Edition',
            'store_variant_id' => 1002,
            'owner_id' => $this->organisation->code,
        ]);

        // Create orders with different countries
        $order1 = \App\Models\Order::factory()->create([
            'state' => 'completed',
            'shipping_country' => 'US',
            'total_amount' => 5000, // $50.00
        ]);

        $order2 = \App\Models\Order::factory()->create([
            'state' => 'completed',
            'shipping_country' => 'CA',
            'total_amount' => 3000, // $30.00
        ]);

        // Create order items
        \App\Models\OrderItem::factory()->create([
            'order_id' => $order1->id,
            'store_variant_id' => 1001,
            'quantity' => 2,
            'total' => 4000, // $40.00
            'quantity_refunded' => 0,
        ]);

        \App\Models\OrderItem::factory()->create([
            'order_id' => $order2->id,
            'store_variant_id' => 1002,
            'quantity' => 1,
            'total' => 2500, // $25.00
            'quantity_refunded' => 0,
        ]);

        // Create financial statement and attach orders
        $financialStatement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
        ]);
        $financialStatement->orders()->attach([$order1->id, $order2->id]);

        Sanctum::actingAs($this->organisationOwner);

        $response = $this->getJson("/api/v1/financial-statements/{$financialStatement->id}");

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'product_sales' => [
                        '*' => [
                            'product_name',
                            'product_slug',
                            'product_package',
                            'total_quantity',
                            'total_sales',
                            'total_sales_formatted',
                            'total_orders',
                            'regions' => [
                                '*' => [
                                    'country',
                                    'total_quantity',
                                    'total_sales',
                                    'total_sales_formatted',
                                    'order_count',
                                ],
                            ],
                        ],
                    ],
                ],
            ]);

        $responseData = $response->json('data');
        $productSales = $responseData['product_sales'];

        // Verify we have product sales data
        $this->assertNotEmpty($productSales);

        // Verify product data structure
        foreach ($productSales as $product) {
            $this->assertArrayHasKey('product_name', $product);
            $this->assertArrayHasKey('regions', $product);
            $this->assertIsArray($product['regions']);

            // Verify region data structure
            foreach ($product['regions'] as $region) {
                $this->assertArrayHasKey('country', $region);
                $this->assertArrayHasKey('total_quantity', $region);
                $this->assertArrayHasKey('total_sales', $region);
                $this->assertArrayHasKey('total_sales_formatted', $region);
                $this->assertArrayHasKey('order_count', $region);
            }
        }
    }

    public function test_show_denies_access_to_unauthorized_user(): void
    {
        Sanctum::actingAs($this->unauthorizedUser);

        $response = $this->getJson("/api/v1/financial-statements/{$this->financialStatement->id}");

        $response->assertForbidden();
    }

    public function test_show_returns_not_found_for_nonexistent_statement(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/financial-statements/99999');

        $response->assertNotFound();
    }

    public function test_update_denies_access_to_organisation_owner(): void
    {
        Sanctum::actingAs($this->organisationOwner);

        $data = [
            'remarks' => 'Updated remarks',
            'total_amount' => 200000,
        ];

        $response = $this->putJson("/api/v1/financial-statements/{$this->financialStatement->id}", $data);

        $response->assertForbidden();
    }

    public function test_update_denies_access_to_unauthorized_user(): void
    {
        Sanctum::actingAs($this->unauthorizedUser);

        $data = ['remarks' => 'Unauthorized update'];

        $response = $this->putJson("/api/v1/financial-statements/{$this->financialStatement->id}", $data);

        $response->assertForbidden();
    }

    public function test_update_allows_system_admin(): void
    {
        $statement = FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PENDING_AUDIT,
        ]);

        Sanctum::actingAs($this->systemAdmin);

        $data = ['total_amount' => 300000];

        $response = $this->putJson("/api/v1/financial-statements/{$statement->id}", $data);

        $response->assertOk();
    }

    public function test_publish_denies_access_to_organisation_owner(): void
    {
        Sanctum::actingAs($this->organisationOwner);

        $response = $this->postJson("/api/v1/financial-statements/{$this->financialStatement->id}/publish");

        $response->assertForbidden();
    }

    public function test_publish_allows_system_admin(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson("/api/v1/financial-statements/{$this->financialStatement->id}/publish");

        $response->assertOk();

        $this->financialStatement->refresh();
        $this->assertEquals(FinancialStatement::STATUS_PUBLISHED, $this->financialStatement->status);
    }

    public function test_by_organisation_returns_statements_for_specific_organisation(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        FinancialStatement::factory()->count(2)->create([
            'organisation_id' => $this->organisation->id,
        ]);
        FinancialStatement::factory()->create([
            'organisation_id' => $this->otherOrganisation->id,
        ]);

        $response = $this->getJson("/api/v1/financial-statements/organisation/{$this->organisation->id}");

        $response->assertOk();

        $data = $response->json('data.data');
        $this->assertCount(3, $data); // 2 created + 1 in setUp

        foreach ($data as $statement) {
            $this->assertEquals($this->organisation->id, $statement['organisation_id']);
        }
    }

    public function test_statistics_returns_aggregated_data(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        FinancialStatement::factory()->create([
            'organisation_id' => $this->organisation->id,
            'status' => FinancialStatement::STATUS_PUBLISHED,
            'report_type' => FinancialStatement::TYPE_QUARTERLY,
        ]);

        $response = $this->getJson('/api/v1/financial-statements/statistics');

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total_statements',
                    'pending_audit',
                    'published',
                    'by_type',
                    'total_amount',
                    'total_orders',
                    'total_quantity',
                ],
            ]);
    }
}
