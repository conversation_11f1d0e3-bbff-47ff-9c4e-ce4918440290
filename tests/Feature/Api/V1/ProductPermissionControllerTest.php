<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Product Permission Controller Test
 *
 * Tests API endpoints for product permission management including individual
 * product permissions and organisation-level product permission queries.
 */
final class ProductPermissionControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $rootUser;
    private User $adminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $regularUser;
    private Organisation $organisation;
    private Product $product;
    private Product $product2;
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create organisation first
        $this->organisation = Organisation::factory()->create([
            'status' => 'active'
        ]);

        // Create system and organisation roles
        $this->permissionService->createSystemRoles();
        $this->permissionService->createDefaultRoles($this->organisation->id);

        // Create test users
        $this->rootUser = User::factory()->create();
        $this->adminUser = User::factory()->create();
        $this->ownerUser = User::factory()->create();
        $this->memberUser = User::factory()->create();
        $this->regularUser = User::factory()->create();

        // Associate organisation users with organisation
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $rootRole = Role::where('name', 'root')->where('guard_name', 'system')->whereNull('organisation_id')->first();
        $adminRole = Role::where('name', 'admin')->where('guard_name', 'system')->whereNull('organisation_id')->first();

        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($rootRole);

        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($adminRole);

        // Assign organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $ownerRole = Role::where('name', 'owner')->where('organisation_id', $this->organisation->id)->first();
        $memberRole = Role::where('name', 'member')->where('organisation_id', $this->organisation->id)->first();

        $this->ownerUser->assignRole($ownerRole);
        $this->memberUser->assignRole($memberRole);

        // Create products for the organisation
        $this->product = Product::factory()->create([
            'owner_id' => $this->organisation->code,
            'name' => 'Test Product 1',
            'slug' => 'test-product-1'
        ]);

        $this->product2 = Product::factory()->create([
            'owner_id' => $this->organisation->code,
            'name' => 'Test Product 2',
            'slug' => 'test-product-2'
        ]);
    }

    public function test_grant_access_success_as_root_user(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports',
            'notes' => 'Test permission grant'
        ]);

        if ($response->status() !== 201) {
            dump($response->json());
        }

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'permission' => [
                        'id',
                        'user_id',
                        'product_id',
                        'permission_type',
                        'expires_at',
                        'granted_at',
                        'granted_by',
                        'notes'
                    ]
                ]
            ]);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $this->rootUser->id,
            'notes' => 'Test permission grant'
        ]);
    }

    public function test_grant_access_success_as_organization_owner(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $this->ownerUser->id
        ]);
    }

    public function test_grant_access_with_expiration_date(): void
    {
        Sanctum::actingAs($this->rootUser);

        $expiresAt = Carbon::now()->addDays(30)->toISOString();

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports',
            'expires_at' => $expiresAt
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);
    }

    public function test_grant_access_forbidden_for_member(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(403);
    }

    public function test_grant_access_validation_errors(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Missing user_id
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['user_id']);

        // Invalid permission type
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'invalid-permission'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permission_type']);

        // Invalid expiration date
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports',
            'expires_at' => Carbon::now()->subDays(1)->toISOString()
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['expires_at']);
    }

    public function test_revoke_access_success(): void
    {
        // Create a permission first
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        Sanctum::actingAs($this->rootUser);

        $response = $this->deleteJson("/api/v1/products/{$this->product->id}/permissions/{$this->regularUser->id}", [
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseMissing('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);
    }

    public function test_revoke_access_not_found(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->deleteJson("/api/v1/products/{$this->product->id}/permissions/{$this->regularUser->id}", [
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(404);
    }

    public function test_get_authorized_users_success(): void
    {
        // Create permissions for multiple users
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        ProductPermission::factory()->create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson("/api/v1/products/{$this->product->id}/authorized-users");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'users' => [
                        '*' => ['id', 'name', 'email']
                    ]
                ]
            ]);

        $users = $response->json('data.users');
        $this->assertCount(2, $users);
    }

    public function test_get_user_accessible_products_success(): void
    {
        // Create permission for the user
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/products/accessible');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'products' => [
                        '*' => [
                            'id',
                            'name',
                            'slug',
                            'organisation' => ['id', 'name', 'code']
                        ]
                    ]
                ]
            ]);
    }

    public function test_get_user_product_permissions_success(): void
    {
        // Create permission
        ProductPermission::factory()->create([
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_by' => $this->ownerUser->id,
            'notes' => 'Test permission'
        ]);

        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson("/api/v1/users/{$this->regularUser->id}/product-permissions");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'permissions' => [
                        '*' => [
                            'id',
                            'product' => [
                                'id',
                                'name',
                                'slug',
                                'organisation' => ['id', 'name']
                            ],
                            'permission_type',
                            'expires_at',
                            'granted_at',
                            'granted_by',
                            'notes'
                        ]
                    ]
                ]
            ]);
    }

    public function test_grant_multiple_access_success(): void
    {
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson('/api/v1/product-permissions/grant-multiple', [
            'user_id' => $this->regularUser->id,
            'product_ids' => [$this->product->id, $this->product2->id],
            'permission_type' => 'view-reports',
            'notes' => 'Bulk permission grant'
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'granted_count',
                    'permissions'
                ]
            ]);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports'
        ]);

        $this->assertDatabaseHas('product_permissions', [
            'user_id' => $this->regularUser->id,
            'product_id' => $this->product2->id,
            'permission_type' => 'view-reports'
        ]);
    }

    public function test_unauthenticated_access_denied(): void
    {
        $response = $this->postJson("/api/v1/products/{$this->product->id}/permissions", [
            'user_id' => $this->regularUser->id,
            'permission_type' => 'view-reports'
        ]);

        $response->assertStatus(401);
    }

    // ========================================
    // Organisation Product Permissions Tests
    // ========================================

    public function test_root_user_can_view_organisation_product_permissions(): void
    {
        // Create some product permissions
        ProductPermission::create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_at' => now(),
            'granted_by' => $this->ownerUser->id,
        ]);

        ProductPermission::create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product2->id,
            'permission_type' => 'edit-reports',
            'granted_at' => now(),
            'granted_by' => $this->ownerUser->id,
        ]);

        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => $this->organisation->id
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'organisation_id',
                    'users' => [
                        '*' => [
                            'user' => ['id', 'name', 'email'],
                            'permissions' => [
                                '*' => [
                                    'id',
                                    'product' => ['id', 'name', 'slug', 'code'],
                                    'permission_type',
                                    'expires_at',
                                    'granted_at',
                                    'granted_by',
                                    'notes'
                                ]
                            ],
                            'permission_summary' => [
                                'total_products',
                                'by_type',
                                'expiring_soon'
                            ]
                        ]
                    ],
                    'summary' => [
                        'total_users_with_permissions',
                        'total_permissions',
                        'permission_types_breakdown'
                    ]
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals($this->organisation->id, $data['organisation_id']);
        $this->assertCount(1, $data['users']); // Only memberUser has permissions
        $this->assertEquals(2, $data['summary']['total_permissions']);
    }

    public function test_organisation_owner_can_view_their_organisation_permissions(): void
    {
        ProductPermission::create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_at' => now(),
            'granted_by' => $this->ownerUser->id,
        ]);

        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => $this->organisation->id
        ]));

        $response->assertStatus(200);
    }

    public function test_regular_user_cannot_view_organisation_permissions(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => $this->organisation->id
        ]));

        $response->assertStatus(403);
    }

    public function test_filter_organisation_permissions_by_permission_type(): void
    {
        // Create permissions with different types
        ProductPermission::create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_at' => now(),
            'granted_by' => $this->ownerUser->id,
        ]);

        ProductPermission::create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product2->id,
            'permission_type' => 'edit-reports',
            'granted_at' => now(),
            'granted_by' => $this->ownerUser->id,
        ]);

        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => $this->organisation->id,
            'permission_type' => 'view-reports'
        ]));

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertCount(1, $data['users']);
        $this->assertCount(1, $data['users'][0]['permissions']);
        $this->assertEquals('view-reports', $data['users'][0]['permissions'][0]['permission_type']);
    }

    public function test_include_expired_organisation_permissions(): void
    {
        // Create expired permission
        ProductPermission::create([
            'user_id' => $this->memberUser->id,
            'product_id' => $this->product->id,
            'permission_type' => 'view-reports',
            'granted_at' => now()->subDays(10),
            'expires_at' => now()->subDays(1), // Expired
            'granted_by' => $this->ownerUser->id,
        ]);

        Sanctum::actingAs($this->rootUser);

        // Test without including expired (should return empty)
        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => $this->organisation->id,
            'include_expired' => false
        ]));

        $response->assertStatus(200);
        $this->assertCount(0, $response->json('data.users'));

        // Test including expired (should return the permission)
        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => $this->organisation->id,
            'include_expired' => true
        ]));

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data.users'));
    }

    public function test_organisation_permissions_validation_errors(): void
    {
        Sanctum::actingAs($this->rootUser);

        // Missing organisation_id
        $response = $this->getJson('/api/v1/product-permissions/organisation');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['organisation_id']);

        // Invalid organisation_id
        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => 99999
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['organisation_id']);

        // Invalid permission_type
        $response = $this->getJson('/api/v1/product-permissions/organisation?' . http_build_query([
            'organisation_id' => $this->organisation->id,
            'permission_type' => 'invalid-type'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permission_type']);
    }
}
