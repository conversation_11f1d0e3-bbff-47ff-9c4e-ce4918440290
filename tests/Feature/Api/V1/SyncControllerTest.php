<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Jobs\ProductSyncJob;
use App\Jobs\OrderSyncJob;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Models\User;
use App\Services\ProductSyncService;
use App\Contracts\OrderSyncServiceInterface;
use App\Services\PermissionService;
use App\Services\SyncProgressService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class SyncControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $systemAdmin;
    private User $regularUser;
    private SyncLog $syncLog;
    private PermissionService $permissionService;
    private SyncProgressService $progressService;

    protected function setUp(): void
    {
        parent::setUp();

        // Clean up any existing cache data
        Cache::flush();

        $this->permissionService = app(PermissionService::class);
        $this->progressService = app(SyncProgressService::class);

        // Create test organisation
        $organisation = Organisation::factory()->create();

        // Create system admin user
        $this->systemAdmin = User::factory()->create();
        $adminRole = $this->permissionService->createRole('admin', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdmin->guard_name = 'system';
        $this->systemAdmin->assignRole($adminRole);

        // Create regular user with organisation membership
        $this->regularUser = User::factory()->create();
        $this->regularUser->organisations()->attach($organisation);
        $memberRole = $this->permissionService->createRole('member', 'api', $organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisation->id);
        $this->regularUser->guard_name = 'api';
        $this->regularUser->assignRole($memberRole);

        // Create test sync log
        $this->syncLog = SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'test_batch_123',
            'status' => 'completed',
            'started_at' => now()->subMinutes(10),
            'completed_at' => now()->subMinutes(5),
            'total_records' => 100,
            'processed_records' => 100,
            'success_records' => 95,
            'failed_records' => 5,
            'summary' => ['test' => 'data'],
            'sync_config' => ['incremental' => true],
        ]);

        // Create test sync records (failed record first to match expected order)
        $baseTime = now();

        // Use DB::table to ensure exact timestamps
        DB::table('sync_records')->insert([
            [
                'batch_id' => 'test_batch_123',
                'source_table' => 'sylius_product_variant',
                'source_id' => '2',
                'target_table' => 'products',
                'status' => 'failed',
                'error_message' => 'Test error',
                'source_data' => null,
                'transformed_data' => null,
                'target_id' => null,
                'created_at' => $baseTime, // Latest timestamp
                'updated_at' => $baseTime,
            ],
            [
                'batch_id' => 'test_batch_123',
                'source_table' => 'sylius_product_variant',
                'source_id' => '1',
                'target_table' => 'products',
                'target_id' => '1',
                'status' => 'success',
                'source_data' => json_encode(['test' => 'data']),
                'transformed_data' => json_encode(['test' => 'transformed']),
                'error_message' => null,
                'created_at' => $baseTime->copy()->subMinutes(1), // Earlier timestamp
                'updated_at' => $baseTime->copy()->subMinutes(1),
            ]
        ]);
    }

    public function test_index_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/sync/logs');

        $response->assertStatus(401);
    }

    public function test_index_requires_system_admin_access(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/sync/logs');

        $response->assertStatus(403);
    }

    public function test_index_returns_sync_logs_for_system_admin(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/sync/logs');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'batch_id',
                            'sync_type',
                            'status',
                            'status_label',
                            'started_at',
                            'completed_at',
                            'duration',
                            'total_records',
                            'processed_records',
                            'success_records',
                            'failed_records',
                            'progress_percentage',
                        ]
                    ],
                    'meta' => [
                        'total',
                        'per_page',
                        'current_page',
                        'last_page',
                    ]
                ],
                'timestamp'
            ])
            ->assertJsonPath('data.data.0.batch_id', 'test_batch_123')
            ->assertJsonPath('data.data.0.sync_type', 'product_sync')
            ->assertJsonPath('data.data.0.status', 'completed');
    }

    public function test_index_filters_by_sync_type(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create another sync log with different type
        SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => 'order_batch_123',
            'status' => 'pending',
            'started_at' => now(),
        ]);

        $response = $this->getJson('/api/v1/sync/logs?sync_type=product_sync');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data.data')
            ->assertJsonPath('data.data.0.sync_type', 'product_sync');
    }

    public function test_index_filters_by_status(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create another sync log with different status
        SyncLog::create([
            'sync_type' => 'product_sync',
            'batch_id' => 'failed_batch_123',
            'status' => 'failed',
            'started_at' => now(),
        ]);

        $response = $this->getJson('/api/v1/sync/logs?status=completed');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data.data')
            ->assertJsonPath('data.data.0.status', 'completed');
    }

    public function test_show_requires_authentication(): void
    {
        $response = $this->getJson("/api/v1/sync/logs/{$this->syncLog->id}");

        $response->assertStatus(401);
    }

    public function test_show_requires_system_admin_access(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson("/api/v1/sync/logs/{$this->syncLog->id}");

        $response->assertStatus(403);
    }

    public function test_show_returns_sync_log_details_for_system_admin(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson("/api/v1/sync/logs/{$this->syncLog->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'batch_id',
                    'sync_type',
                    'status',
                    'total_records',
                    'processed_records',
                    'success_records',
                    'failed_records',
                    'progress_percentage',
                    'started_at',
                    'completed_at',
                ],
                'timestamp'
            ])
            ->assertJsonPath('data.batch_id', 'test_batch_123')
            ->assertJsonPath('data.sync_type', 'product_sync')
            ->assertJsonPath('data.status', 'completed')
            ->assertJsonMissing(['data.records']); // Ensure records are not included
    }

    public function test_show_returns_404_for_non_existent_sync_log(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/sync/logs/999999');

        $response->assertStatus(404);
    }

    public function test_trigger_requires_authentication(): void
    {
        $response = $this->postJson('/api/v1/sync/trigger');

        $response->assertStatus(401);
    }

    public function test_trigger_requires_system_admin_access(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->postJson('/api/v1/sync/trigger');

        $response->assertStatus(403);
    }

    public function test_trigger_starts_new_product_sync_for_system_admin(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson('/api/v1/sync/trigger', [
            'sync_type' => 'product_sync',
            'incremental' => true,
            'batch_size' => 50,
            'timeout' => 300,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'batch_id',
                    'sync_type',
                    'status',
                    'message',
                ],
            ])
            ->assertJsonPath('data.status', 'queued')
            ->assertJsonPath('data.sync_type', 'product_sync');

        Queue::assertPushed(ProductSyncJob::class);
    }

    public function test_trigger_starts_new_order_sync_for_system_admin(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson('/api/v1/sync/trigger', [
            'sync_type' => 'order_sync',
            'incremental' => true,
            'batch_size' => 50,
            'timeout' => 300,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'batch_id',
                    'sync_type',
                    'status',
                    'message',
                ],
            ])
            ->assertJsonPath('data.status', 'queued')
            ->assertJsonPath('data.sync_type', 'order_sync');

        Queue::assertPushed(OrderSyncJob::class);
    }

    public function test_trigger_validates_input_parameters(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson('/api/v1/sync/trigger', [
            'sync_type' => 'invalid',
            'incremental' => 'invalid',
            'batch_size' => 2000, // exceeds max
            'timeout' => 30, // below min
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sync_type', 'incremental', 'batch_size', 'timeout']);
    }

    public function test_trigger_requires_sync_type(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson('/api/v1/sync/trigger', [
            'incremental' => true,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sync_type']);
    }

    public function test_trigger_prevents_concurrent_sync_jobs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Mock active sync jobs
        $this->progressService->initializeProgress('test-job-1', 'test-batch-1');

        $response = $this->postJson('/api/v1/sync/trigger', [
            'sync_type' => 'product_sync',
            'incremental' => true,
        ]);

        $response->assertStatus(409)
            ->assertJsonPath('errors.active_jobs', function ($activeJobs) {
                return is_array($activeJobs) && count($activeJobs) > 0;
            });
    }

    public function test_retry_requires_authentication(): void
    {
        $response = $this->postJson("/api/v1/sync/retry/{$this->syncLog->id}");

        $response->assertStatus(401);
    }

    public function test_retry_requires_system_admin_access(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->postJson("/api/v1/sync/retry/{$this->syncLog->id}");

        $response->assertStatus(403);
    }

    public function test_retry_only_works_for_failed_sync_logs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Current sync log has 'completed' status
        $response = $this->postJson("/api/v1/sync/retry/{$this->syncLog->id}");

        $response->assertStatus(422)
            ->assertJsonPath('errors.current_status', 'completed');
    }

    public function test_retry_starts_new_sync_for_failed_log(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        // Create a failed sync log
        $failedSyncLog = SyncLog::factory()->create([
            'status' => 'failed',
            'sync_config' => ['incremental' => true, 'batch_size' => 100],
        ]);

        $response = $this->postJson("/api/v1/sync/retry/{$failedSyncLog->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'new_batch_id',
                    'original_batch_id',
                    'status',
                    'message',
                ],
            ])
            ->assertJsonPath('data.status', 'queued')
            ->assertJsonPath('data.original_batch_id', $failedSyncLog->batch_id);

        Queue::assertPushed(ProductSyncJob::class, function ($job) use ($failedSyncLog) {
            return $job->isRetry === true && $job->originalBatchId === $failedSyncLog->batch_id;
        });
    }

    public function test_retry_failed_order_sync_for_system_admin(): void
    {
        Queue::fake();
        Sanctum::actingAs($this->systemAdmin);

        // Create a failed order sync log
        $failedSyncLog = SyncLog::factory()->create([
            'sync_type' => 'order_sync',
            'status' => 'failed',
        ]);

        $response = $this->postJson("/api/v1/sync/retry/{$failedSyncLog->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'new_batch_id',
                    'original_batch_id',
                    'sync_type',
                    'status',
                    'message',
                ],
            ])
            ->assertJsonPath('data.status', 'queued')
            ->assertJsonPath('data.sync_type', 'order_sync')
            ->assertJsonPath('data.original_batch_id', $failedSyncLog->batch_id);

        Queue::assertPushed(OrderSyncJob::class, function ($job) use ($failedSyncLog) {
            return $job->isRetry === true && $job->originalBatchId === $failedSyncLog->batch_id;
        });
    }

    public function test_retry_prevents_concurrent_sync_jobs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create a failed sync log
        $failedSyncLog = SyncLog::factory()->create([
            'status' => 'failed',
        ]);

        // Mock active sync jobs
        $this->progressService->initializeProgress('test-job-1', 'test-batch-1');

        $response = $this->postJson("/api/v1/sync/retry/{$failedSyncLog->id}");

        $response->assertStatus(409)
            ->assertJsonPath('errors.active_jobs', function ($activeJobs) {
                return is_array($activeJobs) && count($activeJobs) > 0;
            });
    }

    public function test_retry_returns_404_for_non_existent_sync_log(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson('/api/v1/sync/retry/999999');

        $response->assertStatus(404);
    }

    public function test_sync_config_only_visible_to_system_admins(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson("/api/v1/sync/logs/{$this->syncLog->id}");

        $response->assertStatus(200)
            ->assertJsonPath('data.sync_config.incremental', true);

        // Test that regular users don't see sync_config (if they had access)
        // This is handled by the resource class based on user permissions
    }

    public function test_progress_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/sync/progress?job_id=test-job-id');

        $response->assertStatus(401);
    }

    public function test_progress_requires_system_admin_access(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/sync/progress?job_id=test-job-id');

        $response->assertStatus(403);
    }

    public function test_progress_returns_job_progress_by_job_id(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize progress
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';
        $this->progressService->initializeProgress($jobId, $batchId);
        $this->progressService->updateProgress($jobId, [
            'processed_chunks' => 5,
            'total_chunks' => 10,
            'percentage' => 50,
            'status' => 'processing',
        ]);

        $response = $this->getJson("/api/v1/sync/progress?job_id={$jobId}");

        $response->assertStatus(200)
            ->assertJsonPath('data.job_id', $jobId)
            ->assertJsonPath('data.batch_id', $batchId)
            ->assertJsonPath('data.percentage', 50)
            ->assertJsonPath('data.status', 'processing');
    }

    public function test_progress_returns_job_progress_by_batch_id(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize progress
        $jobId = 'test-job-id';
        $batchId = 'test-batch-id';
        $this->progressService->initializeProgress($jobId, $batchId);

        $response = $this->getJson("/api/v1/sync/progress?batch_id={$batchId}");

        $response->assertStatus(200)
            ->assertJsonPath('data.job_id', $jobId)
            ->assertJsonPath('data.batch_id', $batchId);
    }

    public function test_progress_returns_completed_job_progress_by_batch_id(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize progress
        $jobId = 'test-completed-job-id';
        $batchId = 'test-completed-batch-id';
        $this->progressService->initializeProgress($jobId, $batchId);

        // Mark job as completed (this removes it from active jobs list)
        $this->progressService->markCompleted($jobId, [
            'processed_chunks' => 10,
            'total_chunks' => 10,
            'success_count' => 100,
            'error_count' => 0,
        ]);

        // Should still be able to query by batch_id even after completion
        $response = $this->getJson("/api/v1/sync/progress?batch_id={$batchId}");

        $response->assertStatus(200)
            ->assertJsonPath('data.job_id', $jobId)
            ->assertJsonPath('data.batch_id', $batchId)
            ->assertJsonPath('data.status', 'completed')
            ->assertJsonPath('data.percentage', 100);
    }

    public function test_progress_returns_404_for_non_existent_job(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/sync/progress?job_id=non-existent-job');

        $response->assertStatus(404);
    }

    public function test_progress_validates_required_parameters(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/sync/progress');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['job_id']);
    }

    public function test_active_jobs_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/sync/active-jobs');

        $response->assertStatus(401);
    }

    public function test_active_jobs_requires_system_admin_access(): void
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/sync/active-jobs');

        $response->assertStatus(403);
    }

    public function test_active_jobs_returns_active_sync_jobs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize some active jobs
        $this->progressService->initializeProgress('job-1', 'batch-1');
        $this->progressService->initializeProgress('job-2', 'batch-2');

        $response = $this->getJson('/api/v1/sync/active-jobs');

        $response->assertStatus(200)
            ->assertJsonPath('data.count', 2)
            ->assertJsonPath('data.active_jobs.0.job_id', 'job-1')
            ->assertJsonPath('data.active_jobs.1.job_id', 'job-2');
    }

    public function test_active_jobs_returns_empty_when_no_active_jobs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/sync/active-jobs');

        $response->assertStatus(200)
            ->assertJsonPath('data.count', 0)
            ->assertJsonPath('data.active_jobs', []);
    }

    public function test_progress_returns_validating_status(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize progress and mark as validating
        $jobId = 'test-validating-job-id';
        $batchId = 'test-validating-batch-id';
        $this->progressService->initializeProgress($jobId, $batchId);
        $this->progressService->markSyncCompleted($jobId, [
            'sync_log_id' => 123,
            'total_records' => 1000,
            'success_records' => 950,
            'failed_records' => 50,
        ]);

        $response = $this->getJson("/api/v1/sync/progress?job_id={$jobId}");

        $response->assertStatus(200)
            ->assertJsonPath('data.job_id', $jobId)
            ->assertJsonPath('data.batch_id', $batchId)
            ->assertJsonPath('data.status', 'validating')
            ->assertJsonPath('data.percentage', 90);
    }

    public function test_progress_returns_validation_completed_status(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize progress and complete validation
        $jobId = 'test-validation-completed-job-id';
        $batchId = 'test-validation-completed-batch-id';
        $this->progressService->initializeProgress($jobId, $batchId);
        $this->progressService->markSyncCompleted($jobId, []);
        $this->progressService->markValidationCompleted($jobId, [
            'validation_results' => ['status' => 'passed'],
        ]);

        $response = $this->getJson("/api/v1/sync/progress?job_id={$jobId}");

        $response->assertStatus(200)
            ->assertJsonPath('data.job_id', $jobId)
            ->assertJsonPath('data.batch_id', $batchId)
            ->assertJsonPath('data.status', 'completed')
            ->assertJsonPath('data.percentage', 100)
            ->assertJsonPath('data.validation_results.status', 'passed');
    }

    public function test_progress_returns_validation_failed_status(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize progress and fail validation
        $jobId = 'test-validation-failed-job-id';
        $batchId = 'test-validation-failed-batch-id';
        $this->progressService->initializeProgress($jobId, $batchId);
        $this->progressService->markSyncCompleted($jobId, []);
        $this->progressService->markValidationFailed($jobId, [
            'error_message' => 'Data integrity check failed',
            'validation_results' => ['status' => 'failed'],
        ]);

        $response = $this->getJson("/api/v1/sync/progress?job_id={$jobId}");

        $response->assertStatus(200)
            ->assertJsonPath('data.job_id', $jobId)
            ->assertJsonPath('data.batch_id', $batchId)
            ->assertJsonPath('data.status', 'failed')
            ->assertJsonPath('data.error_message', 'Data integrity check failed')
            ->assertJsonPath('data.validation_results.status', 'failed');
    }

    public function test_active_jobs_includes_validating_jobs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Initialize some jobs in different states
        $this->progressService->initializeProgress('pending-job', 'pending-batch');
        $this->progressService->initializeProgress('processing-job', 'processing-batch');
        $this->progressService->updateProgress('processing-job', ['status' => 'processing']);
        $this->progressService->initializeProgress('validating-job', 'validating-batch');
        $this->progressService->markSyncCompleted('validating-job', []);

        $response = $this->getJson('/api/v1/sync/active-jobs');

        $response->assertStatus(200)
            ->assertJsonPath('data.count', 3);

        $activeJobs = $response->json('data.active_jobs');
        $statuses = array_column($activeJobs, 'status');

        $this->assertContains('pending', $statuses);
        $this->assertContains('processing', $statuses);
        $this->assertContains('validating', $statuses);
    }

    public function test_trigger_prevents_concurrent_sync_jobs_with_validating_status(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Mock active sync job in validating state
        $this->progressService->initializeProgress('test-job-1', 'test-batch-1');
        $this->progressService->markSyncCompleted('test-job-1', []);

        $response = $this->postJson('/api/v1/sync/trigger', [
            'sync_type' => 'product_sync',
            'incremental' => true,
        ]);

        $response->assertStatus(409)
            ->assertJsonPath('errors.active_jobs', function ($activeJobs) {
                return is_array($activeJobs) && count($activeJobs) > 0;
            });
    }

    public function test_active_jobs_excludes_timed_out_validating_jobs(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create a job that appears to be validating but is actually timed out
        $jobId = 'timed-out-job';
        $batchId = 'timed-out-batch';

        // Initialize the job
        $this->progressService->initializeProgress($jobId, $batchId);

        // Mark it as sync completed (validating status) but with old timestamp
        $oldTimestamp = now()->subHours(2)->toISOString(); // 2 hours ago, exceeds 1-hour validation timeout

        // Manually set the cache data to simulate a timed-out validating job
        Cache::put('sync_progress:' . $jobId, [
            'job_id' => $jobId,
            'batch_id' => $batchId,
            'status' => 'validating',
            'percentage' => 90,
            'processed_chunks' => 1000,
            'total_chunks' => 1000,
            'elapsed_time' => 7200, // 2 hours
            'started_at' => $oldTimestamp,
            'updated_at' => $oldTimestamp,
            'sync_completed_at' => $oldTimestamp,
            'total_records' => 1000,
            'success_records' => 1000,
            'failed_records' => 0,
            'progress_percentage' => 100,
        ], 3600);

        // The active jobs endpoint should exclude this timed-out job
        $response = $this->getJson('/api/v1/sync/active-jobs');

        $response->assertStatus(200)
            ->assertJsonPath('data.count', 0)
            ->assertJsonPath('data.active_jobs', []);

        // Verify the job was marked as failed due to timeout
        $progressData = $this->progressService->getProgress($jobId);
        $this->assertEquals('failed', $progressData['status']);
        $this->assertStringContainsString('timed out', $progressData['error_message']);
    }

    public function test_trigger_cleans_up_timed_out_jobs_automatically(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        // Create a job that started 3 hours ago and has been validating for over 2 hours (should be timed out)
        $jobId = 'test-job-timeout';
        $batchId = 'test-batch-timeout';

        // Manually create progress data with old timestamp (validation timeout is 1 hour)
        $progressData = [
            'job_id' => $jobId,
            'batch_id' => $batchId,
            'status' => 'validating',
            'percentage' => 90,
            'processed_chunks' => 0,
            'total_chunks' => 0,
            'elapsed_time' => 0,
            'started_at' => now()->subHours(4)->toISOString(),
            'sync_completed_at' => now()->subHours(2)->toISOString(), // Validation started 2 hours ago
            'updated_at' => now()->subHours(2)->toISOString(),
        ];

        Cache::put('sync_progress:' . $jobId, $progressData, 3600);

        $activeJobs = [$jobId => ['batch_id' => $batchId, 'started_at' => now()->subHours(4)->toISOString()]];
        Cache::put('sync:active_jobs', $activeJobs, 3600);

        // Trigger should succeed because timed out job gets cleaned up
        $response = $this->postJson('/api/v1/sync/trigger', [
            'sync_type' => 'product_sync',
            'incremental' => false,
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.status', 'queued');

        // Verify the timed out job was marked as failed
        $updatedProgress = Cache::get('sync_progress:' . $jobId);
        $this->assertEquals('failed', $updatedProgress['status']);
        $this->assertStringContainsString('timed out', $updatedProgress['error_message']);
    }

    public function test_cleanup_job_removes_stuck_job(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $jobId = '550e8400-e29b-41d4-a716-************'; // Valid UUID
        $batchId = '550e8400-e29b-41d4-a716-************'; // Valid UUID

        // Create a stuck job
        $this->progressService->initializeProgress($jobId, $batchId);
        $this->progressService->markSyncCompleted($jobId, []);

        // Verify job exists and is active
        $this->assertTrue($this->progressService->hasActiveSyncJobs());

        $response = $this->postJson('/api/v1/sync/cleanup-job', [
            'job_id' => $jobId,
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.job_id', $jobId)
            ->assertJsonPath('data.batch_id', $batchId)
            ->assertJsonPath('data.original_status', 'validating');

        // Verify job is no longer active
        $this->assertFalse($this->progressService->hasActiveSyncJobs());

        // Verify job is marked as failed
        $jobProgress = $this->progressService->getProgress($jobId);
        $this->assertEquals('failed', $jobProgress['status']);
        $this->assertStringContainsString('manually cleaned up', $jobProgress['error_message']);
    }

    public function test_cleanup_job_returns_404_for_nonexistent_job(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->postJson('/api/v1/sync/cleanup-job', [
            'job_id' => '550e8400-e29b-41d4-a716-************', // Valid UUID but nonexistent
        ]);

        $response->assertStatus(404)
            ->assertJsonPath('message', __('sync.job_not_found'));
    }

    public function test_job_details_returns_job_information(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $jobId = '550e8400-e29b-41d4-a716-************'; // Valid UUID
        $batchId = '550e8400-e29b-41d4-a716-************'; // Valid UUID

        // Create a job
        $this->progressService->initializeProgress($jobId, $batchId);

        $response = $this->getJson('/api/v1/sync/job-details?job_id=' . $jobId);

        $response->assertStatus(200)
            ->assertJsonPath('data.job_details.job_id', $jobId)
            ->assertJsonPath('data.job_details.batch_id', $batchId)
            ->assertJsonPath('data.job_details.status', 'pending')
            ->assertJsonPath('data.job_details.is_in_active_list', true)
            ->assertJsonPath('data.job_details.is_timed_out', false);
    }

    public function test_job_details_returns_404_for_nonexistent_job(): void
    {
        Sanctum::actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/sync/job-details?job_id=550e8400-e29b-41d4-a716-************'); // Valid UUID but nonexistent

        $response->assertStatus(404)
            ->assertJsonPath('message', __('sync.job_not_found'));
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }
}
