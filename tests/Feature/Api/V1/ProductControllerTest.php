<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Product;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use App\Services\ProductPermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class ProductControllerTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private ProductPermissionService $productPermissionService;
    private User $adminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $userWithProductPermission;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;
    private Product $product;
    private Product $anotherProduct;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);
        $this->productPermissionService = app(ProductPermissionService::class);

        // Create test organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'code' => 'TEST002',
            'status' => 'active',
        ]);

        // Create test products
        $this->product = Product::factory()->create([
            'owner_id' => $this->organisation->code,
            'name' => 'Test Product',
            'code' => 'TEST-PRODUCT-001',
            'sku' => 'SKU001',
            'enabled' => true,
        ]);

        $this->anotherProduct = Product::factory()->create([
            'owner_id' => $this->anotherOrganisation->code,
            'name' => 'Another Product',
            'code' => 'TEST-PRODUCT-002',
            'sku' => 'SKU002',
            'enabled' => true,
        ]);

        // Create admin user
        $this->adminUser = User::factory()->create();
        $adminRole = $this->permissionService->createRole('admin', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($adminRole);

        // Create owner user
        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation);
        $ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->guard_name = 'api';
        $this->ownerUser->assignRole($ownerRole);

        // Create member user
        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation);
        $memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->memberUser->guard_name = 'api';
        $this->memberUser->assignRole($memberRole);

        // Create user with only product-level permissions
        $this->userWithProductPermission = User::factory()->create();
    }

    public function test_admin_can_list_all_products(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/products');

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'code',
                            'sku',
                            'enabled',
                            'organisation',
                        ]
                    ],
                    'meta' => [
                        'total',
                        'per_page',
                        'current_page',
                    ]
                ],
                'timestamp'
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertGreaterThan(0, $response->json('data.meta.total'));
    }

    public function test_member_can_list_organisation_products(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/products');

        $response->assertOk();
        $this->assertTrue($response->json('success'));
    }

    public function test_can_search_products_by_name(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/products?search=Test Product');

        $response->assertOk();
        $products = $response->json('data.data');
        $this->assertNotEmpty($products);
        $this->assertStringContainsString('Test Product', $products[0]['name']);
    }

    public function test_can_search_products_by_code(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/products?search=TEST-PRODUCT');

        $response->assertOk();
        $products = $response->json('data.data');
        $this->assertNotEmpty($products);
        $this->assertStringContainsString('TEST-PRODUCT', $products[0]['code']);
    }

    public function test_can_search_products_by_sku(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/products?search=SKU001');

        $response->assertOk();
        $products = $response->json('data.data');
        $this->assertNotEmpty($products);
        $this->assertStringContainsString('SKU001', $products[0]['sku']);
    }

    public function test_can_filter_products_by_organisation(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson("/api/v1/products?organisation_id={$this->organisation->id}");

        $response->assertOk();
        $products = $response->json('data.data');
        
        foreach ($products as $product) {
            $this->assertEquals($this->organisation->id, $product['organisation']['id']);
        }
    }

    public function test_can_filter_products_by_enabled_status(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/products?enabled=true');

        $response->assertOk();
        $products = $response->json('data.data');
        
        foreach ($products as $product) {
            $this->assertTrue($product['enabled']);
        }
    }

    public function test_admin_can_view_product_details(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson("/api/v1/products/{$this->product->id}");

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'code',
                    'sku',
                    'enabled',
                    'organisation' => [
                        'id',
                        'name',
                        'code',
                    ]
                ],
                'timestamp'
            ]);

        $this->assertEquals($this->product->id, $response->json('data.id'));
        $this->assertEquals($this->product->name, $response->json('data.name'));
    }

    public function test_member_can_view_organisation_product_details(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson("/api/v1/products/{$this->product->id}");

        $response->assertOk();
        $this->assertEquals($this->product->id, $response->json('data.id'));
    }

    public function test_unauthenticated_user_cannot_access_products(): void
    {
        $response = $this->getJson('/api/v1/products');

        $response->assertUnauthorized();
    }

    public function test_pagination_works_correctly(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/products?per_page=5&page=1');

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'meta' => [
                        'per_page',
                        'current_page',
                        'total',
                    ]
                ]
            ]);

        $this->assertEquals(5, $response->json('data.meta.per_page'));
        $this->assertEquals(1, $response->json('data.meta.current_page'));
    }

    // ========================================
    // Authorization Tests
    // ========================================

    public function test_owner_can_list_products(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/products');

        $response->assertOk();
        $this->assertTrue($response->json('success'));
    }

    public function test_user_with_only_product_permission_cannot_list_products(): void
    {
        // Grant product permission to user
        $this->productPermissionService->grantProductAccess($this->userWithProductPermission, $this->product);

        Sanctum::actingAs($this->userWithProductPermission);

        $response = $this->getJson('/api/v1/products');

        $response->assertForbidden();
    }

    public function test_user_with_only_product_permission_can_view_specific_product(): void
    {
        // Grant product permission to user
        $this->productPermissionService->grantProductAccess($this->userWithProductPermission, $this->product);

        Sanctum::actingAs($this->userWithProductPermission);

        $response = $this->getJson("/api/v1/products/{$this->product->id}");

        $response->assertOk();
        $this->assertEquals($this->product->id, $response->json('data.id'));
    }

    public function test_user_cannot_access_other_organisation_products_via_filter(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson("/api/v1/products?organisation_id={$this->anotherOrganisation->id}");

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_admin_can_access_any_organisation_products(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson("/api/v1/products?organisation_id={$this->anotherOrganisation->id}");

        $response->assertOk();
    }

    public function test_member_cannot_access_other_organisation_product_details(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson("/api/v1/products/{$this->anotherProduct->id}");

        $response->assertForbidden();
    }
}
