<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\EmailTemplate;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class EmailTemplateControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $rootUser;
    private User $adminUser;
    private User $organisationOwner;
    private User $organisationMember;
    private Organisation $organisation;
    private EmailTemplate $emailTemplate;
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create();

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $rootRole = collect($systemRoles)->firstWhere('name', 'root');
        $adminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $memberRole = collect($orgRoles)->firstWhere('name', 'member');

        // Create users with different roles
        $this->rootUser = User::factory()->create();
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($rootRole);

        $this->adminUser = User::factory()->create();
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($adminRole);

        $this->organisationOwner = User::factory()->create();
        $this->organisationOwner->organisations()->attach($this->organisation->id);
        $this->permissionService->assignRoleToUser($this->organisationOwner, $ownerRole);

        $this->organisationMember = User::factory()->create();
        $this->organisationMember->organisations()->attach($this->organisation->id);
        $this->permissionService->assignRoleToUser($this->organisationMember, $memberRole);

        // Create test email template
        $this->emailTemplate = EmailTemplate::factory()->create([
            'name' => 'Test Template',
            'code' => 'test_template',
            'subject' => 'Test Subject with {user_name}',
            'content' => 'Hello {user_name}, this is a test email.',
            'variables' => ['user_name'],
            'is_active' => true,
        ]);
    }

    public function test_unauthenticated_users_cannot_access_email_templates(): void
    {
        $response = $this->getJson('/api/v1/email-templates');
        $response->assertStatus(401);
    }

    public function test_authenticated_users_can_view_email_templates_list(): void
    {
        Sanctum::actingAs($this->organisationMember);

        $response = $this->getJson('/api/v1/email-templates');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'code',
                        'subject',
                        'content',
                        'variables',
                        'is_active',
                        'description',
                        'created_at',
                        'updated_at',
                        'required_variables',
                        'is_valid',
                        'variable_count',
                    ]
                ],
                'meta' => [
                    'total',
                    'per_page',
                    'current_page',
                    'last_page',
                    'from',
                    'to',
                ],
                'success',
                'timestamp',
            ]);
    }

    public function test_authenticated_users_can_view_single_email_template(): void
    {
        Sanctum::actingAs($this->organisationMember);

        $response = $this->getJson("/api/v1/email-templates/{$this->emailTemplate->id}");

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'id' => $this->emailTemplate->id,
                    'name' => $this->emailTemplate->name,
                    'code' => $this->emailTemplate->code,
                    'subject' => $this->emailTemplate->subject,
                    'content' => $this->emailTemplate->content,
                    'variables' => $this->emailTemplate->variables,
                    'is_active' => $this->emailTemplate->is_active,
                ],
                'success' => true,
            ]);
    }

    public function test_email_templates_list_supports_search(): void
    {
        Sanctum::actingAs($this->organisationMember);

        // Create additional templates for search testing
        EmailTemplate::factory()->create([
            'name' => 'Welcome Email',
            'code' => 'welcome_email',
        ]);

        EmailTemplate::factory()->create([
            'name' => 'Password Reset Notification',
            'code' => 'password_reset_notification',
        ]);

        $response = $this->getJson('/api/v1/email-templates?search=Welcome');

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertGreaterThanOrEqual(1, count($data));

        // Check that at least one result contains "Welcome"
        $foundWelcome = false;
        foreach ($data as $template) {
            if (str_contains($template['name'], 'Welcome')) {
                $foundWelcome = true;
                break;
            }
        }
        $this->assertTrue($foundWelcome, 'Should find at least one template with "Welcome" in the name');
    }

    public function test_email_templates_list_supports_active_filter(): void
    {
        Sanctum::actingAs($this->organisationMember);

        // Create inactive template
        EmailTemplate::factory()->create([
            'name' => 'Inactive Template',
            'is_active' => false,
        ]);

        $response = $this->getJson('/api/v1/email-templates?is_active=true');

        $response->assertStatus(200);
        $data = $response->json('data');
        foreach ($data as $template) {
            $this->assertTrue($template['is_active']);
        }
    }

    public function test_only_system_admins_can_create_email_templates(): void
    {
        $templateData = [
            'name' => 'New Template',
            'code' => 'new_template',
            'subject' => 'New Subject',
            'content' => 'New content with {variable}',
            'variables' => ['variable'],
            'is_active' => true,
            'description' => 'Test description',
        ];

        // Test organization member cannot create
        Sanctum::actingAs($this->organisationMember);
        $response = $this->postJson('/api/v1/email-templates', $templateData);
        $response->assertStatus(403);

        // Test organization owner cannot create
        Sanctum::actingAs($this->organisationOwner);
        $response = $this->postJson('/api/v1/email-templates', $templateData);
        $response->assertStatus(403);

        // Test admin can create
        Sanctum::actingAs($this->adminUser);
        $response = $this->postJson('/api/v1/email-templates', $templateData);
        $response->assertStatus(201)
            ->assertJson([
                'data' => [
                    'name' => 'New Template',
                    'code' => 'new_template',
                ],
                'success' => true,
            ]);

        // Test root can create
        Sanctum::actingAs($this->rootUser);
        $templateData['code'] = 'another_template';
        $response = $this->postJson('/api/v1/email-templates', $templateData);
        $response->assertStatus(201);
    }

    public function test_only_system_admins_can_update_email_templates(): void
    {
        $updateData = [
            'name' => 'Updated Template',
            'code' => 'updated_template',
            'subject' => 'Updated Subject',
            'content' => 'Updated content',
            'variables' => [],
            'is_active' => false,
        ];

        // Test organization member cannot update
        Sanctum::actingAs($this->organisationMember);
        $response = $this->putJson("/api/v1/email-templates/{$this->emailTemplate->id}", $updateData);
        $response->assertStatus(403);

        // Test admin can update
        Sanctum::actingAs($this->adminUser);
        $response = $this->putJson("/api/v1/email-templates/{$this->emailTemplate->id}", $updateData);
        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'name' => 'Updated Template',
                    'code' => 'updated_template',
                ],
                'success' => true,
            ]);
    }

    public function test_only_system_admins_can_delete_email_templates(): void
    {
        // Test organization member cannot delete
        Sanctum::actingAs($this->organisationMember);
        $response = $this->deleteJson("/api/v1/email-templates/{$this->emailTemplate->id}");
        $response->assertStatus(403);

        // Test admin can delete
        Sanctum::actingAs($this->adminUser);
        $response = $this->deleteJson("/api/v1/email-templates/{$this->emailTemplate->id}");
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ]);

        $this->assertDatabaseMissing('email_templates', [
            'id' => $this->emailTemplate->id,
        ]);
    }

    public function test_authenticated_users_can_preview_email_templates(): void
    {
        Sanctum::actingAs($this->organisationMember);

        $previewData = [
            'variables' => [
                'user_name' => 'John Doe',
            ],
        ];

        $response = $this->postJson("/api/v1/email-templates/{$this->emailTemplate->id}/preview", $previewData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'template' => [
                        'id',
                        'name',
                        'code',
                    ],
                    'rendered' => [
                        'subject',
                        'content',
                    ],
                    'variables_used',
                    'required_variables',
                ],
                'success',
                'message',
            ]);

        $rendered = $response->json('data.rendered');
        $this->assertEquals('Test Subject with John Doe', $rendered['subject']);
        $this->assertEquals('Hello John Doe, this is a test email.', $rendered['content']);
    }

    public function test_preview_fails_with_missing_required_variables(): void
    {
        Sanctum::actingAs($this->organisationMember);

        $response = $this->postJson("/api/v1/email-templates/{$this->emailTemplate->id}/preview", [
            'variables' => [], // Missing required 'user_name' variable
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
            ]);
    }

    public function test_create_email_template_validates_required_fields(): void
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/v1/email-templates', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'code', 'subject', 'content']);
    }

    public function test_create_email_template_validates_code_uniqueness(): void
    {
        Sanctum::actingAs($this->adminUser);

        $templateData = [
            'name' => 'Duplicate Code Template',
            'code' => $this->emailTemplate->code, // Use existing code
            'subject' => 'Test Subject',
            'content' => 'Test content',
            'variables' => [],
        ];

        $response = $this->postJson('/api/v1/email-templates', $templateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['code']);
    }

    public function test_create_email_template_validates_code_format(): void
    {
        Sanctum::actingAs($this->adminUser);

        $templateData = [
            'name' => 'Invalid Code Template',
            'code' => 'Invalid-Code!', // Invalid characters
            'subject' => 'Test Subject',
            'content' => 'Test content',
            'variables' => [],
        ];

        $response = $this->postJson('/api/v1/email-templates', $templateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['code']);
    }

    public function test_create_email_template_validates_variable_consistency(): void
    {
        Sanctum::actingAs($this->adminUser);

        // Test undeclared variables
        $templateData = [
            'name' => 'Inconsistent Variables Template',
            'code' => 'inconsistent_template',
            'subject' => 'Hello {user_name}',
            'content' => 'Welcome {user_name}, your code is {verification_code}',
            'variables' => ['user_name'], // Missing 'verification_code'
        ];

        $response = $this->postJson('/api/v1/email-templates', $templateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['variables']);

        // Test unused variables
        $templateData = [
            'name' => 'Unused Variables Template',
            'code' => 'unused_template',
            'subject' => 'Hello {user_name}',
            'content' => 'Welcome {user_name}',
            'variables' => ['user_name', 'unused_variable'], // 'unused_variable' not used
        ];

        $response = $this->postJson('/api/v1/email-templates', $templateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['variables']);
    }

    public function test_update_email_template_allows_same_code_for_same_template(): void
    {
        Sanctum::actingAs($this->adminUser);

        $updateData = [
            'name' => 'Updated Template Name',
            'code' => $this->emailTemplate->code, // Same code should be allowed
            'subject' => 'Updated Subject',
            'content' => 'Updated content',
            'variables' => [],
        ];

        $response = $this->putJson("/api/v1/email-templates/{$this->emailTemplate->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'name' => 'Updated Template Name',
                    'code' => $this->emailTemplate->code,
                ],
                'success' => true,
            ]);
    }

    public function test_email_templates_list_supports_pagination(): void
    {
        Sanctum::actingAs($this->organisationMember);

        // Create additional templates (we already have 1 from setUp)
        EmailTemplate::factory()->count(25)->create();

        $response = $this->getJson('/api/v1/email-templates?per_page=5');

        $response->assertStatus(200);
        $meta = $response->json('meta');

        // Check that pagination structure exists
        $this->assertArrayHasKey('total', $meta);
        $this->assertArrayHasKey('per_page', $meta);
        $this->assertArrayHasKey('current_page', $meta);
        $this->assertArrayHasKey('last_page', $meta);

        // Check that data is limited by per_page
        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertLessThanOrEqual(5, count($data));
    }

    public function test_email_template_not_found_returns_404(): void
    {
        Sanctum::actingAs($this->organisationMember);

        $response = $this->getJson('/api/v1/email-templates/99999');

        $response->assertStatus(404);
    }

    public function test_preview_with_empty_variables_works_for_templates_without_variables(): void
    {
        Sanctum::actingAs($this->organisationMember);

        // Create template without variables
        $simpleTemplate = EmailTemplate::factory()->create([
            'subject' => 'Simple Subject',
            'content' => 'Simple content without variables',
            'variables' => [],
        ]);

        $response = $this->postJson("/api/v1/email-templates/{$simpleTemplate->id}/preview", [
            'variables' => [],
        ]);

        $response->assertStatus(200);
        $rendered = $response->json('data.rendered');
        $this->assertEquals('Simple Subject', $rendered['subject']);
        $this->assertEquals('Simple content without variables', $rendered['content']);
    }
}
