<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Activitylog\Models\Activity;
use Tests\TestCase;

/**
 * Activity Log Controller Test
 *
 * Tests the ActivityLogController API endpoints for system administrators.
 * Covers authorization via ActivityLogPolicy, filtering, pagination, and response formatting.
 */
final class ActivityLogControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $systemAdmin;
    private User $rootUser;
    private User $regularUser;
    private User $orgOwner;
    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test users with different roles
        $this->systemAdmin = User::factory()->create(['name' => 'System Admin']);
        $this->rootUser = User::factory()->create(['name' => 'Root User']);
        $this->regularUser = User::factory()->create(['name' => 'Regular User']);
        $this->orgOwner = User::factory()->create(['name' => 'Org Owner']);

        // Create system roles and assign roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $adminRole = collect($systemRoles)->firstWhere('name', 'admin');
        $rootRole = collect($systemRoles)->firstWhere('name', 'root');

        // Clear team context and assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemAdmin->guard_name = 'system';
        $this->systemAdmin->assignRole($adminRole);

        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($rootRole);

        // Create organisation and owner role
        $organisation = Organisation::factory()->create();
        $orgRoles = $this->permissionService->createDefaultRoles($organisation->id);
        $ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->orgOwner->organisations()->attach($organisation->id);

        // Set team context and assign organisation role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisation->id);
        $this->orgOwner->assignRole($ownerRole);

        // Clear system admin access cache
        $this->systemAdmin->clearSystemAdminAccessCache();
        $this->rootUser->clearSystemAdminAccessCache();
    }

    public function test_system_admin_can_access_activity_logs(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        // Create some test activity logs
        $this->actingAs($this->systemAdmin);
        $organisation = Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/v1/activity-logs');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'log_name',
                            'description',
                            'subject_type',
                            'subject_id',
                            'causer_type',
                            'causer_id',
                            'causer',
                            'properties',
                            'event',
                            'batch_uuid',
                            'created_at',
                        ]
                    ],
                    'meta' => [
                        'total',
                        'per_page',
                        'current_page',
                        'last_page',
                        'from',
                        'to',
                    ]
                ],
                'timestamp'
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Activity logs retrieved successfully',
            ]);

        // Verify activity log data structure
        $data = $response->json('data.data');
        if (!empty($data)) {
            $this->assertArrayHasKey('causer', $data[0]);
            $this->assertArrayHasKey('name', $data[0]['causer']);
            $this->assertArrayHasKey('email', $data[0]['causer']);
        }
    }

    public function test_root_user_can_access_activity_logs(): void
    {
        $this->actingAs($this->rootUser);

        $response = $this->getJson('/api/v1/activity-logs');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data',
                    'meta' => [
                        'total',
                        'per_page',
                        'current_page',
                        'last_page',
                        'from',
                        'to',
                    ]
                ],
                'timestamp'
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Activity logs retrieved successfully',
            ]);
    }

    public function test_index_requires_system_admin_access(): void
    {
        $this->actingAs($this->regularUser);

        $response = $this->getJson('/api/v1/activity-logs');

        $response->assertStatus(403);
    }

    public function test_organisation_owner_cannot_access_activity_logs(): void
    {
        $this->actingAs($this->orgOwner);

        $response = $this->getJson('/api/v1/activity-logs');

        $response->assertStatus(403);
    }

    public function test_unauthenticated_user_cannot_access_activity_logs(): void
    {
        $response = $this->getJson('/api/v1/activity-logs');

        $response->assertStatus(401);
    }

    public function test_activity_logs_pagination(): void
    {
        $this->actingAs($this->systemAdmin);

        // Test with custom per_page
        $response = $this->getJson('/api/v1/activity-logs?per_page=5&page=1');

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.per_page', 5)
            ->assertJsonPath('data.meta.current_page', 1);
    }

    public function test_activity_logs_filtering_by_subject_type(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $this->actingAs($this->systemAdmin);

        // Create test data
        Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        // Filter by Organisation
        $response = $this->getJson('/api/v1/activity-logs?subject_type=App\Models\Organisation');

        $response->assertStatus(200);

        $data = $response->json('data.data');
        foreach ($data as $activity) {
            $this->assertEquals('App\Models\Organisation', $activity['subject_type']);
        }
    }

    public function test_activity_logs_filtering_by_event(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $this->actingAs($this->systemAdmin);

        // Create test data
        $organisation = Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        // Update to create an 'updated' event
        $organisation->update(['name' => 'Updated Organisation']);

        // Filter by 'updated' event
        $response = $this->getJson('/api/v1/activity-logs?event=updated');

        $response->assertStatus(200);

        $data = $response->json('data.data');
        foreach ($data as $activity) {
            $this->assertEquals('updated', $activity['event']);
        }
    }

    public function test_activity_logs_filtering_by_date_range(): void
    {
        $this->actingAs($this->systemAdmin);

        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');

        $response = $this->getJson("/api/v1/activity-logs?date_from={$yesterday}&date_to={$today}");

        $response->assertStatus(200);
    }

    public function test_activity_logs_validation_errors(): void
    {
        $this->actingAs($this->systemAdmin);

        // Test invalid per_page
        $response = $this->getJson('/api/v1/activity-logs?per_page=150');
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['per_page']);

        // Test invalid subject_type
        $response = $this->getJson('/api/v1/activity-logs?subject_type=InvalidModel');
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['subject_type']);

        // Test invalid event
        $response = $this->getJson('/api/v1/activity-logs?event=invalid_event');
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['event']);

        // Test invalid date range
        $response = $this->getJson('/api/v1/activity-logs?date_from=2025-12-31&date_to=2025-01-01');
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['date_from']);
    }

    public function test_activity_logs_default_pagination(): void
    {
        $this->actingAs($this->systemAdmin);

        $response = $this->getJson('/api/v1/activity-logs');

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.per_page', 15)
            ->assertJsonPath('data.meta.current_page', 1);
    }

    public function test_activity_logs_max_per_page_limit(): void
    {
        $this->actingAs($this->systemAdmin);

        // Test that per_page is capped at 100
        $response = $this->getJson('/api/v1/activity-logs?per_page=200');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['per_page']);
    }
}
