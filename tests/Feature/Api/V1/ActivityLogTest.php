<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\ProductPermission;
use App\Models\Role;
use App\Models\SyncLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Activitylog\Models\Activity;
use Tests\TestCase;

class ActivityLogTest extends TestCase
{
    use RefreshDatabase;

    // Tests for default behavior (activity logging disabled by default)

    public function test_activity_logging_is_disabled_by_default(): void
    {
        // This test verifies that activity logging is disabled by default
        // No need to call $this->disableActivityLogging() - it should be disabled by default

        $user = User::factory()->create();
        $this->actingAs($user);

        $organisation = Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        // Assert that no activity log was created (default behavior)
        $this->assertDatabaseMissing('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'event' => 'created',
        ]);

        // Verify the organisation was created successfully
        $this->assertDatabaseHas('organisations', [
            'id' => $organisation->id,
            'name' => 'Test Organisation',
        ]);
    }

    public function test_multiple_model_operations_without_activity_logs(): void
    {
        // Test that multiple model operations don't generate activity logs by default
        // Clear any existing activity logs first
        Activity::truncate();

        $user1 = User::factory()->create(['name' => 'User 1']);
        $user2 = User::factory()->create(['name' => 'User 2']);

        $org1 = Organisation::create([
            'name' => 'Organisation 1',
            'code' => 'ORG1',
            'status' => 'active',
        ]);

        $org2 = Organisation::create([
            'name' => 'Organisation 2',
            'code' => 'ORG2',
            'status' => 'suspended',
        ]);

        // Update operations
        $org1->update(['name' => 'Updated Organisation 1']);
        $user1->update(['name' => 'Updated User 1']);

        // Assert that no activity logs were created for any of these operations
        $this->assertEquals(0, Activity::count(), 'No activity logs should be created when logging is disabled by default');

        // Verify all models were created/updated successfully
        $this->assertDatabaseHas('users', ['name' => 'Updated User 1']);
        $this->assertDatabaseHas('users', ['name' => 'User 2']);
        $this->assertDatabaseHas('organisations', ['name' => 'Updated Organisation 1']);
        $this->assertDatabaseHas('organisations', ['name' => 'Organisation 2']);
    }

    // Tests for activity logging control (enable/disable functionality)

    public function test_activity_logging_can_be_explicitly_disabled(): void
    {
        // Explicitly disable activity logging for this test
        $this->disableActivityLogging();

        $user = User::factory()->create();
        $this->actingAs($user);

        $organisation = Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        // Assert that no activity log was created
        $this->assertDatabaseMissing('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'event' => 'created',
        ]);

        // Verify the organisation was created
        $this->assertDatabaseHas('organisations', [
            'id' => $organisation->id,
            'name' => 'Test Organisation',
        ]);
    }

    public function test_activity_logging_can_be_enabled_in_tests(): void
    {
        // Enable activity logging for this test (now explicitly required)
        $this->enableActivityLogging();

        $user = User::factory()->create();
        $this->actingAs($user);

        $organisation = Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        // Assert that activity log was created
        $this->assertDatabaseHas('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'event' => 'created',
            'causer_type' => User::class,
            'causer_id' => $user->id,
        ]);
    }

    public function test_activity_logging_can_be_toggled_within_test(): void
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // First, disable activity logging
        $this->disableActivityLogging();

        $org1 = Organisation::create([
            'name' => 'Organisation 1',
            'code' => 'ORG1',
            'status' => 'active',
        ]);

        // No log should be created
        $this->assertDatabaseMissing('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $org1->id,
            'event' => 'created',
        ]);

        // Now enable activity logging
        $this->enableActivityLogging();

        $org2 = Organisation::create([
            'name' => 'Organisation 2',
            'code' => 'ORG2',
            'status' => 'active',
        ]);

        // Log should be created for the second organisation
        $this->assertDatabaseHas('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $org2->id,
            'event' => 'created',
            'causer_type' => User::class,
            'causer_id' => $user->id,
        ]);

        // But still no log for the first organisation
        $this->assertDatabaseMissing('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $org1->id,
            'event' => 'created',
        ]);
    }

    // Tests for specific model activity logging (when enabled)

    public function test_invitation_activity_logging(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $user = User::factory()->create();
        $organisation = Organisation::factory()->create();

        $this->actingAs($user);

        $invitation = Invitation::create([
            'model_type' => Organisation::class,
            'model_id' => $organisation->id,
            'role' => 'member',
            'created_by_user_id' => $user->id,
            'max_uses' => 10,
            'uses' => 0,
        ]);

        $this->assertDatabaseHas('activity_log', [
            'subject_type' => Invitation::class,
            'subject_id' => $invitation->id,
            'description' => __('activity.descriptions.invitation.created'),
            'event' => 'created',
            'causer_type' => User::class,
            'causer_id' => $user->id,
        ]);
    }

    public function test_organisation_activity_logging(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $user = User::factory()->create();
        $this->actingAs($user);

        $organisation = Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        $this->assertDatabaseHas('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'description' => __('activity.descriptions.organisation.created'),
            'event' => 'created',
            'causer_type' => User::class,
            'causer_id' => $user->id,
        ]);
    }

    public function test_user_activity_logging_excludes_sensitive_data(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $user = User::factory()->create();
        $this->actingAs($user);

        $newUser = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $activity = Activity::where('subject_type', User::class)
            ->where('subject_id', $newUser->id)
            ->where('event', 'created')
            ->first();

        $this->assertNotNull($activity);
        $this->assertEquals(__('activity.descriptions.user.created'), $activity->description);

        // Check that password is not logged
        $properties = $activity->properties->toArray();
        $this->assertArrayNotHasKey('password', $properties['attributes'] ?? []);
        $this->assertArrayHasKey('name', $properties['attributes'] ?? []);
        $this->assertArrayHasKey('email', $properties['attributes'] ?? []);
    }

    public function test_role_activity_logging(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $user = User::factory()->create();
        $organisation = Organisation::factory()->create();
        $this->actingAs($user);

        $role = Role::create([
            'name' => 'test-role',
            'guard_name' => 'api',
            'organisation_id' => $organisation->id,
        ]);

        $this->assertDatabaseHas('activity_log', [
            'subject_type' => Role::class,
            'subject_id' => $role->id,
            'description' => __('activity.descriptions.role.created'),
            'event' => 'created',
            'causer_type' => User::class,
            'causer_id' => $user->id,
        ]);
    }

    public function test_sync_log_activity_logging(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $user = User::factory()->create();
        $this->actingAs($user);

        $syncLog = SyncLog::create([
            'sync_type' => 'test',
            'batch_id' => 'test-batch-123',
            'status' => 'pending',
            'total_records' => 100,
            'processed_records' => 0,
            'success_records' => 0,
            'failed_records' => 0,
        ]);

        $this->assertDatabaseHas('activity_log', [
            'subject_type' => SyncLog::class,
            'subject_id' => $syncLog->id,
            'description' => __('activity.descriptions.sync_log.created'),
            'event' => 'created',
            'causer_type' => User::class,
            'causer_id' => $user->id,
        ]);
    }

    public function test_activity_log_only_logs_dirty_attributes(): void
    {
        // Enable activity logging for this test
        $this->enableActivityLogging();

        $user = User::factory()->create();
        $this->actingAs($user);

        $organisation = Organisation::create([
            'name' => 'Test Organisation',
            'code' => 'TEST_ORG',
            'status' => 'active',
        ]);

        // Clear existing activities
        Activity::truncate();

        // Update with same values (should not log)
        $organisation->update([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->assertDatabaseMissing('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'event' => 'updated',
        ]);

        // Update with different values (should log)
        $organisation->update([
            'name' => 'Updated Organisation',
        ]);

        $this->assertDatabaseHas('activity_log', [
            'subject_type' => Organisation::class,
            'subject_id' => $organisation->id,
            'description' => __('activity.descriptions.organisation.updated'),
            'event' => 'updated',
        ]);
    }
}
