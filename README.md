# jast_partner

## Installation

1. `make init ENV=development`
2. `make up ENV=development`
3. `make artisan cmd="migrate:fresh --seed" ENV=development`
4. `make artisan cmd="db:seed" ENV=development`
4. `make down ENV=development`

## Accelerated Build for China Mainland Users

If the build process is slow in mainland China, you can use a proxy to speed it up:

1. **Check proxy configuration:**
   ```bash
   ./scripts/check-proxy.sh
   ```

2. **Build with proxy:**
   ```bash
   # WSL2 environment (adjust IP according to the check script result)
   make build HTTP_PROXY=http://***********:7890 HTTPS_PROXY=http://***********:7890
   make init HTTP_PROXY=http://***********:7890 HTTPS_PROXY=http://***********:7890

   # Docker Desktop environment
   make build HTTP_PROXY=http://host.docker.internal:7890
   make init HTTP_PROXY=http://host.docker.internal:7890
   ```


## Installation without makefile

1. `php artisan migrate:fresh --seed --env=development`
2. `php artisan db:seed --env=development`

## Start web ui dev server
`cd webui && pnpm run dev`
