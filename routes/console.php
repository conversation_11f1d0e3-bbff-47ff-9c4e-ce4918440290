<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule sync commands with configuration-based control
Schedule::command('sync:products --incremental --scheduled --queue')
    ->hourly()
    ->withoutOverlapping()
    ->runInBackground()
    ->when(function () {
        return config('sync.scheduled_incremental_enabled', true);
    })
    ->emailOutputOnFailure(config('sync.notifications.email'))
    ->description('Hourly incremental product synchronization');

Schedule::command('sync:orders --incremental --scheduled --queue')
    ->hourly()
    ->withoutOverlapping()
    ->runInBackground()
    ->when(function () {
        return config('sync.scheduled_incremental_enabled', true);
    })
    ->emailOutputOnFailure(config('sync.notifications.email'))
    ->description('Hourly incremental order synchronization');

/*
Schedule::command('sync:products')
    ->dailyAt('02:00')
    ->withoutOverlapping()
    ->runInBackground()
    ->when(function () {
        return config('sync.scheduled_full_enabled', true);
    })
    ->emailOutputOnFailure(config('sync.notifications.email'))
    ->description('Daily full product synchronization');
*/
// Schedule cleanup command
Schedule::command('sync:cleanup --days=30')
    ->dailyAt('03:00')
    ->withoutOverlapping()
    ->when(function () {
        return config('sync.scheduled_cleanup_enabled', true);
    })
    ->description('Daily cleanup of old sync logs and records');

// Schedule activity log cleanup command
Schedule::command('activitylog:clean --days=' . config('activitylog.delete_records_older_than_days', 90))
    ->weekly()
    ->sundays()
    ->at('04:00')
    ->withoutOverlapping()
    ->when(function () {
        return config('activitylog.enabled', true) && env('ACTIVITY_LOG_CLEANUP_ENABLED', true);
    })
    ->emailOutputOnFailure(config('sync.notifications.email'))
    ->description('Weekly cleanup of old activity log records');
