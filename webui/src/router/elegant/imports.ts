/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  join: () => import("@/views/_builtin/join/index.vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  admin_horizon: () => import("@/views/admin/horizon/index.vue"),
  "admin_import-logs": () => import("@/views/admin/import-logs/index.vue"),
  admin_settings: () => import("@/views/admin/settings/index.vue"),
  "admin_system-logs": () => import("@/views/admin/system-logs/index.vue"),
  finance: () => import("@/views/finance/index.vue"),
  "game-reports": () => import("@/views/game-reports/index.vue"),
  game: () => import("@/views/game/index.vue"),
  home: () => import("@/views/home/<USER>"),
  organization: () => import("@/views/organization/index.vue"),
  profile: () => import("@/views/profile/index.vue"),
  sales: () => import("@/views/sales/index.vue"),
  user: () => import("@/views/user/index.vue"),
};
