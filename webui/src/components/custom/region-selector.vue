<script setup lang="ts">
import { computed, h } from 'vue';
import { NSelect } from 'naive-ui';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'RegionSelector'
});

interface Props {
  value?: string | string[] | null; // Support both single and multiple selection
  multiple?: boolean; // Whether to enable multiple selection
  clearable?: boolean; // Whether to enable clear function
  placeholder?: string; // Placeholder text
  size?: 'small' | 'medium' | 'large'; // Component size
  disabled?: boolean; // Whether disabled
  filterable?: boolean; // Whether to enable search/filter
}

interface Emits {
  (e: 'update:value', value: string | string[] | null): void;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  clearable: true,
  size: 'medium',
  disabled: false,
  filterable: true
});

const emit = defineEmits<Emits>();

// Region data with flag icons
const countryData = [
  { code: 'US', name: 'United States', icon: 'circle-flags:us' },
  { code: 'CA', name: 'Canada', icon: 'circle-flags:ca' },
  { code: 'GB', name: 'United Kingdom', icon: 'circle-flags:gb' },
  { code: 'DE', name: 'Germany', icon: 'circle-flags:de' },
  { code: 'FR', name: 'France', icon: 'circle-flags:fr' },
  { code: 'IT', name: 'Italy', icon: 'circle-flags:it' },
  { code: 'ES', name: 'Spain', icon: 'circle-flags:es' },
  { code: 'NL', name: 'Netherlands', icon: 'circle-flags:nl' },
  { code: 'BE', name: 'Belgium', icon: 'circle-flags:be' },
  { code: 'CH', name: 'Switzerland', icon: 'circle-flags:ch' },
  { code: 'AT', name: 'Austria', icon: 'circle-flags:at' },
  { code: 'SE', name: 'Sweden', icon: 'circle-flags:se' },
  { code: 'NO', name: 'Norway', icon: 'circle-flags:no' },
  { code: 'DK', name: 'Denmark', icon: 'circle-flags:dk' },
  { code: 'FI', name: 'Finland', icon: 'circle-flags:fi' },
  { code: 'PL', name: 'Poland', icon: 'circle-flags:pl' },
  { code: 'CZ', name: 'Czech Republic', icon: 'circle-flags:cz' },
  { code: 'HU', name: 'Hungary', icon: 'circle-flags:hu' },
  { code: 'RO', name: 'Romania', icon: 'circle-flags:ro' },
  { code: 'BG', name: 'Bulgaria', icon: 'circle-flags:bg' },
  { code: 'HR', name: 'Croatia', icon: 'circle-flags:hr' },
  { code: 'SI', name: 'Slovenia', icon: 'circle-flags:si' },
  { code: 'SK', name: 'Slovakia', icon: 'circle-flags:sk' },
  { code: 'LT', name: 'Lithuania', icon: 'circle-flags:lt' },
  { code: 'LV', name: 'Latvia', icon: 'circle-flags:lv' },
  { code: 'EE', name: 'Estonia', icon: 'circle-flags:ee' },
  { code: 'IE', name: 'Ireland', icon: 'circle-flags:ie' },
  { code: 'PT', name: 'Portugal', icon: 'circle-flags:pt' },
  { code: 'GR', name: 'Greece', icon: 'circle-flags:gr' },
  { code: 'CY', name: 'Cyprus', icon: 'circle-flags:cy' },
  { code: 'MT', name: 'Malta', icon: 'circle-flags:mt' },
  { code: 'LU', name: 'Luxembourg', icon: 'circle-flags:lu' },
  { code: 'JP', name: 'Japan', icon: 'circle-flags:jp' },
  { code: 'KR', name: 'South Korea', icon: 'circle-flags:kr' },
  { code: 'CN', name: 'China', icon: 'circle-flags:cn' },
  { code: 'HK', name: 'Hong Kong', icon: 'circle-flags:hk' },
  { code: 'TW', name: 'Taiwan', icon: 'circle-flags:cn' },
  { code: 'SG', name: 'Singapore', icon: 'circle-flags:sg' },
  { code: 'MY', name: 'Malaysia', icon: 'circle-flags:my' },
  { code: 'TH', name: 'Thailand', icon: 'circle-flags:th' },
  { code: 'ID', name: 'Indonesia', icon: 'circle-flags:id' },
  { code: 'PH', name: 'Philippines', icon: 'circle-flags:ph' },
  { code: 'VN', name: 'Vietnam', icon: 'circle-flags:vn' },
  { code: 'IN', name: 'India', icon: 'circle-flags:in' },
  { code: 'AU', name: 'Australia', icon: 'circle-flags:au' },
  { code: 'NZ', name: 'New Zealand', icon: 'circle-flags:nz' },
  { code: 'BR', name: 'Brazil', icon: 'circle-flags:br' },
  { code: 'AR', name: 'Argentina', icon: 'circle-flags:ar' },
  { code: 'MX', name: 'Mexico', icon: 'circle-flags:mx' },
  { code: 'CL', name: 'Chile', icon: 'circle-flags:cl' },
  { code: 'CO', name: 'Colombia', icon: 'circle-flags:co' },
  { code: 'PE', name: 'Peru', icon: 'circle-flags:pe' },
  { code: 'UY', name: 'Uruguay', icon: 'circle-flags:uy' },
  { code: 'ZA', name: 'South Africa', icon: 'circle-flags:za' },
  { code: 'EG', name: 'Egypt', icon: 'circle-flags:eg' },
  { code: 'IL', name: 'Israel', icon: 'circle-flags:il' },
  { code: 'AE', name: 'United Arab Emirates', icon: 'circle-flags:ae' },
  { code: 'SA', name: 'Saudi Arabia', icon: 'circle-flags:sa' },
  { code: 'TR', name: 'Turkey', icon: 'circle-flags:tr' },
  { code: 'RU', name: 'Russia', icon: 'circle-flags:ru' },
  { code: 'UA', name: 'Ukraine', icon: 'circle-flags:ua' }
];

// Generate options for NSelect
const countryOptions = computed(() =>
  countryData.map(country => ({
    label: `${country.name} (${country.code})`,
    value: country.code,
    icon: country.icon
  }))
);

// Computed placeholder
const computedPlaceholder = computed(() => {
  if (props.placeholder) return props.placeholder;
  return props.multiple
    ? $t('page.game.statistics.selectCountries')
    : $t('page.game.statistics.selectCountry');
});

// Handle value change
function handleUpdateValue(value: string | string[] | null) {
  emit('update:value', value);
}

// Custom render label with icon
function renderLabel(option: any) {
  return h('div', { class: 'flex items-center gap-2' }, [
    h(SvgIcon, {
      icon: option.icon,
      class: 'w-4 h-4 flex-shrink-0'
    }),
    h('span', option.label)
  ]);
}

// Custom render tag (for multiple selection)
function renderTag({ option, handleClose }: any) {
  return h('div', {
    class: 'flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded text-xs'
  }, [
    h(SvgIcon, {
      icon: option.icon,
      class: 'w-3 h-3 flex-shrink-0'
    }),
    h('span', option.code),
    h('button', {
      class: 'ml-1 hover:bg-primary/20 rounded-full w-4 h-4 flex items-center justify-center',
      onClick: handleClose
    }, '×')
  ]);
}
</script>

<template>
  <NSelect
    :value="value"
    :options="countryOptions"
    :multiple="multiple"
    :clearable="clearable"
    :placeholder="computedPlaceholder"
    :size="size"
    :disabled="disabled"
    :filterable="filterable"
    :render-label="renderLabel"
    :render-tag="multiple ? renderTag : undefined"
    @update:value="handleUpdateValue"
  />
</template>

<style scoped>
/* Custom styles for country selector */
</style>
