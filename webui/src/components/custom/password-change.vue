<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import type { FormInst, FormRules } from 'naive-ui';
import { $t } from '@/locales';
import { fetchChangePassword } from '@/service/api';

interface Emits {
  (e: 'success'): void;
  (e: 'error', error: string): void;
}

const emit = defineEmits<Emits>();

// Form reference
const formRef = ref<FormInst | null>(null);

// Form data
const formData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmNewPassword: ''
});

// Loading state
const loading = ref(false);

// Form validation rules
const rules: FormRules = {
  currentPassword: [
    {
      required: true,
      message: $t('page.user.profile.currentPasswordRequired'),
      trigger: 'blur'
    }
  ],
  newPassword: [
    {
      required: true,
      message: $t('page.user.profile.newPasswordRequired'),
      trigger: 'blur'
    },
    {
      min: 6,
      message: $t('form.pwd.invalid'),
      trigger: 'blur'
    }
  ],
  confirmNewPassword: [
    {
      required: true,
      message: $t('page.user.profile.confirmNewPasswordRequired'),
      trigger: 'blur'
    },
    {
      validator: (rule: any, value: string) => {
        if (value !== formData.newPassword) {
          return new Error($t('page.user.profile.passwordMismatch'));
        }
        return true;
      },
      trigger: ['blur', 'input']
    }
  ]
};

// Handle form submission
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    // Validate form
    await formRef.value.validate();

    loading.value = true;

    // Call API
    const { data, error } = await fetchChangePassword({
      current_password: formData.currentPassword,
      password: formData.newPassword,
      password_confirmation: formData.confirmNewPassword
    });

    if (error) {
      throw new Error(error.message || $t('page.user.profile.changePasswordFailed'));
    }

    // Success
    window.$message?.success($t('page.user.profile.changePasswordSuccess'));
    emit('success');

    // Reset form
    resetForm();

  } catch (error: any) {
    console.error('Password change failed:', error);
    const errorMessage = error.message || $t('page.user.profile.changePasswordFailed');

    // Handle specific error cases
    if (errorMessage.includes('current password') || errorMessage.includes('当前密码')) {
      window.$message?.error($t('page.user.profile.currentPasswordIncorrect'));
    } else {
      window.$message?.error(errorMessage);
    }

    emit('error', errorMessage);
  } finally {
    loading.value = false;
  }
};

// Reset form
const resetForm = () => {
  formData.currentPassword = '';
  formData.newPassword = '';
  formData.confirmNewPassword = '';
  formRef.value?.restoreValidation();
};

// Handle form reset
const handleReset = () => {
  resetForm();
};

// Password strength indicator
const getPasswordStrength = (password: string): { level: number; text: string; color: string } => {
  if (!password) return { level: 0, text: '', color: '' };

  let score = 0;

  // Length check
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;

  // Character variety checks
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;

  if (score <= 2) {
    return { level: 1, text: $t('common.weak'), color: '#f56565' };
  } else if (score <= 4) {
    return { level: 2, text: $t('common.medium'), color: '#ed8936' };
  } else {
    return { level: 3, text: $t('common.strong'), color: '#38a169' };
  }
};

// Computed password strength
const passwordStrength = computed(() => getPasswordStrength(formData.newPassword));
</script>

<template>
  <div class="password-change">
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="140px"
      require-mark-placement="right-hanging"
    >
      <!-- Current Password -->
      <NFormItem :label="$t('page.user.profile.currentPassword')" path="currentPassword">
        <NInput
          v-model:value="formData.currentPassword"
          type="password"
          show-password-on="click"
          :placeholder="$t('page.user.profile.currentPasswordPlaceholder')"
          :disabled="loading"
          clearable
        />
      </NFormItem>

      <!-- New Password -->
      <NFormItem :label="$t('page.user.profile.newPassword')" path="newPassword">
        <NInput
          v-model:value="formData.newPassword"
          type="password"
          show-password-on="click"
          :placeholder="$t('page.user.profile.newPasswordPlaceholder')"
          :disabled="loading"
          clearable
        />

        <!-- Password strength indicator -->
        <div v-if="formData.newPassword" class="password-strength">
          <div class="strength-bar">
            <div
              class="strength-fill"
              :style="{
                width: `${(passwordStrength.level / 3) * 100}%`,
                backgroundColor: passwordStrength.color
              }"
            />
          </div>
          <span
            class="strength-text"
            :style="{ color: passwordStrength.color }"
          >
            {{ passwordStrength.text }}
          </span>
        </div>
      </NFormItem>

      <!-- Confirm New Password -->
      <NFormItem :label="$t('page.user.profile.confirmNewPassword')" path="confirmNewPassword">
        <NInput
          v-model:value="formData.confirmNewPassword"
          type="password"
          show-password-on="click"
          :placeholder="$t('page.user.profile.confirmNewPasswordPlaceholder')"
          :disabled="loading"
          clearable
        />
      </NFormItem>

      <!-- Action buttons -->
      <NFormItem>
        <NSpace>
          <NButton
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            {{ $t('page.user.profile.changePassword') }}
          </NButton>

          <NButton
            :disabled="loading"
            @click="handleReset"
          >
            {{ $t('common.reset') }}
          </NButton>
        </NSpace>
      </NFormItem>
    </NForm>

    <!-- Password requirements -->
    <div class="password-requirements">
      <h4 class="requirements-title">{{ $t('common.passwordRequirements') }}:</h4>
      <ul class="requirements-list">
        <li>{{ $t('common.passwordMinLength') }}</li>
        <li>{{ $t('common.passwordComplexity') }}</li>
        <li>{{ $t('common.passwordRecommendation') }}</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.password-change {
  max-width: 500px;
}

.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
}

.password-requirements {
  margin-top: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.requirements-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.requirements-list {
  margin: 0;
  padding-left: 16px;
  font-size: 13px;
  color: #666;
}

.requirements-list li {
  margin-bottom: 4px;
}

.requirements-list li:last-child {
  margin-bottom: 0;
}
</style>
