<script setup lang="ts">
import { ref, computed } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { $t } from '@/locales';
import { fetchUploadAvatar } from '@/service/api';
import SvgIcon from '@/components/custom/svg-icon.vue';

interface Props {
  /** Current avatar URL */
  avatarUrl?: string;
  /** Avatar size */
  size?: number;
  /** Whether upload is disabled */
  disabled?: boolean;
}

interface Emits {
  (e: 'success', url: string): void;
  (e: 'error', error: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  avatarUrl: '',
  size: 120,
  disabled: false
});

const emit = defineEmits<Emits>();

// State
const uploading = ref(false);
const previewUrl = ref(props.avatarUrl);

// Computed
const currentAvatarUrl = computed(() => previewUrl.value || props.avatarUrl);

// File validation
const validateFile = (file: File): boolean => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  if (!allowedTypes.includes(file.type)) {
    window.$message?.error('头像格式不正确，请上传 JPG、PNG、GIF 格式的图片');
    return false;
  }

  // Check file size (2MB)
  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    window.$message?.error('头像文件大小不能超过 2MB');
    return false;
  }

  return true;
};

// Handle file selection
const handleFileSelect = async (options: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = options;

  if (!file.file) return;

  // Validate file
  if (!validateFile(file.file)) {
    return;
  }

  // Create preview URL
  const reader = new FileReader();
  reader.onload = (e) => {
    previewUrl.value = e.target?.result as string;
  };
  reader.readAsDataURL(file.file);

  // Upload file
  await uploadFile(file.file);
};

// Upload file to server
const uploadFile = async (file: File) => {
  if (props.disabled) return;

  uploading.value = true;

  try {
    const { data, error } = await fetchUploadAvatar(file);

    if (error) {
      throw new Error(error.message || $t('page.user.profile.uploadFailed'));
    }

    if (data?.data?.avatar_url) {
      previewUrl.value = data.data.avatar_url;
      emit('success', data.data.avatar_url);
      window.$message?.success($t('page.user.profile.uploadSuccess'));
    } else {
      throw new Error($t('page.user.profile.uploadFailed'));
    }
  } catch (error: any) {
    console.error('Avatar upload failed:', error);
    emit('error', error.message || $t('page.user.profile.uploadFailed'));
    window.$message?.error(error.message || $t('page.user.profile.uploadFailed'));

    // Reset preview on error
    previewUrl.value = props.avatarUrl;
  } finally {
    uploading.value = false;
  }
};

// Handle remove avatar
const handleRemoveAvatar = () => {
  previewUrl.value = '';
  emit('success', '');
  window.$message?.success('头像删除成功');
};

// Custom request to prevent default upload behavior
const customRequest = () => {
  // Do nothing - we handle upload in handleFileSelect
  return { abort: () => {} };
};
</script>

<template>
  <div class="avatar-upload">
    <div class="avatar-preview">
      <NAvatar
        :size="size"
        :src="currentAvatarUrl"
        fallback-src="/default-avatar.png"
        class="avatar-image"
        round
      />

      <!-- Upload overlay -->
      <div v-if="!disabled" class="upload-overlay">
        <NUpload
          :show-file-list="false"
          accept="image/jpeg,image/jpg,image/png,image/gif"
          :max="1"
          :custom-request="customRequest"
          @change="handleFileSelect"
        >
          <div class="upload-trigger">
            <SvgIcon
              :icon="uploading ? 'ph:spinner' : 'ph:camera'"
              :class="['upload-icon', { 'animate-spin': uploading }]"
            />
            <span class="upload-text">
              {{ uploading ? $t('common.uploading') : $t('page.user.profile.changeAvatar') }}
            </span>
          </div>
        </NUpload>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="avatar-actions">
      <NUpload
        :show-file-list="false"
        accept="image/jpeg,image/jpg,image/png,image/gif"
        :max="1"
        :disabled="disabled || uploading"
        :custom-request="customRequest"
        @change="handleFileSelect"
      >
        <NButton
          :loading="uploading"
          :disabled="disabled"
          secondary
          size="small"
        >
          <template #icon>
            <SvgIcon icon="ph:upload" />
          </template>
          {{ $t('page.user.profile.uploadAvatar') }}
        </NButton>
      </NUpload>

      <NButton
        v-if="currentAvatarUrl"
        :disabled="disabled || uploading"
        secondary
        size="small"
        @click="handleRemoveAvatar"
      >
        <template #icon>
          <SvgIcon icon="ph:trash" />
        </template>
        {{ $t('page.user.removeAvatar') }}
      </NButton>
    </div>

    <!-- Upload hint -->
    <p class="upload-hint">
      {{ $t('page.user.profile.avatarHint') }}
    </p>
  </div>
</template>

<style scoped>
.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.avatar-preview {
  position: relative;
  display: inline-block;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-image {
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  box-sizing: border-box;
}

.avatar-preview:hover .upload-overlay {
  opacity: 1;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: white;
  text-align: center;
}

.upload-icon {
  font-size: 24px;
}

.upload-text {
  font-size: 12px;
  white-space: nowrap;
}

.avatar-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.upload-hint {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin: 0;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
