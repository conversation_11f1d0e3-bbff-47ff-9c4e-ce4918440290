<script setup lang="tsx">
import SvgIcon from '@/components/custom/svg-icon.vue';
import CountTo from '@/components/custom/count-to.vue';

defineOptions({
  name: 'StatsCard'
});

interface Props {
  /** Title */
  title: string;
  /** Value */
  value: number;
  /** Unit */
  unit: string;
  /** Gradient color configuration */
  color: {
    start: string;
    end: string;
  };
  /** Icon name */
  icon: string;
  /** Whether to display the unit as a prefix (e.g., currency symbol) */
  unitAsPrefix?: boolean;
  /** Animation duration */
  duration?: number;
  /** Whether to use easing animation */
  useEasing?: boolean;
  /** Animation transition effect */
  transition?: string;
}

const props = withDefaults(defineProps<Props>(), {
  unitAsPrefix: false,
  duration: 2000,
  useEasing: true,
  transition: 'easeOutExpo'
});
</script>

<template>
  <div
    class="relative overflow-hidden rounded-lg p-6 text-white"
    :style="{
      background: `linear-gradient(135deg, ${color.start} 0%, ${color.end} 100%)`
    }"
  >
    <div class="flex items-center justify-between">
      <div>
        <div class="mb-1 text-sm opacity-90">{{ title }}</div>
        <div class="text-2xl font-bold">
          <CountTo
            :prefix="unitAsPrefix ? unit : ''"
            :suffix="unitAsPrefix ? '' : ` ${unit}`"
            :end-value="value"
            :duration="duration"
            :use-easing="useEasing"
            :transition="transition"
            class="text-white"
          />
        </div>
      </div>
      <div class="text-3xl opacity-80">
        <SvgIcon :icon="icon" />
      </div>
    </div>

    <!-- Decorative background pattern -->
    <div class="absolute text-6xl opacity-20 -right-4 -top-4">
      <SvgIcon :icon="icon" />
    </div>
  </div>
</template>


