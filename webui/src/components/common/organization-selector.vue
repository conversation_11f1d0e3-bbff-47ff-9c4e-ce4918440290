<script setup lang="ts">
import { computed, onMounted, ref, watch, nextTick } from 'vue';
import type { SelectOption } from 'naive-ui';
import { NSelect, NPagination, NEmpty, NSpin, useMessage } from 'naive-ui';
import { fetchOrganizations } from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { usePermission } from '@/hooks/common/permission';
import { $t } from '@/locales';
import { useDebounceFn } from '@vueuse/core';

interface Props {
  modelValue?: number | number[] | null;
  multiple?: boolean;
  placeholder?: string;
  clearable?: boolean;
  filterable?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  showAllOption?: boolean; // Whether to show "All" option
  allOptionLabel?: string; // Label for "All" option
  allOptionValue?: any; // Value for "All" option
  organizationIds?: number[]; // Limit search to specific organization IDs
}

interface Emits {
  (e: 'update:modelValue', value: number | number[] | null): void;
  (e: 'change', value: number | number[] | null): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  multiple: false,
  placeholder: '',
  clearable: true,
  filterable: true,
  disabled: false,
  size: 'medium',
  loading: false,
  showAllOption: false,
  allOptionLabel: '',
  allOptionValue: null,
  organizationIds: () => []
});

const emit = defineEmits<Emits>();

const { isSystemAdmin, userOrganizations } = usePermission();
const message = useMessage();

const isLoading = ref(false);
const organizations = ref<Api.Organization.Organization[]>([]);
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);
const showDropdown = ref(false);

// Compute placeholder text
const computedPlaceholder = computed(() => {
  if (props.placeholder) {
    return props.placeholder;
  }
  return props.multiple
    ? $t('common.selectMultipleOrganizations' as any)
    : $t('common.selectOrganization' as any);
});

// Compute "All" option label
const computedAllOptionLabel = computed(() => {
  return props.allOptionLabel || $t('common.allOrganizations' as any);
});

// Compute available organization options
const organizationOptions = computed<SelectOption[]>(() => {
  const options: SelectOption[] = organizations.value.map((org: Api.Organization.Organization) => ({
    label: org.name,
    value: org.id,
    // admin/root users can select organizations in any status, others can only select active ones
    disabled: !isSystemAdmin() && org.status !== 'active'
  }));

  // If "All" option is needed, add it to the beginning
  if (props.showAllOption && currentPage.value === 1 && !searchKeyword.value) {
    options.unshift({
      label: computedAllOptionLabel.value,
      value: props.allOptionValue,
      disabled: false
    });
  }

  return options;
});

// Load organization list
async function loadOrganizations(resetPage = false) {
  try {
    isLoading.value = true;

    if (resetPage) {
      currentPage.value = 1;
    }

    const params: any = {
      per_page: pageSize.value,
      page: currentPage.value
    };

    if (searchKeyword.value.trim()) {
      params.search = searchKeyword.value.trim();
    }

    // Add organization IDs filter if provided
    if (props.organizationIds && props.organizationIds.length > 0) {
      params.organization_ids = props.organizationIds.join(',');
    }

    const { data } = await fetchOrganizations(params);
    if (data?.success) {
      organizations.value = data.data?.data || [];
      totalCount.value = data.data?.meta?.total || 0;

      // Auto-select first organization for system admin if no value is selected
      if (isSystemAdmin() && !props.modelValue && organizations.value.length > 0 && currentPage.value === 1) {
        const firstOrg = organizations.value[0];
        console.log('OrganizationSelector: Auto-selecting first organization for system admin:', firstOrg.id, firstOrg.name);
        emit('update:modelValue', firstOrg.id);
        emit('change', firstOrg.id);
      }
    } else {
      handleApiError({ response: { data } }, message, $t('page.organization.loadFailed' as any));
    }
  } catch (error) {
    console.error('Load organizations error:', error);
    handleApiError(error, message, $t('page.organization.loadFailed' as any));
  } finally {
    isLoading.value = false;
  }
}

// Debounced search function
const debouncedSearch = useDebounceFn(() => {
  loadOrganizations(true);
}, 500);

// Handle search from NSelect
function handleSearch(query: string) {
  searchKeyword.value = query;
  debouncedSearch();
}

// Handle focus event
function handleFocus() {
  if (organizations.value.length === 0) {
    loadOrganizations();
  }
}

// Handle page change
function handlePageChange(page: number) {
  currentPage.value = page;
  loadOrganizations();
}

// Handle value change
function handleValueChange(value: number | number[] | null) {
  emit('update:modelValue', value);
  emit('change', value);

  // Clear search keyword when selection is cleared
  if (value === null || (Array.isArray(value) && value.length === 0)) {
    searchKeyword.value = '';
    loadOrganizations(true);
  }
}

// Load organization list when component is mounted
onMounted(() => {
  loadOrganizations();
});

// Watch for search keyword changes
watch(searchKeyword, (newValue) => {
  if (!newValue.trim()) {
    loadOrganizations(true);
  }
});

// Watch for organizationIds changes
watch(() => props.organizationIds, () => {
  loadOrganizations(true);
}, { deep: true });

// Expose refresh method
defineExpose({
  refresh: loadOrganizations
});
</script>

<template>
  <div class="organization-selector">
    <NSelect
      :value="modelValue"
      :options="organizationOptions"
      :placeholder="computedPlaceholder"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="true"
      :remote="true"
      :disabled="disabled"
      :size="size"
      :loading="isLoading"
      :filter="() => true"
      @search="handleSearch"
      @update:value="handleValueChange"
      @focus="handleFocus"
    >
      <template #empty>
        <div class="p-4 text-center">
          <NEmpty v-if="!isLoading && searchKeyword" :description="$t('common.noSearchResults')" />
          <NEmpty v-else-if="!isLoading" :description="$t('common.noOrganizations')" />
          <NSpin v-else />
        </div>
      </template>
      <template #action>
        <div class=" border-gray-200 dark:border-gray-700">
          <NPagination
            :page="currentPage"
            :page-size="pageSize"
            :item-count="totalCount"
            size="small"
            simple
            @update:page="handlePageChange"
          />
        </div>
      </template>
    </NSelect>
  </div>
</template>

<style scoped>
.organization-selector {
  width: 100%;
}


</style>
