/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  /** Generic API response structure */
  interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    errors?: any;
    timestamp: string;
  }

  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    /** Login request */
    interface LoginRequest {
      email: string;
      password: string;
    }

    /** Login response data */
    interface LoginData {
      user: {
        id: number;
        name: string;
        email: string;
        email_verified_at: string | null;
      };
      token: string;
      token_type: string;
    }

    /** Login token (for compatibility) */
    interface LoginToken {
      token: string;
      refreshToken?: string; // Optional for backward compatibility
    }

    /** Organization info */
    interface Organisation {
      id: number;
      name: string;
      code: string;
      status: string;
    }

    /** Role info */
    interface Role {
      id: number;
      name: string;
      guard_name: string;
      organisation_id: number | null;
      organisation_name?: string;
    }

    /** User roles structure */
    interface UserRoles {
      system_roles: Role[];
      organisation_roles: Role[];
      all_role_names: string[];
      permissions?: Permission[];
    }

    /** Permission info for visitor access */
    interface Permission {
      name: string;
      product_id: number;
      permission_type: string;
      notes: string;
    }

    /** Complete user info from API */
    interface ApiUserInfo {
      id: number;
      name: string;
      email: string;
      email_verified_at: string | null;
      organisations: Organisation[];
      roles: UserRoles;
      created_at: string;
      updated_at: string;
    }

    /** User info for frontend (compatible with existing code) */
    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      buttons: string[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace Organization
   *
   * backend api module: "organization"
   */
  namespace Organization {
    /** Organization status */
    type OrganizationStatus = 'pending' | 'active' | 'suspended';

    /** Organization basic info */
    interface Organization {
      id: number;
      name: string;
      code: string;
      details?: Record<string, any>;
      remarks?: string;
      status: OrganizationStatus;
      is_active: boolean;
      is_pending: boolean;
      is_suspended: boolean;
      users_count?: number;
      created_at: string;
      updated_at: string;
    }

    /** Pagination meta info */
    interface PaginationMeta {
      total: number;
      per_page: number;
      current_page: number;
      last_page: number;
      from: number;
      to: number;
    }

    /** Organization list response */
    interface OrganizationListResponse {
      data: Organization[];
      meta: PaginationMeta;
    }

    /** Create organization request */
    interface CreateOrganizationRequest {
      name: string;
      code: string;
      details?: Record<string, any>;
      remarks?: string;
      status?: OrganizationStatus;
    }

    /** Update organization request */
    interface UpdateOrganizationRequest {
      name?: string;
      code?: string;
      details?: Record<string, any>;
      remarks?: string;
      status?: OrganizationStatus;
    }

    /** User in organization context */
    interface OrganizationUser {
      id: number;
      name: string;
      email: string;
      email_verified_at: string | null;
      organisations: Auth.Organisation[];
      created_at: string;
      updated_at: string;
    }

    /** Organization users response */
    interface OrganizationUsersResponse {
      data: OrganizationUser[];
      meta: PaginationMeta;
    }

    /** User list response */
    interface UserListResponse {
      data: OrganizationUser[];
      meta: PaginationMeta;
    }

    /** Create user request */
    interface CreateUserRequest {
      name: string;
      email: string;
      password: string;
      organisation_ids?: number[];
    }

    /** Update user request */
    interface UpdateUserRequest {
      name?: string;
      email?: string;
      password?: string;
      organisation_ids?: number[];
    }

    /** Invitation info */
    interface Invitation {
      id: string;
      model_type: string;
      model_id: number;
      role: string;
      created_by_user_id: number;
      expires_at: string | null;
      max_uses: number;
      uses: number;
      email_restriction: string | null;
      created_at: string;
      updated_at: string;
      model: Organization;
      created_by: {
        id: number;
        name: string;
        email: string;
      };
    }

    /** Invitation list response */
    interface InvitationListResponse {
      data: Invitation[];
      meta: PaginationMeta;
    }

    /** Create invitation request */
    interface CreateInvitationRequest {
      model_type: string;
      model_id: number;
      role: string;
      expires_at?: string;
      max_uses?: number;
      email_restriction?: string;
    }

    /** Accept invitation response */
    interface AcceptInvitationResponse {
      user: {
        id: number;
        name: string;
        email: string;
        email_verified_at: string;
      };
      organisation: Organization;
      role: string;
    }

    /** Invitation info response (for join page) */
    interface InvitationInfo {
      id: string;
      model_type: string;
      model_id: number;
      role: string;
      expires_at: string | null;
      max_uses: number;
      uses: number;
      email_restriction: string | null;
      is_expired: boolean;
      is_valid: boolean;
      has_reached_usage_limit: boolean;
      created_by: {
        id: number;
        name: string;
        email: string;
      };
      model_name: string;
      created_at: string;
      updated_at: string;
    }

    /** Send verification code response */
    interface SendVerificationCodeResponse {
      message: string;
      expires_in_seconds: number;
    }

    /** User status type */
    type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';

    /** User search parameters */
    interface UserSearchParams {
      per_page?: number;
      organisation_id?: number;
      organisation_ids?: string; // Comma-separated organisation IDs
      status?: UserStatus;
      search?: string; // Combined search for name or email
      name?: string;
      email?: string;
    }

    /** User statistics */
    interface UserStats {
      total: number;
      active: number;
      pending: number;
      suspended: number;
      new_this_month: number;
    }

    /** Role assignment request */
    interface AssignRoleRequest {
      role_name: string;
      organisation_id?: number;
    }

    /** Role assignment response */
    interface AssignRoleResponse {
      user_id: number;
      role_name: string;
      organisation_id?: number;
    }

    /** Remove role response */
    interface RemoveRoleResponse {
      user_id: number;
      role_id: number;
      role_name: string;
    }

    /** User roles detail response */
    interface UserRolesResponse {
      user_id: number;
      user_name: string;
      user_email: string;
      organisations: Auth.Organisation[];
      roles: Auth.UserRoles;
    }

    /** Transfer owner response */
    interface TransferOwnerResponse {
      new_owner: {
        id: number;
        name: string;
        email: string;
      };
      organisation: {
        id: number;
        name: string;
        code: string;
      };
      previous_owner: {
        id: number;
        name: string;
        email: string;
      };
    }

    /** Sync organizations request */
    interface SyncOrganizationsRequest {
      organisation_ids: number[];
    }

    /** Send verification code response */
    interface SendVerificationCodeResponse {
      message: string;
      expires_in_seconds: number;
    }

    /** Guest registration request */
    interface GuestRegistrationRequest {
      name: string;
      email: string;
      password: string;
      password_confirmation: string;
      verification_code: string;
      organisation_ids?: number[];
    }
  }

  /**
   * namespace Reports
   *
   * backend api module: "reports"
   */
  namespace Reports {
    /** Report filter parameters */
    interface ReportFilterParams {
      /** Start date (required) */
      start_date: string;
      /** End date (required) */
      end_date: string;
      /** Group by period */
      group_by?: 'day' | 'week' | 'month' | 'quarter' | 'year';
      /** Country codes array */
      countries?: string[];
      /** Order status array */
      states?: ('completed' | 'cancelled' | 'processing' | 'pending')[];
      /** Payment status array */
      payment_states?: ('completed' | 'pending' | 'failed' | 'cancelled')[];
      /** Organization ID (optional when product_id is provided) */
      organisation_id?: number;
      /** Product ID (optional) */
      product_id?: number;
      /** Currency code */
      currency?: string;
      /** Timezone */
      timezone?: string;
      /** Include refunds */
      include_refunds?: boolean;
      /** Refund status */
      refund_status?: 'success' | 'pending' | 'failed';
    }

    /** Report period info */
    interface ReportPeriod {
      start_date: string;
      end_date: string;
      group_by: string;
    }

    /** Country data in reports */
    interface CountryData {
      country_code: string;
      country_name: string;
      sales_amount?: number;
      order_count?: number;
      quantity_sold?: number;
    }

    /** Sales report summary */
    interface SalesReportSummary {
      total_sales: number;
      total_orders: number;
      average_order_value: number;
      currency: string;
      period: ReportPeriod;
    }

    /** Sales report data item */
    interface SalesReportDataItem {
      period: string;
      period_label: string;
      sales_amount: number;
      order_count: number;
      average_order_value: number;
      refund_amount: number;
      net_sales: number;
      countries: CountryData[];
    }

    /** Sales report response */
    interface SalesReportResponse {
      daily_sales_chart: {
        xAxis: { data: string[] };
        series: [{ data: number[] }];
      };
      dual_axis_chart: {
        xAxis: { data: string[] };
        series: [
          { name: string; yAxisIndex: 0; data: number[] },
          { name: string; yAxisIndex: 1; data: number[] }
        ];
      };
      regional_sales_amount_chart: {
        series: [{ data: Array<{ name: string; value: number }> }];
      };
    }

    /** Volume report summary */
    interface VolumeReportSummary {
      total_quantity: number;
      total_orders: number;
      average_items_per_order: number;
      period: ReportPeriod;
    }

    /** Volume report data item */
    interface VolumeReportDataItem {
      period: string;
      period_label: string;
      quantity_sold: number;
      order_count: number;
      average_items_per_order: number;
      countries: CountryData[];
    }

    /** Volume report response */
    interface VolumeReportResponse {
      daily_quantity_chart: {
        xAxis: { data: string[] };
        series: [{ data: number[] }];
      };
      regional_sales_quantity_chart: {
        series: [{ data: Array<{ name: string; value: number }> }];
      };
    }

    /** Refund report summary */
    interface RefundReportSummary {
      total_refunds: number;
      total_refund_amount: number;
      refund_rate: number;
      average_refund_amount: number;
      period: ReportPeriod;
    }

    /** Refund report data item */
    interface RefundReportDataItem {
      period: string;
      period_label: string;
      refund_count: number;
      refund_amount: number;
      refund_rate: number;
      countries: CountryData[];
    }

    /** Refund trend chart data */
    interface RefundTrendChart {
      xAxis: {
        data: string[];
      };
      series: Array<{
        name: string;
        data: number[];
      }>;
    }

    /** Refund reasons chart data item */
    interface RefundReasonsChartDataItem {
      name: string;
      value: number;
      color: string;
    }

    /** Refund reasons chart data */
    interface RefundReasonsChart {
      data: RefundReasonsChartDataItem[];
    }

    /** Refund report response */
    interface RefundReportResponse {
      summary: RefundReportSummary;
      data: RefundReportDataItem[];
      filters_applied: ReportFilterParams;
      refund_trend_chart: RefundTrendChart;
      refund_reasons_chart: RefundReasonsChart;
    }

    /** Order status report summary */
    interface OrderStatusReportSummary {
      total_orders: number;
      period: ReportPeriod;
    }

    /** Order status chart data item */
    interface OrderStatusChartDataItem {
      name: string;
      value: number;
      color: string;
    }

    /** Payment status chart data item */
    interface PaymentStatusChartDataItem {
      name: string;
      value: number;
      color: string;
    }

    /** Order status chart data */
    interface OrderStatusChartData {
      data: OrderStatusChartDataItem[];
    }

    /** Payment status chart data */
    interface PaymentStatusChartData {
      data: PaymentStatusChartDataItem[];
    }

    /** Order status report response */
    interface OrderStatusReportResponse {
      order_status_chart: OrderStatusChartData;
      payment_status_chart: PaymentStatusChartData;
    }

    /** Export report request */
    interface ExportReportRequest {
      report_type: 'sales' | 'volume' | 'refunds' | 'order_status' | 'product_ranking';
      format: 'xlsx' | 'csv' | 'pdf';
      filename?: string;
      include_summary?: boolean;
      include_charts?: boolean;
      max_records?: number;
      email_to?: string;
    }

    /** Product ranking filter parameters */
    interface ProductRankingFilterParams {
      /** Start date (required) */
      start_date: string;
      /** End date (required) */
      end_date: string;
      /** Organization ID (optional for system admin) */
      organisation_id?: number;
      /** Limit number of products returned (1-100, default: 10) */
      limit?: number;
      /** Product IDs array */
      product_ids?: number[];
      /** Country codes array */
      countries?: string[];
      /** Order status array */
      states?: ('completed' | 'cancelled' | 'processing' | 'pending')[];
      /** Payment status array */
      payment_states?: ('completed' | 'pending' | 'failed' | 'cancelled')[];
      /** Currency code */
      currency?: string;
      /** Include refunds */
      include_refunds?: boolean;
    }

    /** Product ranking item */
    interface ProductRankingItem {
      rank: number;
      store_variant_id: number;
      product_id: number;
      organisation_id: number;
      product_name: string;
      product_slug: string;
      product_package: string;
      total_quantity: number;
      total_sales: number;
      total_sales_formatted: string;
    }

    /** Chart data for product ranking */
    interface ProductRankingChartData {
      xAxis: {
        data: string[];
      };
      series: Array<{
        name: string;
        data: number[];
      }>;
    }

    /** Product ranking summary */
    interface ProductRankingSummary {
      total_products: number;
      limit: number;
      period: {
        start_date: string;
        end_date: string;
        days: number;
      };
    }

    /** Product ranking report response */
    interface ProductRankingReportResponse {
      sales_rankings: ProductRankingItem[];
      quantity_rankings: ProductRankingItem[];
      sales_ranking_chart: ProductRankingChartData;
      quantity_ranking_chart: ProductRankingChartData;
      summary: ProductRankingSummary;
    }

    /** Export report response */
    interface ExportReportResponse {
      export_id: string;
      status: string;
      estimated_completion: string;
      format: string;
      report_type: string;
      estimated_record_count: number;
      async: boolean;
      progress_url: string;
      email_notification?: string;
    }
  }

  /**
   * namespace Product
   *
   * backend api module: "product"
   */
  namespace Product {
    /** Product basic info from API */
    interface ProductInfo {
      id: number;
      store_variant_id: string;
      code: string;
      sku: string;
      name: string;
      slug: string;
      package: string;
      enabled: boolean;
      organisation: {
        id: number;
        name: string;
        code: string;
        status: string;
      };
      created_at: string;
      updated_at: string;
    }

    /** Product details with extended info */
    interface ProductDetails extends ProductInfo {
      organisation: {
        id: number;
        name: string;
        code: string;
        status: string;
        details?: {
          industry?: string;
          size?: string;
        };
      };
    }

    /** Product list response */
    interface ProductListResponse {
      data: ProductInfo[];
      meta: {
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
        from: number;
        to: number;
      };
    }

    /** Accessible product info */
    interface AccessibleProduct {
      id: number;
      name: string;
      slug: string;
      organisation: {
        id: number;
        name: string;
        code: string;
      };
    }

    /** Product statistics */
    interface ProductStatistics {
      total_products: number;
      enabled_products: number;
      disabled_products: number;
      new_products_this_month: number;
    }

    /** Game data for UI (adapted from API) */
    interface GameData {
      id: string;
      coverImage: string;
      name: string;
      code: string;
      multiLangName: Record<string, string>;
      languages: string[];
      onHand: number;
      enabled: boolean;
      price: number;
      sales: number;
      variants: GameVariant[];
      isExpanded?: boolean;
      organisation?: {
        id: number;
        name: string;
        code: string;
      };
    }

    /** Game variant data for UI */
    interface GameVariant {
      id: string;
      name: string;
      code: string;
      onHand: number;
      price: number;
      enabled: boolean;
      type: 'default' | 'special' | 'collector' | 'limited';
      description?: string;
    }
  }

  /**
   * namespace ProductPermission
   *
   * backend api module: "product-permission"
   */
  namespace ProductPermission {
    /** Permission types */
    type PermissionType = 'view-reports';

    /** Product permission info */
    interface Permission {
      id: number;
      permission_type: PermissionType;
      expires_at: string | null;
      granted_at: string;
      granted_by: {
        id: number;
        name: string;
      } | null;
      notes: string | null;
    }

    /** User with permission info */
    interface AuthorizedUser {
      id: number;
      name: string;
      email: string;
      permission: Permission;
    }

    /** Authorized users response */
    interface AuthorizedUsersResponse {
      users: AuthorizedUser[];
    }

    /** Grant permission request */
    interface GrantPermissionRequest {
      user_id: number;
      permission_type: PermissionType;
      expires_at?: string;
      notes?: string;
    }

    /** Revoke permission request */
    interface RevokePermissionRequest {
      permission_type: PermissionType;
    }

    /** Permission response */
    interface PermissionResponse {
      permission: Permission;
    }

    /** Accessible product info */
    interface AccessibleProduct {
      id: number;
      name: string;
      slug: string;
      organisation: {
        id: number;
        name: string;
        code: string;
      };
    }

    /** User product permission with product info */
    interface UserProductPermission {
      id: number;
      product: {
        id: number;
        name: string;
        slug: string;
        organisation: {
          id: number;
          name: string;
        };
      };
      permission_type: PermissionType;
      expires_at: string | null;
      granted_at: string;
      granted_by: {
        id: number;
        name: string;
      } | null;
      notes: string | null;
    }

    /** User product permissions response */
    interface UserProductPermissionsResponse {
      permissions: UserProductPermission[];
    }

    /** Grant multiple permissions request */
    interface GrantMultiplePermissionsRequest {
      user_id: number;
      product_ids: number[];
      permission_type: PermissionType;
      expires_at?: string;
      notes?: string;
    }

    /** Grant multiple permissions response */
    interface GrantMultiplePermissionsResponse {
      granted_count: number;
      permissions: Permission[];
    }

    /** Revoke multiple permissions request */
    interface RevokeMultiplePermissionsRequest {
      product_ids: number[];
      permission_type?: PermissionType;
    }

    /** Revoke multiple permissions response */
    interface RevokeMultiplePermissionsResponse {
      revoked_count: number;
    }
  }
}
