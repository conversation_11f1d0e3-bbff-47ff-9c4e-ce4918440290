import { locale } from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';
import { localStg } from '@/utils/storage';
import { getAppLanguageWithDetection } from '@/utils/locale';

/**
 * Set dayjs locale
 *
 * @param lang
 */
export function setDayjsLocale(lang?: App.I18n.LangType) {
  const localMap = {
    'zh-CN': 'zh-cn',
    'en-US': 'en'
  } satisfies Record<App.I18n.LangType, string>;

  // Use provided lang, or get from storage with browser detection, or fallback to detected language
  const l = lang || getAppLanguageWithDetection(localStg.get('lang'));

  locale(localMap[l]);
}
