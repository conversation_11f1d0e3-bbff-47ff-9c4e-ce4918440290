import type { App } from 'vue';
import { createI18n } from 'vue-i18n';
import { localStg } from '@/utils/storage';
import { getAppLanguageWithDetection } from '@/utils/locale';
import messages from './locale';

// Get initial locale with browser detection fallback
const initialLocale = getAppLanguageWithDetection(localStg.get('lang'));

// Save the detected language to localStorage if it wasn't set before
if (!localStg.get('lang')) {
  localStg.set('lang', initialLocale);
}

const i18n = createI18n({
  locale: initialLocale,
  fallbackLocale: 'en-US',
  messages,
  legacy: false
});

/**
 * Setup plugin i18n
 *
 * @param app
 */
export function setupI18n(app: App) {
  app.use(i18n);
}

export const $t = i18n.global.t as App.I18n.$T;

export function setLocale(locale: App.I18n.LangType) {
  i18n.global.locale.value = locale;
}
