<script setup lang="tsx">
import { ref, onMounted } from 'vue';
import { $t } from '@/locales';
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';
import { fetchProductStatistics } from '@/service/api';
import { adaptProductStatisticsToGameStats } from '@/utils/adapters/product-adapter';

defineOptions({
  name: 'GameStats'
});

// 统计数据
const stats = ref({
  totalGames: 0,
  enabledGames: 0,
  totalInventory: 0,
  newGames: 0
});

const loading = ref(false);

// 加载统计数据
async function loadStats() {
  try {
    loading.value = true;
    const { data } = await fetchProductStatistics();

    if (data?.success && data.data) {
      const adaptedStats = adaptProductStatisticsToGameStats(data.data);
      stats.value = adaptedStats;
    } else {
      console.warn('Failed to load product statistics:', data?.message);
    }
  } catch (error: any) {
    console.error('Failed to load product statistics:', error);
    // 使用默认值，不显示错误消息，因为这是统计数据
  } finally {
    loading.value = false;
  }
}

// 初始加载
onMounted(() => {
  loadStats();
});
</script>

<template>
  <StatsGrid>
    <StatsCard
      :title="$t('page.game.totalGames')"
      :value="stats.totalGames"
      :unit="$t('common.unit.games')"
      :color="{ start: '#007aff', end: '#0056cc' }"
      icon="mdi:gamepad-variant"
      :loading="loading"
    />
    <StatsCard
      :title="$t('page.game.enabledGames')"
      :value="stats.enabledGames"
      :unit="$t('common.unit.games')"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      icon="mdi:check-circle"
      :loading="loading"
    />
    <StatsCard
      :title="$t('page.game.totalInventory')"
      :value="stats.totalInventory"
      :unit="$t('common.unit.copies')"
      :color="{ start: '#722ed1', end: '#531dab' }"
      icon="mdi:package-variant"
      :loading="loading"
    />
    <StatsCard
      :title="$t('page.game.newGames')"
      :value="stats.newGames"
      :unit="$t('common.unit.games')"
      :color="{ start: '#fa8c16', end: '#d46b08' }"
      icon="mdi:plus-circle"
      :loading="loading"
    />
  </StatsGrid>
</template>
