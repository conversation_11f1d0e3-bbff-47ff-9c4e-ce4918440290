<script setup lang="tsx">
import { computed, h, ref, watch, onBeforeUnmount } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import {
  NButton,
  NDataTable,
  NModal,
  NCard,
  NTag,
  NAutoComplete,
  NEmpty,
  NPopconfirm,
  NSpin,
  NTooltip,
  NAvatar,
  NText,
  NIcon,
  NConfigProvider
} from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import {
  fetchProductAuthorizedUsers,
  fetchGrantProductPermission,
  fetchRevokeProductPermission
} from '@/service/api';
import { fetchUsersList } from '@/service/api';

defineOptions({
  name: 'ProductPermissionModal'
});

interface Props {
  /** Modal visibility */
  visible: boolean;
  /** Product ID */
  productId?: number;
  /** Product name */
  productName?: string;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<Emits>();

const appStore = useAppStore();
const themeStore = useThemeStore();
const isMobile = computed(() => appStore.isMobile);

// Dark mode theme overrides for better visibility
const themeOverrides = computed(() => {
  if (!themeStore.darkMode) return {};

  return {
    DataTable: {
      thColor: '#374151',
      thColorHover: '#4b5563',
      tdColor: '#1f2937',
      tdColorHover: '#374151',
      tdColorStriped: '#1a202c',
      borderColor: '#374151',
      thTextColor: '#d1d5db',
      tdTextColor: '#e5e7eb'
    },
    Card: {
      color: '#1f2937',
      colorModal: '#1f2937',
      borderColor: '#374151',
      textColor: '#e5e7eb'
    },
    Modal: {
      color: '#1f2937',
      textColor: '#e5e7eb'
    },
    AutoComplete: {
      color: '#374151',
      colorDisabled: '#1f2937',
      textColor: '#e5e7eb',
      textColorDisabled: '#9ca3af',
      placeholderColor: '#9ca3af',
      borderColor: '#4b5563',
      borderColorHover: '#6b7280',
      borderColorFocus: '#3b82f6',
      boxShadowFocus: '0 0 0 2px rgba(59, 130, 246, 0.2)'
    },
    Input: {
      color: '#374151',
      colorDisabled: '#1f2937',
      textColor: '#e5e7eb',
      textColorDisabled: '#9ca3af',
      placeholderColor: '#9ca3af',
      borderColor: '#4b5563',
      borderColorHover: '#6b7280',
      borderColorFocus: '#3b82f6',
      boxShadowFocus: '0 0 0 2px rgba(59, 130, 246, 0.2)'
    }
  };
});

// Modal class for responsive design
const modalClass = computed(() => {
  if (isMobile.value) {
    return 'size-full top-0px rounded-0';
  }
  return 'w-85vw max-w-1400px min-w-900px top-16px';
});

// State
const loading = ref(false);
const grantLoading = ref(false);
const authorizedUsers = ref<Api.ProductPermission.AuthorizedUser[]>([]);

// User search state
const userSearchLoading = ref(false);
const userSearchValue = ref('');
const userSearchOptions = ref<Array<{ label: string; value: string; email: string }>>([]);
const selectedUserId = ref<number | null>(null);
const showNoResultsHint = ref(false);
const isSelectingUser = ref(false); // Flag to prevent search when selecting

// Load authorized users
async function loadAuthorizedUsers() {
  if (!props.productId) return;

  try {
    loading.value = true;
    const { data } = await fetchProductAuthorizedUsers(props.productId);
    if (data?.success) {
      authorizedUsers.value = data.data?.users || [];
    }
  } catch (error: any) {
    console.error('Failed to load authorized users:', error);
    const errorMessage = error?.response?.data?.message || error?.message || 'Failed to load authorized users';
    window.$message?.error(errorMessage);
  } finally {
    loading.value = false;
  }
}

// Search users with debounce
let searchTimeout: NodeJS.Timeout | null = null;

async function searchUsers(query: string) {
  // Skip search if user is selecting from dropdown
  if (isSelectingUser.value) {
    isSelectingUser.value = false;
    return;
  }

  // Clear previous timeout
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  if (!query || query.length < 2) {
    userSearchOptions.value = [];
    userSearchLoading.value = false;
    showNoResultsHint.value = false;
    return;
  }

  // Show loading immediately for better UX
  userSearchLoading.value = true;
  showNoResultsHint.value = false;

  // Debounce search
  searchTimeout = setTimeout(async () => {
    try {
      userSearchLoading.value = true;
      const { data } = await fetchUsersList({
        search: query,
        per_page: 10,
        only_owner: false
      });

      if (data?.success) {
        // Filter out users who already have this permission type
        const existingUserIds = authorizedUsers.value.map(user => user.id);
        const users = data.data?.data || [];

        const filteredUsers = users?.filter(user => !existingUserIds.includes(user.id)) || [];

        userSearchOptions.value = filteredUsers.map(user => ({
          label: `${user.name} (${user.email})`,
          value: user.id.toString(),
          email: user.email
        }));

        // Show hint when no users found (only for longer queries)
        const shouldShowHint = filteredUsers.length === 0 && query.length >= 2;
        showNoResultsHint.value = shouldShowHint;

        console.log('Search results:', {
          query,
          totalUsers: users.length,
          filteredUsers: filteredUsers.length,
          shouldShowHint
        });
      } else {
        console.error('User search API request failed:', data);
        window.$message?.error($t('page.game.permission.searchUserFailed'));
        showNoResultsHint.value = false;
      }
    } catch (error) {
      console.error('Failed to search users:', error);
      window.$message?.error($t('page.game.permission.searchUserFailed'));
      showNoResultsHint.value = false;
    } finally {
      userSearchLoading.value = false;
    }
  }, 300); // 300ms debounce
}

// Handle user selection
function handleUserSelect(value: string) {
  isSelectingUser.value = true; // Set flag to prevent search trigger
  selectedUserId.value = parseInt(value);
  // Find the selected user's name for display
  const selectedUser = userSearchOptions.value.find(option => option.value === value);
  if (selectedUser) {
    userSearchValue.value = selectedUser.label;
  }
  // Clear search options and hide hint after selection
  userSearchOptions.value = [];
  showNoResultsHint.value = false;
}

// Grant permission
async function grantPermission() {
  if (!props.productId || !selectedUserId.value) {
    window.$message?.warning($t('page.game.permission.selectUser'));
    return;
  }

  // Check if user already has this permission type
  const existingPermission = authorizedUsers.value.find(
    user => user.id === selectedUserId.value && user.permission.permission_type === 'view-reports'
  );

  if (existingPermission) {
    window.$message?.warning('User already has this permission type');
    return;
  }

  try {
    grantLoading.value = true;
    const { data } = await fetchGrantProductPermission(props.productId, {
      user_id: selectedUserId.value,
      permission_type: 'view-reports'
    });

    if (data?.success) {
      window.$message?.success($t('page.game.permission.grantSuccess'));
      // Clear form
      selectedUserId.value = null;
      userSearchValue.value = '';
      userSearchOptions.value = [];
      showNoResultsHint.value = false;
      // Reload the list
      await loadAuthorizedUsers();
    } else {
      window.$message?.error(data?.message || $t('page.game.permission.grantFailed'));
    }
  } catch (error: any) {
    console.error('Failed to grant permission:', error);
    const errorMessage = error?.response?.data?.message || error?.message || $t('page.game.permission.grantFailed');
    window.$message?.error(errorMessage);
  } finally {
    grantLoading.value = false;
  }
}

// Revoke permission
async function revokePermission(userId: number, permissionType: Api.ProductPermission.PermissionType) {
  if (!props.productId) return;

  try {
    const { data } = await fetchRevokeProductPermission(props.productId, userId, {
      permission_type: permissionType
    });

    if (data?.success) {
      window.$message?.success($t('page.game.permission.revokeSuccess'));
      await loadAuthorizedUsers();
    } else {
      window.$message?.error(data?.message || $t('page.game.permission.revokeFailed'));
    }
  } catch (error: any) {
    console.error('Failed to revoke permission:', error);
    const errorMessage = error?.response?.data?.message || error?.message || $t('page.game.permission.revokeFailed');
    window.$message?.error(errorMessage);
  }
}

// Table columns
const columns: DataTableColumns<Api.ProductPermission.AuthorizedUser> = [
  {
    title: $t('page.game.permission.user'),
    key: 'user',
    render: row => h('div', { class: 'flex items-center gap-3' }, [
      h(NAvatar, {
        size: 'small',
        round: true,
        color: 'transparent',
        style: 'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }, {
        default: () => h(NIcon, { size: 16 }, {
          default: () => h(SvgIcon, { icon: 'mdi:account' })
        })
      }),
      h('div', { class: 'flex-1' }, [
        h(NText, { class: 'font-medium text-14px' }, { default: () => row.name }),
        h('div', { class: 'text-12px text-gray-500 mt-1' }, row.email)
      ])
    ])
  },
  {
    title: $t('page.game.permission.permissionType'),
    key: 'permission_type',
    render: row => h(NTag, {
      type: 'info',
      size: 'small',
      round: true,
      class: 'permission-type-tag'
    }, {
      default: () => row.permission.permission_type
    })
  },
  {
    title: $t('page.game.permission.grantedAt'),
    key: 'granted_at',
    render: row => h(NText, { class: 'text-13px' }, { default: () => new Date(row.permission.granted_at).toLocaleString() })
  },
  {
    title: $t('page.game.permission.grantedBy'),
    key: 'granted_by',
    render: row => h(NText, {
      class: 'text-13px font-medium',
      type: 'primary'
    }, { default: () => row.permission.granted_by ? row.permission.granted_by.name : $t('page.game.permission.system') })
  },
  {
    title: $t('page.game.permission.expiresAt'),
    key: 'expires_at',
    render: row => h(NText, {
      class: 'text-13px',
      type: row.permission.expires_at ? 'warning' : 'success'
    }, { default: () => row.permission.expires_at ? new Date(row.permission.expires_at).toLocaleString() : $t('page.game.permission.never') })
  },
  {
    title: $t('page.game.permission.actions'),
    key: 'actions',
    width: 120,
    render: row => h(
      NPopconfirm,
      {
        onPositiveClick: () => revokePermission(row.id, row.permission.permission_type),
        positiveText: 'Confirm',
        negativeText: 'Cancel'
      },
      {
        trigger: () => h(
          NTooltip,
          { placement: 'top' },
          {
            trigger: () => h(
              NButton,
              {
                size: 'small',
                type: 'error',
                secondary: true,
                round: true,
                class: 'hover:scale-105 transition-transform duration-200'
              },
              {
                icon: () => h(SvgIcon, { icon: 'mdi:delete-outline', class: 'text-14px' }),
                default: () => 'Revoke'
              }
            ),
            default: () => $t('page.game.permission.revoke')
          }
        ),
        default: () => $t('page.game.permission.revokeConfirm')
      }
    )
  }
];

// Watch for modal visibility changes
watch(() => props.visible, (visible) => {
  if (visible && props.productId) {
    loadAuthorizedUsers();
  }
});



// Cleanup on component unmount
onBeforeUnmount(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
});
</script>

<template>
  <NConfigProvider :theme-overrides="themeOverrides">
    <NModal
      :show="visible"
      :class="modalClass"
      preset="card"
      :bordered="false"
      :segmented="{ content: 'soft', footer: 'soft' }"
      :closable="true"
      :close-on-esc="true"
      :mask-closable="false"
      @update:show="emit('update:visible', $event)"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-br from-primary-400 to-primary-600">
          <SvgIcon icon="mdi:shield-account" class="text-white text-20px" />
        </div>
        <div>
          <h3 class="text-18px font-semibold text-gray-800 dark:text-gray-200 mb-1">
            {{ $t('page.game.permission.modalTitle', { productName: productName || 'Unknown Product' }) }}
          </h3>
          <p class="text-13px text-gray-500 dark:text-gray-400">Manage user access permissions for this product</p>
        </div>
      </div>
    </template>

    <div class="space-y-6">


      <!-- Add User Section -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-8px p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center gap-4">
          <!-- Title Section -->
          <div class="flex items-center gap-2 flex-shrink-0">
            <div class="flex items-center justify-center w-7 h-7 rounded-full bg-gradient-to-br from-success-400 to-success-600">
              <SvgIcon icon="mdi:account-plus" class="text-white text-14px" />
            </div>
            <span class="text-14px font-medium text-gray-800 dark:text-gray-200">{{ $t('page.game.permission.addUser') }}</span>
          </div>

          <!-- Search Input -->
          <div class="flex-1 min-w-0">
            <div class="relative">
              <NAutoComplete
                v-model:value="userSearchValue"
                :options="userSearchOptions"
                :loading="userSearchLoading"
                :placeholder="$t('page.game.permission.searchUserPlaceholder')"
                clearable
                size="medium"
                @select="handleUserSelect"
                @update:value="searchUsers"
              />
              <!-- No results hint -->
              <div v-if="showNoResultsHint" class="absolute top-full left-0 right-0 mt-1 px-3 py-2 text-xs text-gray-500 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-2">
                  <SvgIcon icon="mdi:information-outline" class="text-gray-400" />
                  <span>{{ $t('page.game.permission.noUsersFound') }} - {{ $t('page.game.permission.searchHint') }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Grant Button -->
          <div class="flex-shrink-0">
            <NButton
              type="primary"
              size="medium"
              :disabled="!selectedUserId"
              :loading="grantLoading"
              class="h-36px font-medium px-4"
              @click="grantPermission"
            >
              <template #icon>
                <SvgIcon icon="mdi:shield-plus" />
              </template>
              {{ $t('page.game.permission.grant') }}
            </NButton>
          </div>
        </div>
      </div>

      <!-- Authorized Users List -->
      <NCard size="small" class="shadow-sm border border-gray-200">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-br from-info-400 to-info-600">
                <SvgIcon icon="mdi:account-group" class="text-white text-16px" />
              </div>
              <span class="text-16px font-medium text-gray-800">{{ $t('page.game.permission.authorizedUsers') }}</span>
            </div>
            <NTag v-if="authorizedUsers.length > 0" type="info" size="small" round>
              {{ authorizedUsers.length }} {{ authorizedUsers.length === 1 ? 'user' : 'users' }}
            </NTag>
          </div>
        </template>

        <NSpin :show="loading">
          <div class="min-h-200px">
            <NDataTable
              :columns="columns"
              :data="authorizedUsers"
              :pagination="false"
              :bordered="false"
              size="medium"
              :max-height="450"
              class="rounded-6px overflow-hidden"
            >
              <template #empty>
                <div class="py-12">
                  <NEmpty
                    :description="$t('page.game.permission.noAuthorizedUsers')"
                    class="text-gray-500"
                  >
                    <template #icon>
                      <SvgIcon icon="mdi:account-off" class="text-48px text-gray-300" />
                    </template>
                  </NEmpty>
                </div>
              </template>
            </NDataTable>
          </div>
        </NSpin>
      </NCard>
    </div>
  </NModal>
  </NConfigProvider>
</template>

<style scoped>
/* Modal styling */
:deep(.n-modal) {
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

:deep(.n-card-header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.n-card__content) {
  padding: 20px 24px;
}

/* Filter section styling */
.bg-gray-50 {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
}

/* Card styling */
:deep(.n-card) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.n-card:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Button styling */
:deep(.n-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
}

/* Table styling */
/* Custom table styles removed to allow Naive UI theme-overrides to take effect */

/* Input styling */
:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-auto-complete) {
  border-radius: 6px;
}

/* Tag styling */
:deep(.n-tag) {
  font-weight: 500;
}

/* Permission Type Tag */
:deep(.permission-type-tag) {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/*
  Removed conflicting dark mode overrides.
  The component now relies on the `themeOverrides` prop passed to `NConfigProvider`
  for consistent dark mode styling.
*/

html.dark .bg-gray-50 {
  background: linear-gradient(135deg, #2d3748 0%, #374151 100%) !important;
  border-color: #4b5563 !important;
}

html.dark .text-gray-700 {
  color: #d1d5db !important;
}

html.dark .text-gray-500 {
  color: #9ca3af !important;
}

html.dark .text-gray-800 {
  color: #f3f4f6 !important;
}

html.dark .border-gray-200 {
  border-color: #4b5563 !important;
}

/* Avatar gradient */
.avatar-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-1.lg\\:grid-cols-12 {
    grid-template-columns: 1fr;
  }

  :deep(.n-card-header) {
    padding: 16px 20px 12px;
  }

  :deep(.n-card__content) {
    padding: 16px 20px;
  }
}
</style>
