<script setup lang="ts">
import { watch, ref, computed, inject, onMounted, type Ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import { fetchSalesReport } from '@/service/api/reports';
import dayjs from 'dayjs';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'DailySalesAmountChart'
});

const appStore = useAppStore();
const authStore = useAuthStore();

// Loading and error states
const loading = ref(true);
const error = ref<string | null>(null);

// Get selected organization ID and date range from parent component
const selectedOrganizationId = inject<Ref<number | null>>('selectedOrganizationId', ref(null));
const selectedDateRange = inject<Ref<[number, number]>>('selectedDateRange', ref([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]));

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: $t('page.home.dailySalesAmount'),
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: function(params: any) {
      let result = params[0].name + '<br/>';
      params.forEach((param: any) => {
        result += param.marker + param.seriesName + ': $' + param.value + '<br/>';
      });
      return result;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    name: $t('page.home.salesAmount') + ' ($)',
    axisLabel: {
      formatter: '${value}'
    }
  },
  series: [
    {
      color: '#8e9dff',
      name: $t('page.home.salesAmount'),
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0.25,
              color: '#8e9dff'
            },
            {
              offset: 1,
              color: '#fff'
            }
          ]
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: [] as number[]
    }
  ]
}));

async function loadChartData() {
  // Don't load data if no organization is selected or no date range
  if (!selectedOrganizationId?.value || !selectedDateRange?.value) {
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    error.value = null;

    // Convert timestamp to date string
    const [startTimestamp, endTimestamp] = selectedDateRange.value;
    const startDate = dayjs(startTimestamp).format('YYYY-MM-DD');
    const endDate = dayjs(endTimestamp).format('YYYY-MM-DD');

    // Prepare API parameters
    const params: Api.Reports.ReportFilterParams = {
      start_date: startDate,
      end_date: endDate,
      group_by: 'day',
      organisation_id: selectedOrganizationId.value,
      currency: 'USD'
    };

    const { data: response, error: apiError } = await fetchSalesReport(params);

    if (apiError || !response?.data) {
      const errorMessage = apiError?.message || 'Failed to load sales data';
      console.error('Failed to load sales data:', apiError);
      error.value = errorMessage;
      return;
    }

    const reportData = response.data;

    updateOptions(opts => {
      // Extract dates and sales amounts from API response
      let dates: string[] = [];
      let salesAmounts: number[] = [];

      // Check if daily_sales_chart data exists
      if (reportData?.daily_sales_chart?.xAxis?.data && reportData?.daily_sales_chart?.series?.[0]?.data) {
        dates = reportData.daily_sales_chart.xAxis.data;
        salesAmounts = reportData.daily_sales_chart.series[0].data;

        // If API returns empty arrays, show placeholder
        if (dates.length === 0 || salesAmounts.length === 0) {
          dates = ['No Data'];
          salesAmounts = [0];
        }
      } else {
        // If no data available, show empty chart with placeholder
        dates = ['No Data'];
        salesAmounts = [0];
      }

      opts.xAxis.data = dates;
      opts.series[0].data = salesAmounts;

      return opts;
    });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    console.error('Failed to load chart data:', err);
    error.value = errorMessage;

    // Fallback to empty data on error
    updateOptions(opts => {
      opts.xAxis.data = [];
      opts.series[0].data = [];
      return opts;
    });
  } finally {
    loading.value = false;
  }
}

function updateLocale() {
  try {
    updateOptions((opts, factory) => {
      const originOpts = factory();

      opts.title.text = originOpts.title.text;
      opts.yAxis.name = originOpts.yAxis.name;
      opts.series[0].name = originOpts.series[0].name;

      return opts;
    });
  } catch (error) {
    console.error('Failed to update chart locale:', error);
  }
}

async function init() {
  loadChartData();
}

// Watch for locale changes
watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// Watch for selected organization changes and reload data
watch(() => selectedOrganizationId?.value, () => {
  loadChartData();
}, { immediate: false });

// Watch for date range changes and reload data
watch(() => selectedDateRange?.value, () => {
  loadChartData();
}, { immediate: false });

// Initialize component
onMounted(() => {
  init();
});
</script>

<template>
  <div class="h-360px overflow-hidden relative">
    <!-- Loading state -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 bg-opacity-75">
      <div class="flex flex-col items-center gap-2">
        <NSpin size="medium" />
        <span class="text-sm text-gray-500">{{ $t('common.loading') }}</span>
      </div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 bg-opacity-75">
      <div class="flex flex-col items-center gap-2 text-center p-4">
        <SvgIcon icon="mdi:alert-circle-outline" class="text-32px text-error" />
        <span class="text-sm text-gray-500">{{ $t('common.loadError') }}</span>
        <NButton size="small" @click="loadChartData">{{ $t('common.retry') }}</NButton>
      </div>
    </div>

    <!-- Chart -->
    <div ref="domRef" class="h-full w-full"></div>
  </div>
</template>

<style scoped></style>
