<script setup lang="ts">
import { computed, ref, inject, watch, type Ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { usePermission } from '@/hooks/common/permission';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import OrganizationSelector from '@/components/common/organization-selector.vue';
import dayjs from 'dayjs';

defineOptions({
  name: 'TimeControlBar'
});

// Define emits
const emit = defineEmits<{
  'update:dateRange': [dateRange: [number, number]];
}>();

const appStore = useAppStore();
const { isSystemAdmin, userOrganizations } = usePermission();

// Get selected organization ID from parent component
const selectedOrganizationId = inject<Ref<number | null>>('selectedOrganizationId', ref(null));

// Time range state
const selectedTimeRange = ref<string>('pastMonth'); // Default to past month
const customDateRange = ref<[number, number] | null>(null);
const isCustomRange = ref(false); // Only true when user explicitly selects "custom"
const isUsingCustomRange = ref(false); // True when using custom range (either from custom selection or period navigation)

// Quick time range options with short labels and tooltips
const timeRangeOptions = computed(() => [
  {
    value: 'pastWeek',
    label: $t('page.home.timeControl.shortLabels.week'),
    tooltip: $t('page.home.timeControl.pastWeek')
  },
  {
    value: 'pastMonth',
    label: $t('page.home.timeControl.shortLabels.month'),
    tooltip: $t('page.home.timeControl.pastMonth')
  },
  {
    value: 'pastQuarter',
    label: $t('page.home.timeControl.shortLabels.quarter'),
    tooltip: $t('page.home.timeControl.pastQuarter')
  },
  {
    value: 'pastYear',
    label: $t('page.home.timeControl.shortLabels.year'),
    tooltip: $t('page.home.timeControl.pastYear')
  },
  {
    value: 'custom',
    label: $t('page.home.timeControl.shortLabels.custom'),
    tooltip: $t('page.home.timeControl.customRange')
  }
]);

// Computed property to get organization IDs for the selector based on user role
const allowedOrganizationIds = computed(() => {
  // If current user is root or admin, allow all organizations (no restriction)
  if (isSystemAdmin()) {
    console.log('User is system admin, allowing all organizations');
    return undefined;
  }

  // For non-admin users, return their organization IDs
  const orgIds = userOrganizations.value.map(org => org.id);
  console.log('User organizations for selector:', userOrganizations.value, 'IDs:', orgIds);

  // If user has no organizations, return empty array to prevent loading all organizations
  if (orgIds.length === 0) {
    console.log('User has no organizations, returning empty array');
    return [];
  }

  return orgIds;
});

// Calculate date range based on selected time range
const calculateDateRange = (timeRange: string): [number, number] => {
  const now = dayjs();
  let startDate: dayjs.Dayjs;
  let endDate = now;

  switch (timeRange) {
    case 'pastWeek':
      startDate = now.subtract(1, 'week');
      break;
    case 'pastMonth':
      startDate = now.subtract(1, 'month');
      break;
    case 'pastQuarter':
      startDate = now.subtract(3, 'month');
      break;
    case 'pastYear':
      startDate = now.subtract(1, 'year');
      break;
    default:
      startDate = now.subtract(1, 'month');
  }

  return [startDate.valueOf(), endDate.valueOf()];
};

// Get current effective date range
const effectiveDateRange = computed(() => {
  // If we have a custom date range (either from custom selection or period navigation), use it
  if (isUsingCustomRange.value && customDateRange.value) {
    return customDateRange.value;
  }
  // Otherwise, calculate based on the selected time range
  return calculateDateRange(selectedTimeRange.value);
});

// Handle time range selection change
function handleTimeRangeChange(value: string) {
  selectedTimeRange.value = value;
  isCustomRange.value = value === 'custom';

  // Clear custom date range when switching to predefined ranges
  if (value !== 'custom') {
    customDateRange.value = null;
    isUsingCustomRange.value = false;
  } else {
    isUsingCustomRange.value = true;
  }
}

// Handle custom date range change
function handleCustomDateRangeChange(value: [number, number] | null) {
  customDateRange.value = value;
  if (value) {
    isCustomRange.value = true;
    isUsingCustomRange.value = true;
    selectedTimeRange.value = 'custom';
  }
}

// Navigate to previous period
function goToPreviousPeriod() {
  if (isCustomRange.value && customDateRange.value) {
    // True custom range: shift by the current duration
    const [start, end] = customDateRange.value;
    const duration = end - start;
    customDateRange.value = [start - duration, end - duration];
  } else if (isUsingCustomRange.value && customDateRange.value) {
    // Period navigation range: continue shifting by the original period type
    const currentPeriod = selectedTimeRange.value;
    const [currentStart] = customDateRange.value;
    let newEndDate: dayjs.Dayjs;
    let newStartDate: dayjs.Dayjs;

    switch (currentPeriod) {
      case 'pastWeek':
        newEndDate = dayjs(currentStart);
        newStartDate = newEndDate.subtract(1, 'week');
        break;
      case 'pastMonth':
        newEndDate = dayjs(currentStart);
        newStartDate = newEndDate.subtract(1, 'month');
        break;
      case 'pastQuarter':
        newEndDate = dayjs(currentStart);
        newStartDate = newEndDate.subtract(3, 'month');
        break;
      case 'pastYear':
        newEndDate = dayjs(currentStart);
        newStartDate = newEndDate.subtract(1, 'year');
        break;
      default:
        return; // Unknown period type
    }

    customDateRange.value = [newStartDate.valueOf(), newEndDate.valueOf()];
  } else {
    // First time navigation from predefined range
    const currentPeriod = selectedTimeRange.value;
    const now = dayjs();
    let newEndDate: dayjs.Dayjs;
    let newStartDate: dayjs.Dayjs;

    switch (currentPeriod) {
      case 'pastWeek':
        newEndDate = now.subtract(1, 'week');
        newStartDate = newEndDate.subtract(1, 'week');
        break;
      case 'pastMonth':
        newEndDate = now.subtract(1, 'month');
        newStartDate = newEndDate.subtract(1, 'month');
        break;
      case 'pastQuarter':
        newEndDate = now.subtract(3, 'month');
        newStartDate = newEndDate.subtract(3, 'month');
        break;
      case 'pastYear':
        newEndDate = now.subtract(1, 'year');
        newStartDate = newEndDate.subtract(1, 'year');
        break;
      default:
        return; // Unknown period type
    }

    // Set as custom range internally but don't show the date picker
    customDateRange.value = [newStartDate.valueOf(), newEndDate.valueOf()];
    isUsingCustomRange.value = true;
    // Keep isCustomRange.value = false so date picker doesn't show
    // Keep selectedTimeRange.value unchanged so button stays highlighted
  }
}

// Navigate to next period
function goToNextPeriod() {
  if (isCustomRange.value && customDateRange.value) {
    // True custom range: shift by the current duration
    const [start, end] = customDateRange.value;
    const duration = end - start;
    const newEnd = Math.min(end + duration, dayjs().valueOf());
    const newStart = newEnd - duration;
    customDateRange.value = [newStart, newEnd];
  } else if (isUsingCustomRange.value && customDateRange.value) {
    // Period navigation range: move forward by the original period type
    const currentPeriod = selectedTimeRange.value;
    const [, currentEnd] = customDateRange.value;
    let newEndDate: dayjs.Dayjs;
    let newStartDate: dayjs.Dayjs;

    switch (currentPeriod) {
      case 'pastWeek':
        newStartDate = dayjs(currentEnd);
        newEndDate = newStartDate.add(1, 'week');
        break;
      case 'pastMonth':
        newStartDate = dayjs(currentEnd);
        newEndDate = newStartDate.add(1, 'month');
        break;
      case 'pastQuarter':
        newStartDate = dayjs(currentEnd);
        newEndDate = newStartDate.add(3, 'month');
        break;
      case 'pastYear':
        newStartDate = dayjs(currentEnd);
        newEndDate = newStartDate.add(1, 'year');
        break;
      default:
        return; // Unknown period type
    }

    // Don't go beyond current time
    const now = dayjs().valueOf();
    if (newEndDate.valueOf() > now) {
      newEndDate = dayjs(now);
      switch (currentPeriod) {
        case 'pastWeek':
          newStartDate = newEndDate.subtract(1, 'week');
          break;
        case 'pastMonth':
          newStartDate = newEndDate.subtract(1, 'month');
          break;
        case 'pastQuarter':
          newStartDate = newEndDate.subtract(3, 'month');
          break;
        case 'pastYear':
          newStartDate = newEndDate.subtract(1, 'year');
          break;
      }
    }

    customDateRange.value = [newStartDate.valueOf(), newEndDate.valueOf()];
  } else {
    // If we're at the default period, we can't go forward
    return;
  }
}

// Check if next period button should be disabled
const isNextPeriodDisabled = computed(() => {
  if (isCustomRange.value && customDateRange.value) {
    // For custom ranges, check if we can move forward
    const [, end] = customDateRange.value;
    const now = dayjs().valueOf();
    return end >= now - 24 * 60 * 60 * 1000; // Disable if end date is within 1 day of now
  } else if (isUsingCustomRange.value && customDateRange.value) {
    // For period navigation ranges, check if we can move forward
    const [, end] = customDateRange.value;
    const now = dayjs().valueOf();
    return end >= now - 24 * 60 * 60 * 1000;
  } else {
    // For predefined ranges, disable next period (can't go beyond current)
    return true;
  }
});

// Reset to current period
function resetToCurrentPeriod() {
  // Clear custom range and reset to predefined period
  customDateRange.value = null;
  isCustomRange.value = false;
  isUsingCustomRange.value = false;
  // selectedTimeRange.value stays the same to maintain the selected period type
  console.log('Reset to current period:', selectedTimeRange.value);
}

// Check if reset button should be shown
const shouldShowResetButton = computed(() => {
  return isUsingCustomRange.value || isCustomRange.value;
});

// Handle organization selection change
function handleOrganizationChange(value: number | number[] | null) {
  console.log('Organization selection changed:', value);
  // Since we're not using multiple selection, value should be a single number or null
  if (Array.isArray(value)) {
    if (selectedOrganizationId) {
      selectedOrganizationId.value = value.length > 0 ? value[0] : null;
      console.log('Set selectedOrganizationId to:', selectedOrganizationId.value);
    }
  } else {
    if (selectedOrganizationId) {
      selectedOrganizationId.value = value;
      console.log('Set selectedOrganizationId to:', selectedOrganizationId.value);
    }
  }
}

// Handle when OrganizationSelector has loaded its options
function handleOrganizationSelectorReady(organizations: any[]) {
  console.log('OrganizationSelector ready with organizations:', organizations);

  // If user is system admin and no organization is selected, select the first one
  if (isSystemAdmin() && !selectedOrganizationId?.value && organizations.length > 0) {
    const firstOrg = organizations[0];
    if (selectedOrganizationId && firstOrg.value) {
      selectedOrganizationId.value = firstOrg.value;
      console.log('Auto-selected first organization for system admin:', firstOrg.value, firstOrg.label);
    }
  }
}

// Format date range for display
function formatDateRange(dateRange: [number, number]): string {
  const [start, end] = dateRange;
  const startDate = dayjs(start);
  const endDate = dayjs(end);
  const currentYear = dayjs().year();

  // Check if any date is not in the current year
  const needsYear = startDate.year() !== currentYear || endDate.year() !== currentYear;

  if (needsYear) {
    // Show year when not current year
    return `${startDate.format('YYYY-MM-DD')} ~ ${endDate.format('YYYY-MM-DD')}`;
  } else {
    // Current year, show MM-DD format
    return `${startDate.format('MM-DD')} ~ ${endDate.format('MM-DD')}`;
  }
}

// Watch for effective date range changes and emit to parent component
watch(
  () => effectiveDateRange.value,
  (newRange) => {
    console.log('Effective date range changed:', {
      range: newRange,
      startDate: dayjs(newRange[0]).format('YYYY-MM-DD'),
      endDate: dayjs(newRange[1]).format('YYYY-MM-DD')
    });
    // Emit the new date range to parent component
    emit('update:dateRange', newRange);
  },
  { immediate: true }
);
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <!-- Single Row Layout -->
    <div :class="appStore.isMobile ? 'flex flex-col gap-3' : 'flex items-center gap-4'">
      <!-- Left side: Time controls -->
      <div :class="appStore.isMobile ? 'flex flex-col gap-2' : 'flex items-center gap-4'">
        <!-- Quick Time Range Buttons -->
        <div class="flex items-center gap-1">
          <NButtonGroup size="small">
            <NTooltip
              v-for="option in timeRangeOptions"
              :key="option.value"
              :disabled="!option.tooltip"
            >
              <template #trigger>
                <NButton
                  :type="selectedTimeRange === option.value ? 'primary' : 'default'"
                  size="small"
                  @click="handleTimeRangeChange(option.value)"
                >
                  {{ option.label }}
                </NButton>
              </template>
              {{ option.tooltip }}
            </NTooltip>
          </NButtonGroup>
        </div>

        <!-- Custom Date Range (only show when custom is selected) -->
        <div v-if="isCustomRange" class="flex items-center">
          <NDatePicker
            v-model:value="customDateRange"
            type="daterange"
            size="small"
            :start-placeholder="$t('page.home.timeControl.startDate')"
            :end-placeholder="$t('page.home.timeControl.endDate')"
            clearable
            @update:value="handleCustomDateRangeChange"
          />
        </div>

        <!-- Period Navigation -->
        <div class="flex items-center gap-1">
          <NButton
            size="small"
            quaternary
            circle
            @click="goToPreviousPeriod"
          >
            <template #icon>
              <SvgIcon icon="material-symbols:chevron-left" />
            </template>
          </NButton>
          <NButton
            size="small"
            quaternary
            circle
            :disabled="isNextPeriodDisabled"
            @click="goToNextPeriod"
          >
            <template #icon>
              <SvgIcon icon="material-symbols:chevron-right" />
            </template>
          </NButton>
        </div>

        <!-- Reset Button -->
        <div v-if="shouldShowResetButton" class="flex items-center">
          <NTooltip>
            <template #trigger>
              <NButton
                size="small"
                quaternary
                circle
                @click="resetToCurrentPeriod"
              >
                <template #icon>
                  <SvgIcon icon="material-symbols:home" />
                </template>
              </NButton>
            </template>
            {{ $t('page.home.timeControl.resetToCurrent') }}
          </NTooltip>
        </div>

        <!-- Current Date Range Display (compact) -->
        <div v-if="effectiveDateRange" class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
          {{ formatDateRange(effectiveDateRange) }}
        </div>
      </div>

      <!-- Right side: Organization Selector -->
      <div :class="appStore.isMobile ? 'flex items-center gap-2' : 'flex items-center gap-2 ml-auto'">
        <SvgIcon icon="mdi:office-building" class="text-16px text-gray-500" />
        <div :class="appStore.isMobile ? 'flex-1' : 'w-180px'">
          <OrganizationSelector
            v-model="selectedOrganizationId"
            :organization-ids="allowedOrganizationIds"
            :placeholder="$t('common.selectOrganization')"
            size="small"
            clearable
            @change="handleOrganizationChange"
            @ready="handleOrganizationSelectorReady"
          />
        </div>
      </div>
    </div>
  </NCard>
</template>

<style scoped></style>
