<script setup lang="ts">
import { ref, onMounted, inject, watch, type Ref } from 'vue';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { fetchProductRankingByDateRange } from '@/service/api/reports';

defineOptions({
  name: 'TopSalesQuantityRanking'
});

interface ProductRankingData {
  rank: number;
  store_variant_id: number;
  product_name: string;
  product_slug: string;
  product_package: string;
  total_quantity: number;
  total_sales: number;
  total_sales_formatted: string;
}

// Legacy interface for compatibility with game click events
interface GameRankingData {
  id: string;
  product_id?: number; // Optional for backward compatibility
  organisation_id?: number; // Optional for backward compatibility
  name: string;
  code: string;
  coverImage: string;
  dailySalesQuantity: number;
  price: number;
  sales: number;
  enabled: boolean;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
}

interface Emits {
  (e: 'game-click', game: GameRankingData): void;
}

const emit = defineEmits<Emits>();

// Get selected organization ID and date range from parent component
const selectedOrganizationId = inject<Ref<number | null>>('selectedOrganizationId', ref(null));
const selectedDateRange = inject<Ref<[number, number]>>('selectedDateRange', ref([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]));
const refreshTrigger = inject<Ref<number>>('refreshTrigger', ref(0));

const loading = ref(true);
const rankingData = ref<ProductRankingData[]>([]);


// 获取排名图标
function getRankIcon(rank: number) {
  switch (rank) {
    case 1:
      return 'mdi:trophy';
    case 2:
      return 'mdi:medal';
    case 3:
      return 'mdi:medal-outline';
    default:
      return 'mdi:numeric-' + rank + '-circle';
  }
}

// 获取排名颜色
function getRankColor(rank: number) {
  switch (rank) {
    case 1:
      return '#FFD700'; // 金色
    case 2:
      return '#C0C0C0'; // 银色
    case 3:
      return '#CD7F32'; // 铜色
    default:
      return '#666666'; // 灰色
  }
}

// Load product ranking data
async function loadProductRankingData() {
  // Don't load data if no organization or date range is selected
  if (!selectedOrganizationId?.value || !selectedDateRange?.value) {
    loading.value = false;
    return;
  }

  try {
    loading.value = true;

    const { data: response, error } = await fetchProductRankingByDateRange(
      selectedOrganizationId.value,
      selectedDateRange.value,
      10
    );

    if (!error && response?.data) {
      // Use quantity rankings data from API response
      rankingData.value = response.data.quantity_rankings || [];
    } else {
      console.error('Failed to load product ranking data:', error);
      rankingData.value = [];
    }
  } catch (error) {
    console.error('Failed to load product ranking data:', error);
    rankingData.value = [];
  } finally {
    loading.value = false;
  }
}

// Convert product ranking data to legacy game format for compatibility
function convertToGameData(product: ProductRankingData): GameRankingData {
  return {
    id: product.store_variant_id.toString(),
    product_id: product.store_variant_id, // Add product_id for API calls
    organisation_id: selectedOrganizationId?.value || undefined, // Add organisation_id for API calls
    name: product.product_name,
    code: product.product_slug.toUpperCase(),
    coverImage: product.product_package || 'https://picsum.photos/200/300?random=' + product.rank,
    dailySalesQuantity: product.total_quantity,
    price: product.total_sales / Math.max(product.total_quantity, 1), // Calculate average price
    sales: product.total_quantity,
    enabled: true,
    multiLangName: { 'zh-CN': product.product_name, 'en-US': product.product_name },
    languages: ['en', 'zh'],
    onHand: 0
  };
}

// Handle product click
function handleProductClick(product: ProductRankingData) {
  const gameData = convertToGameData(product);
  emit('game-click', gameData);
}

// Load data when component mounts
onMounted(() => {
  loadProductRankingData();
});

// Watch for selected organization changes and reload data
watch(() => selectedOrganizationId?.value, () => {
  loadProductRankingData();
}, { immediate: false });

// Watch for date range changes and reload data
watch(() => selectedDateRange?.value, () => {
  loadProductRankingData();
}, { immediate: false });

// Watch for refresh trigger changes and reload data
watch(() => refreshTrigger?.value, () => {
  loadProductRankingData();
}, { immediate: false });
</script>

<template>
  <div class="space-y-4">
    <div class="flex items-center gap-2">
      <SvgIcon icon="mdi:chart-bar" class="text-20px text-primary" />
      <h3 class="text-16px font-semibold">{{ $t('page.home.topSalesQuantityRanking') }}</h3>
    </div>

    <div class="space-y-2">
      <!-- Loading skeleton -->
      <div v-if="loading" class="space-y-2">
        <div v-for="i in 10" :key="`skeleton-${i}`" class="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
          <NSkeleton circle :width="32" :height="32" />
          <NSkeleton :width="40" :height="48" />
          <div class="flex-1 space-y-2">
            <NSkeleton text :width="120" />
            <NSkeleton text :width="80" />
          </div>
          <div class="text-right space-y-2">
            <NSkeleton text :width="80" />
            <NSkeleton text :width="60" />
          </div>
          <NSkeleton circle :width="16" :height="16" />
        </div>
      </div>

      <!-- Actual data -->
      <div
        v-else
        v-for="(product, index) in rankingData"
        :key="product.store_variant_id"
        class="flex items-center gap-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors cursor-pointer"
        @click="handleProductClick(product)"
      >
        <!-- 排名 -->
        <div class="flex items-center justify-center w-8 h-8">
          <SvgIcon
            :icon="getRankIcon(product.rank)"
            class="text-20px"
            :style="{ color: getRankColor(product.rank) }"
          />
        </div>

        <!-- 产品封面 -->
        <img
          :src="product.product_package || 'https://picsum.photos/200/300?random=' + product.rank"
          :alt="product.product_name"
          class="w-10 h-12 rounded object-cover shadow-sm"
        />

        <!-- 产品信息 -->
        <div class="flex-1 min-w-0">
          <div class="font-medium text-gray-900 dark:text-white truncate hover:text-primary transition-colors">
            {{ product.product_name }}
          </div>
          <div class="text-sm text-gray-500 font-mono">{{ product.product_slug.toUpperCase() }}</div>
        </div>

        <!-- 销量 -->
        <div class="text-right">
          <div class="font-semibold text-primary">{{ product.total_quantity.toLocaleString() }}</div>
          <div class="text-sm text-gray-500">{{ product.total_sales_formatted }}</div>
        </div>

        <!-- 查看详情图标 -->
        <div class="flex items-center">
          <SvgIcon icon="mdi:chevron-right" class="text-16px text-gray-400" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
