<script setup lang="ts">
import { computed, ref, onMounted, inject, watch, type Ref } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { useAuthStore } from '@/store/modules/auth';
import { fetchCurrentMonthSalesByCountry } from '@/service/api/reports';

defineOptions({
  name: 'CardData'
});

interface CardData {
  key: string;
  title: string;
  value: number;
  unit: string;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

const authStore = useAuthStore();

// Reactive data for card values
const gameCount = ref(0);
const totalSalesAmount = ref(0);
const orderCount = ref(0);
const reportCount = ref(0);
const loading = ref(true);

// Get selected organization ID from parent component
const selectedOrganizationId = inject<Ref<number | null>>('selectedOrganizationId', ref(null));

const cardData = computed<CardData[]>(() => [
  {
    key: 'gameCount',
    title: $t('page.home.gameCount'),
    value: gameCount.value,
    unit: '',
    color: {
      start: '#865ec0',
      end: '#5144b4'
    },
    icon: 'mdi:gamepad-variant'
  },
  {
    key: 'totalSalesAmount',
    title: $t('page.home.totalSalesAmount'),
    value: totalSalesAmount.value,
    unit: '$',
    color: {
      start: '#ec4786',
      end: '#b955a4'
    },
    icon: 'mdi:currency-usd'
  },
  {
    key: 'orderCount',
    title: $t('page.home.orderCount'),
    value: orderCount.value,
    unit: '',
    color: {
      start: '#56cdf3',
      end: '#719de3'
    },
    icon: 'ant-design:shopping-cart-outlined'
  },
  {
    key: 'reportCount',
    title: $t('page.home.reportCount'),
    value: reportCount.value,
    unit: '',
    color: {
      start: '#fcbc25',
      end: '#f68057'
    },
    icon: 'ant-design:file-text-outlined'
  }
]);

// Load card data from API
async function loadCardData() {
  // Don't load data if no organization is selected
  if (!selectedOrganizationId?.value) {
    loading.value = false;
    return;
  }

  try {
    loading.value = true;

    // Fetch current month sales data to get summary statistics
    const { data: salesResponse, error } = await fetchCurrentMonthSalesByCountry(selectedOrganizationId.value);

    if (!error && salesResponse?.data) {
      const summary = salesResponse.data.summary;

      // Check if summary exists before accessing its properties
      if (summary) {
        // Update card values from API response
        totalSalesAmount.value = Math.round(summary.total_sales || 0);
        orderCount.value = summary.total_orders || 0;
      } else {
        console.warn('No summary data available for card data');
        totalSalesAmount.value = 0;
        orderCount.value = 0;
      }

      // For now, set placeholder values for game count and report count
      // These would need separate API endpoints to get actual data
      // TODO: Integrate with games API when available
      gameCount.value = 156; // Placeholder - would need games API
      reportCount.value = 89; // Placeholder - would need reports count API
    }
  } catch (error) {
    console.error('Failed to load card data:', error);

    // Set fallback values on error
    gameCount.value = 0;
    totalSalesAmount.value = 0;
    orderCount.value = 0;
    reportCount.value = 0;
  } finally {
    loading.value = false;
  }
}

// Load data when component mounts or selected organization changes
onMounted(() => {
  loadCardData();
});

// Watch for organization selection changes
watch(() => selectedOrganizationId?.value, () => {
  loadCardData();
}, { immediate: false });



interface GradientBgProps {
  gradientColor: string;
}

const [DefineGradientBg, GradientBg] = createReusableTemplate<GradientBgProps>();

function getGradientColor(color: CardData['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="rd-8px px-16px pb-4px pt-8px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->

    <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="16" :y-gap="16">
      <NGi v-for="item in cardData" :key="item.key">
        <GradientBg :gradient-color="getGradientColor(item.color)" class="flex-1">
          <h3 class="text-16px">{{ item.title }}</h3>
          <div class="flex justify-between pt-12px">
            <SvgIcon :icon="item.icon" class="text-32px" />
            <div class="text-30px text-white dark:text-dark">
              <NSkeleton v-if="loading" text :width="80" />
              <CountTo
                v-else
                :prefix="item.key === 'totalSalesAmount' ? item.unit : ''"
                :suffix="item.key !== 'totalSalesAmount' ? item.unit : ''"
                :start-value="0"
                :end-value="item.value"
              />
            </div>
          </div>
        </GradientBg>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped></style>
