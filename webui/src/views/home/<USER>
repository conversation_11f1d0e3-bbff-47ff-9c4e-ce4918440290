<script setup lang="ts">
import { computed, ref, provide, onMounted, watch, nextTick } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { usePermission } from '@/hooks/common/permission';
import dayjs from 'dayjs';
import GameStatsDrawer from '@/views/game/modules/game-stats-drawer.vue';
import HeaderBanner from './modules/header-banner.vue';
import TimeControlBar from './modules/time-control-bar.vue';
import CardData from './modules/card-data.vue';
import DailySalesAmountChart from './modules/daily-sales-amount-chart.vue';
import DailySalesQuantityChart from './modules/daily-sales-quantity-chart.vue';
import RegionalSalesAmountChart from './modules/regional-sales-amount-chart.vue';
import RegionalSalesQuantityChart from './modules/regional-sales-quantity-chart.vue';
import TopSalesAmountRanking from './modules/top-sales-amount-ranking.vue';
import TopSalesQuantityRanking from './modules/top-sales-quantity-ranking.vue';

const appStore = useAppStore();
const { userOrganizations } = usePermission();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// Organization selector state management
const selectedOrganizationId = ref<number | null>(null);

// Time range state management
const selectedDateRange = ref<[number, number]>([
  dayjs().subtract(1, 'month').valueOf(),
  dayjs().valueOf()
]);

// Provide selected organization ID and date range to child components
provide('selectedOrganizationId', selectedOrganizationId);
provide('selectedDateRange', selectedDateRange);

// Game data interface (compatible with GameStatsDrawer)
interface GameData {
  id: string;
  product_id?: number; // Optional for backward compatibility
  organisation_id?: number; // Optional for backward compatibility
  coverImage: string;
  name: string;
  code: string;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
  enabled: boolean;
  price: number;
  sales: number;
}

// Game statistics drawer related
const gameStatsDrawerVisible = ref(false);
const selectedGameForStats = ref<GameData | null>(null);



// Initialize default organization selection
function initializeDefaultOrganization() {
  // Get user's organizations
  const userOrgs = userOrganizations.value;
  const { isSystemAdmin } = usePermission();

  console.log('initializeDefaultOrganization called:', {
    userOrgs,
    currentSelected: selectedOrganizationId.value,
    userOrgsLength: userOrgs.length,
    isSystemAdmin: isSystemAdmin()
  });

  if (userOrgs.length > 0 && !selectedOrganizationId.value) {
    // For regular users, select the first organization from their list
    selectedOrganizationId.value = userOrgs[0].id;
    console.log('Default organization selected from user orgs:', userOrgs[0].id, userOrgs[0].name);
  } else if (userOrgs.length === 0 && isSystemAdmin()) {
    console.log('System admin with no user organizations - will wait for OrganizationSelector to load');
    // For system admin, we need to wait for OrganizationSelector to load all organizations
    // This will be handled by a callback from the OrganizationSelector component
  } else if (userOrgs.length === 0) {
    console.log('No user organizations available for regular user');
  } else if (selectedOrganizationId.value) {
    console.log('Organization already selected:', selectedOrganizationId.value);
  }
}

// Handle date range change from TimeControlBar
function handleDateRangeChange(dateRange: [number, number]) {
  selectedDateRange.value = dateRange;
  console.log('Home: Date range updated:', {
    range: dateRange,
    startDate: dayjs(dateRange[0]).format('YYYY-MM-DD'),
    endDate: dayjs(dateRange[1]).format('YYYY-MM-DD')
  });
}



// Handle game click events
function handleGameClick(game: GameData) {
  selectedGameForStats.value = game;
  gameStatsDrawerVisible.value = true;
}



// Watch for user organizations changes and initialize default selection
watch(
  () => userOrganizations.value,
  async (newOrgs, oldOrgs) => {
    console.log('userOrganizations changed:', { newOrgs, oldOrgs, currentSelected: selectedOrganizationId.value });
    if (newOrgs.length > 0 && !selectedOrganizationId.value) {
      // Wait for next tick to ensure OrganizationSelector is ready
      await nextTick();
      // Add a small delay to ensure the selector has loaded its options
      setTimeout(() => {
        initializeDefaultOrganization();
      }, 100);
    }
  },
  { immediate: true }
);

// Initialize component
onMounted(() => {
  console.log('Home component mounted, userOrganizations:', userOrganizations.value);
  // Try to initialize immediately in case data is already available
  initializeDefaultOrganization();
});
</script>

<template>
  <div>
    <NSpace vertical :size="16">
      <HeaderBanner />
      <TimeControlBar @update:date-range="handleDateRangeChange" />


      <CardData />
      <!-- 2x2 layout for the four charts -->
      <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <!-- First row: Daily sales amount line chart -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <DailySalesAmountChart />
          </NCard>
        </NGi>
        <!-- First row: Daily sales quantity line chart -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <DailySalesQuantityChart />
          </NCard>
        </NGi>
        <!-- Second row: Sales amount by region pie chart -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <RegionalSalesAmountChart />
          </NCard>
        </NGi>
        <!-- Second row: Sales quantity by region pie chart -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <RegionalSalesQuantityChart />
          </NCard>
        </NGi>
      </NGrid>

      <!-- TOP10 Ranking -->
      <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <!-- Daily Sales Amount TOP10 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <TopSalesAmountRanking @game-click="handleGameClick" />
          </NCard>
        </NGi>
        <!-- Daily Sales Quantity TOP10 -->
        <NGi span="24 s:24 m:12">
          <NCard :bordered="false" class="card-wrapper">
            <TopSalesQuantityRanking @game-click="handleGameClick" />
          </NCard>
        </NGi>
      </NGrid>

      <!-- Message section hidden -->
      <!--
   <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <NGi span="24 s:24 m:14">
          <SystemMessages />
        </NGi>
        <NGi span="24 s:24 m:10">
          <Notifications />
        </NGi>
      </NGrid>
  -->
    </NSpace>

    <!-- Game statistics drawer -->
    <GameStatsDrawer v-model:visible="gameStatsDrawerVisible" :game="selectedGameForStats" />
  </div>
</template>

<style scoped></style>
