<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import { NCard, NDatePicker, NGrid, NGridItem, NSelect, NSpin } from 'naive-ui';

import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { GraphicComponent } from 'echarts/components';
import * as echarts from 'echarts/core';

// Register GraphicComponent for empty data display
echarts.use([GraphicComponent]);
import type { ECOption } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useReports } from '@/composables/use-reports';
import { fetchSalesReportEnhanced, fetchVolumeReportEnhanced, fetchProductRankingEnhanced } from '@/service/api/reports';
import { usePermission } from '@/hooks/common/permission';
import RegionSelector from '@/components/custom/region-selector.vue';

defineOptions({
  name: 'GameReportContent'
});

interface ProductData {
  id: number;
  name: string;
  slug: string;
  organisation: {
    id: number;
    name: string;
    code: string;
  };
}

interface Props {
  product: ProductData | null;
}

const props = defineProps<Props>();

const appStore = useAppStore();
const { isSystemAdmin } = usePermission();

const isMobile = computed(() => appStore.isMobile);

// Use reports composable
const {
  isLoading,
  formatDateRangeForApi,
  handleReportError
} = useReports();

// Since we're working with specific products, we don't need fallback organization logic

// Time range selection, default to last 30 days
const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

// API data storage
const salesData = ref<Api.Reports.SalesReportResponse | null>(null);
const volumeData = ref<Api.Reports.VolumeReportResponse | null>(null);
const productRankingData = ref<Api.Reports.ProductRankingReportResponse | null>(null);

// Filter options
const filterOptions = reactive({
  countries: [] as string[], // Array of ISO 2-letter country codes
  currency: 'all'
});

// Currency options
const currencyOptions = computed(() => [
  { label: $t('page.game.statistics.allCurrencies'), value: 'all' },
  { label: 'USD', value: 'usd' },
  { label: 'EUR', value: 'eur' },
  { label: 'JPY', value: 'jpy' },
  { label: 'CNY', value: 'cny' }
]);

// Computed property: Extract chart data from API data
const regionVolumeData = computed(() => {
  if (!volumeData.value?.regional_sales_quantity_chart?.series?.[0]?.data) {
    return [];
  }
  return volumeData.value.regional_sales_quantity_chart.series[0].data;
});

const regionAmountData = computed(() => {
  if (!salesData.value?.regional_sales_amount_chart?.series?.[0]?.data) {
    return [];
  }
  return salesData.value.regional_sales_amount_chart.series[0].data;
});

// Daily quantity data from volume API
const dailyQuantityData = computed(() => {
  if (!volumeData.value?.daily_quantity_chart) {
    return {
      xAxis: { data: [] },
      series: [{ data: [] }]
    };
  }
  return volumeData.value.daily_quantity_chart;
});

// Currency data (API may not support grouping by currency, showing empty data)
const currencyVolumeData = computed(() => {
  return [];
});

const currencyAmountData = computed(() => {
  return [];
});

// Hourly sales data (API does not support grouping by hour, showing empty data)
const hourlySalesData = computed(() => {
  return {
    xAxis: [],
    data: []
  };
});

// Statistical data computed property
const gameStatistics = computed(() => {
  // Since the API response does not contain a summary field, we calculate totals from chart data
  const dailySalesChart = salesData.value?.daily_sales_chart;
  const dailyQuantityChart = volumeData.value?.daily_quantity_chart;

  // Calculate total sales amount (sum from daily sales data)
  const totalSalesAmount = dailySalesChart?.series?.[0]?.data?.reduce((sum: number, value: number) => sum + value, 0) || 0;

  // Calculate total sales volume (sum from daily quantity data)
  const totalSalesVolume = dailyQuantityChart?.series?.[0]?.data?.reduce((sum: number, value: number) => sum + value, 0) || 0;

  return {
    totalSalesAmount,
    totalSalesVolume,
    refundCount: 0, // TODO: Need refund API data
    refundRate: 0 // TODO: Need refund API data
  };
});

// Product ranking information computed property
const productRankingInfo = computed(() => {
  if (!productRankingData.value || !props.product) {
    return {
      salesRank: null,
      quantityRank: null,
      totalProducts: 0
    };
  }

  const productId = props.product.id;

  // Find current product in sales ranking
  const salesRankItem = productRankingData.value.sales_rankings.find(
    item => item.store_variant_id === productId
  );

  // Find current product in quantity ranking
  const quantityRankItem = productRankingData.value.quantity_rankings.find(
    item => item.store_variant_id === productId
  );

  return {
    salesRank: salesRankItem?.rank || null,
    quantityRank: quantityRankItem?.rank || null,
    totalProducts: productRankingData.value.summary?.total_products || 0,
    salesRankItem,
    quantityRankItem
  };
});



// Debounce timer for fetchProductReportData
let fetchDataTimer: NodeJS.Timeout | null = null;

/**
 * Get sales data related to the product (debounced)
 */
function debouncedFetchProductReportData() {
  // Clear existing timer
  if (fetchDataTimer) {
    clearTimeout(fetchDataTimer);
  }

  // Set new timer
  fetchDataTimer = setTimeout(() => {
    fetchProductReportData();
  }, 200); // 200ms debounce to prevent multiple rapid calls
}

/**
 * Get sales data related to the product
 */
async function fetchProductReportData() {
  if (!props.product) {
    return;
  }

  try {
    const dateParams = formatDateRangeForApi(dateRange.value);

    // Get product_id from product data
    const productId = props.product.id;

    // For product-specific reports, we only need product_id, not organisation_id
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      product_id: productId, // Only product_id is needed for product-specific data
      group_by: 'day'
    };

    // Add currency parameter only if not 'all'
    if (filterOptions.currency !== 'all') {
      params.currency = filterOptions.currency;
    }

    // Add countries parameter if any countries are selected
    if (filterOptions.countries.length > 0) {
      params.countries = filterOptions.countries;
    }

    // Product ranking params (for getting ranking position)
    // For ranking, we need organisation_id to get the ranking within the organization
    const rankingParams: Api.Reports.ProductRankingFilterParams = {
      ...dateParams,
      organisation_id: props.product.organisation.id, // Use product's organization ID for ranking
      limit: 100 // Get more products to find current product's ranking
    };

    // Add currency parameter only if not 'all'
    if (filterOptions.currency !== 'all') {
      rankingParams.currency = filterOptions.currency;
    }

    // Add countries parameter if any countries are selected
    if (filterOptions.countries.length > 0) {
      rankingParams.countries = filterOptions.countries;
    }

    const [salesResponse, volumeResponse, rankingResponse] = await Promise.all([
      fetchSalesReportEnhanced(params, {
        onLoading: (loading) => { isLoading.value = loading; }
      }),
      fetchVolumeReportEnhanced(params),
      fetchProductRankingEnhanced(rankingParams)
    ]);

    // Process responses
    if (salesResponse.data?.data?.success) {
      salesData.value = salesResponse.data.data.data as Api.Reports.SalesReportResponse;
    }

    if (volumeResponse.data?.data?.success) {
      volumeData.value = volumeResponse.data.data.data as Api.Reports.VolumeReportResponse;
    }

    if (rankingResponse.data?.data?.success) {
      productRankingData.value = rankingResponse.data.data.data as Api.Reports.ProductRankingReportResponse;
    }

    // Delay chart updates to ensure containers have correct dimensions
    setTimeout(() => {
      updateChartsWithData();
    }, 100);

  } catch (error) {
    console.error('fetchProductReportData: Error occurred:', error);
    handleReportError(error, 'Failed to load product statistics');
  }
}

// Force update all charts with current data
function updateChartsWithData() {
  // Force update all charts to ensure they render with correct data and dimensions
  updateRegionVolumeChart((_, factory) => factory());
  updateCurrencyVolumeChart((_, factory) => factory());
  updateRegionAmountChart((_, factory) => factory());
  updateCurrencyAmountChart((_, factory) => factory());
  updateDailyChart((_, factory) => factory());
  updateHourlyChart((_, factory) => factory());
}

// Watch for product changes and refetch data
watch(
  () => props.product,
  (newProduct) => {
    if (newProduct) {
      debouncedFetchProductReportData();
    }
  },
  { deep: true }
);

// Watch for date range and filter option changes
watch(
  () => [dateRange.value, filterOptions.countries, filterOptions.currency],
  () => {
    if (props.product) {
      debouncedFetchProductReportData();
    }
  },
  { deep: true }
);

// Watch for language changes and regenerate data to update chart text
watch(
  () => appStore.locale,
  () => {
    updateChartsLocale();
  }
);



// Regional order volume pie chart configuration
const regionVolumePieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.regionVolumeDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: ${params.value} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: regionVolumeData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: regionVolumeData.value
      }
    ]
  })
);

// Currency order volume pie chart configuration
const currencyVolumePieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.currencyVolumeDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: ${params.value} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: currencyVolumeData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: currencyVolumeData.value
      }
    ]
  })
);

// Regional sales amount pie chart configuration
const regionAmountPieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.regionAmountDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.value.toLocaleString()} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: regionAmountData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesAmount'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: regionAmountData.value
      }
    ]
  })
);

// Currency sales amount pie chart configuration
const currencyAmountPieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.currencyAmountDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: $${params.value.toLocaleString()} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: currencyAmountData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesAmount'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: currencyAmountData.value
      }
    ]
  })
);

// Daily quantity bar chart configuration (using volume data)
const dailyBarOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.dailySales'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    graphic: dailyQuantityData.value.xAxis.data.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    xAxis: [
      {
        type: 'category',
        data: dailyQuantityData.value.xAxis.data || [],
        axisTick: {
          alignWithLabel: true
        },
        name: $t('page.game.statistics.date'),
        nameLocation: 'middle',
        nameGap: 30
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('page.game.statistics.quantity'),
        nameLocation: 'middle',
        nameGap: 50
      }
    ],
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        },
        data: dailyQuantityData.value.series?.[0]?.data || []
      }
    ]
  })
);

// Hourly line chart configuration
const hourlyLineOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.hourlySales'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    graphic: hourlySalesData.value.data.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: hourlySalesData.value.xAxis,
      name: $t('page.game.statistics.hour'),
      nameLocation: 'middle',
      nameGap: 30
    },
    yAxis: {
      type: 'value',
      name: $t('page.game.statistics.quantity'),
      nameLocation: 'middle',
      nameGap: 50
    },
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          opacity: 0.3
        },
        data: hourlySalesData.value.data
      }
    ]
  })
);

// Chart hooks
const { domRef: regionVolumeChartRef, updateOptions: updateRegionVolumeChart } = useEcharts(
  () => regionVolumePieOption.value
);
const { domRef: currencyVolumeChartRef, updateOptions: updateCurrencyVolumeChart } = useEcharts(
  () => currencyVolumePieOption.value
);
const { domRef: regionAmountChartRef, updateOptions: updateRegionAmountChart } = useEcharts(
  () => regionAmountPieOption.value
);
const { domRef: currencyAmountChartRef, updateOptions: updateCurrencyAmountChart } = useEcharts(
  () => currencyAmountPieOption.value
);
const { domRef: dailyChartRef, updateOptions: updateDailyChart } = useEcharts(() => dailyBarOption.value);
const { domRef: hourlyChartRef, updateOptions: updateHourlyChart } = useEcharts(() => hourlyLineOption.value);

// Update chart language configuration
function updateChartsLocale() {
  // Use the same function to ensure consistency
  updateChartsWithData();
}

// Initialize on mount
onMounted(() => {
  if (props.product) {
    debouncedFetchProductReportData();
  }
});
</script>

<template>
  <div v-if="product" class="space-y-6">
    <!-- Product basic information -->
    <NCard :bordered="false" class="from-blue-50 to-indigo-50 bg-gradient-to-r dark:from-gray-800 dark:to-gray-700">
      <div class="flex items-center gap-4">
        <div class="flex-1">
          <h3 class="text-lg text-gray-900 font-bold dark:text-white">{{ product.name }}</h3>
          <p class="text-sm text-gray-600 font-mono dark:text-gray-300">{{ product.slug }}</p>
          <div class="mt-2 flex items-center gap-4">
            <span class="text-sm text-gray-500">
              {{ $t('page.game.reports.productInfo') }}
            </span>
          </div>
        </div>
      </div>
    </NCard>

    <!-- Filter conditions -->
    <NCard :bordered="false" class="bg-gray-50 dark:bg-gray-800">
      <div class="space-y-4">
        <h4 class="text-md flex items-center gap-2 text-gray-900 font-semibold dark:text-white">
          <SvgIcon icon="mdi:filter" class="text-primary" />
          {{ $t('page.game.statistics.filterConditions') }}
        </h4>

        <div :class="isMobile ? 'space-y-3' : 'grid grid-cols-2 lg:grid-cols-3 gap-3'">
          <div class="flex flex-col gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.game.statistics.timeRange') }}
            </span>
            <NDatePicker
              v-model:value="dateRange"
              type="daterange"
              size="small"
              clearable
              @update:value="debouncedFetchProductReportData"
            />
          </div>

          <div class="flex flex-col gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.game.statistics.countries') }}
            </span>
            <RegionSelector
              v-model:value="filterOptions.countries"
              multiple
              size="small"
              @update:value="debouncedFetchProductReportData"
            />
          </div>

          <div class="flex flex-col gap-1">
            <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
              {{ $t('page.game.statistics.currency') }}
            </span>
            <NSelect
              v-model:value="filterOptions.currency"
              :options="currencyOptions"
              size="small"
              @update:value="debouncedFetchProductReportData"
            />
          </div>
        </div>
      </div>
    </NCard>

    <!-- Statistical data overview -->
    <NCard :bordered="false" class="bg-white dark:bg-gray-800">
      <div class="space-y-4">
        <h4 class="text-md flex items-center gap-2 text-gray-900 font-semibold dark:text-white">
          <SvgIcon icon="mdi:chart-line" class="text-primary" />
          {{ $t('page.game.statistics.overview') }}
        </h4>

        <NGrid :cols="isMobile ? 2 : 3" :x-gap="16" :y-gap="16">
          <!-- Sales amount -->
          <NGridItem>
            <div
              class="h-24 flex items-center border-l-4 border-blue-500 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20"
            >
              <div class="w-full flex items-center justify-between">
                <div class="min-w-0 flex-1">
                  <p class="truncate text-sm text-blue-600 font-medium dark:text-blue-400">
                    {{ $t('page.game.statistics.gameTotalSalesAmount') }}
                  </p>
                  <p class="mt-1 text-2xl text-blue-900 font-bold dark:text-blue-100">
                    <NSpin v-if="isLoading" size="small" />
                    <span v-else>${{ gameStatistics.totalSalesAmount.toLocaleString() }}</span>
                  </p>
                </div>
                <SvgIcon icon="mdi:currency-usd" class="ml-3 flex-shrink-0 text-3xl text-blue-500" />
              </div>
            </div>
          </NGridItem>

          <!-- Sales volume -->
          <NGridItem>
            <div
              class="h-24 flex items-center border-l-4 border-green-500 rounded-lg bg-green-50 p-4 dark:bg-green-900/20"
            >
              <div class="w-full flex items-center justify-between">
                <div class="min-w-0 flex-1">
                  <p class="truncate text-sm text-green-600 font-medium dark:text-green-400">
                    {{ $t('page.game.statistics.gameTotalSalesVolume') }}
                  </p>
                  <p class="mt-1 text-2xl text-green-900 font-bold dark:text-green-100">
                    {{ gameStatistics.totalSalesVolume.toLocaleString() }}
                  </p>
                </div>
                <SvgIcon icon="mdi:cart" class="ml-3 flex-shrink-0 text-3xl text-green-500" />
              </div>
            </div>
          </NGridItem>

          <!-- Sales ranking -->
          <NGridItem>
            <div
              class="h-24 flex items-center border-l-4 border-purple-500 rounded-lg bg-purple-50 p-4 dark:bg-purple-900/20"
            >
              <div class="w-full flex items-center justify-between">
                <div class="min-w-0 flex-1">
                  <p class="truncate text-sm text-purple-600 font-medium dark:text-purple-400">
                    {{ $t('page.game.statistics.salesRanking') }}
                  </p>
                  <p class="mt-1 text-2xl text-purple-900 font-bold dark:text-purple-100">
                    <NSpin v-if="isLoading" size="small" />
                    <span v-else-if="productRankingInfo.salesRank">
                      #{{ productRankingInfo.salesRank }}
                      <span class="text-sm text-purple-600 font-normal">
                        / {{ productRankingInfo.totalProducts }}
                      </span>
                    </span>
                    <span v-else class="text-sm text-purple-600">{{ $t('page.game.statistics.noRanking') }}</span>
                  </p>
                </div>
                <SvgIcon icon="mdi:trophy" class="ml-3 flex-shrink-0 text-3xl text-purple-500" />
              </div>
            </div>
          </NGridItem>

          <!-- Volume ranking -->
          <NGridItem>
            <div
              class="h-24 flex items-center border-l-4 border-indigo-500 rounded-lg bg-indigo-50 p-4 dark:bg-indigo-900/20"
            >
              <div class="w-full flex items-center justify-between">
                <div class="min-w-0 flex-1">
                  <p class="truncate text-sm text-indigo-600 font-medium dark:text-indigo-400">
                    {{ $t('page.game.statistics.quantityRanking') }}
                  </p>
                  <p class="mt-1 text-2xl text-indigo-900 font-bold dark:text-indigo-100">
                    <NSpin v-if="isLoading" size="small" />
                    <span v-else-if="productRankingInfo.quantityRank">
                      #{{ productRankingInfo.quantityRank }}
                      <span class="text-sm text-indigo-600 font-normal">
                        / {{ productRankingInfo.totalProducts }}
                      </span>
                    </span>
                    <span v-else class="text-sm text-indigo-600">{{ $t('page.game.statistics.noRanking') }}</span>
                  </p>
                </div>
                <SvgIcon icon="mdi:medal" class="ml-3 flex-shrink-0 text-3xl text-indigo-500" />
              </div>
            </div>
          </NGridItem>
        </NGrid>
      </div>
    </NCard>

    <!-- Chart area -->
    <div class="space-y-6">
      <!-- Pie chart row -->
      <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
        <NGridItem>
          <NCard :bordered="false"  class="h-80">
            <div ref="regionVolumeChartRef" class="h-full w-full"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard :bordered="false" class="h-80">
            <div ref="currencyVolumeChartRef" class="h-full w-full"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard :bordered="false" class="h-80">
            <div ref="regionAmountChartRef" class="h-full w-full"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard :bordered="false" class="h-80">
            <div ref="currencyAmountChartRef" class="h-full w-full"></div>
          </NCard>
        </NGridItem>
      </NGrid>

      <!-- Bar chart and line chart -->
      <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
        <NGridItem>
          <NCard :bordered="false" class="h-96">
            <div ref="dailyChartRef" class="h-full w-full"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard :bordered="false" class="h-96">
            <div ref="hourlyChartRef" class="h-full w-full"></div>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>
  </div>

  <div v-else class="h-64 flex items-center justify-center">
    <div class="text-center text-gray-500">
      <SvgIcon icon="mdi:chart-box-outline" class="mb-4 text-6xl" />
      <p>{{ $t('page.game.reports.noProductSelected') }}</p>
    </div>
  </div>
</template>

<style scoped>
/* Custom styles */
</style>
