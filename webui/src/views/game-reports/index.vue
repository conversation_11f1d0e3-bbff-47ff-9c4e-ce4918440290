<script setup lang="tsx">
import { computed, onMounted, ref } from 'vue';
import { NCard, NSelect, NSpin, NEmpty } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import GameReportContent from './modules/game-report-content.vue';
import { fetchUserAccessibleProducts } from '@/service/api/product-permission';

defineOptions({
  name: 'GameReports'
});

const appStore = useAppStore();

// Loading states
const isLoadingProducts = ref(false);
const isInitializing = ref(true);

// Product data
const accessibleProducts = ref<Api.ProductPermission.AccessibleProduct[]>([]);
const selectedProductId = ref<number | null>(null);

// Computed properties
const selectedProduct = computed(() => {
  if (!selectedProductId.value) return null;
  return accessibleProducts.value.find(p => p.id === selectedProductId.value) || null;
});

const productOptions = computed(() => {
  return accessibleProducts.value.map(product => ({
    label: `${product.name} (${product.organisation.name})`,
    value: product.id,
    product
  }));
});

const hasProducts = computed(() => accessibleProducts.value.length > 0);

/**
 * Fetch user's accessible products
 */
async function fetchAccessibleProducts() {
  isLoadingProducts.value = true;
  try {
    const { data } = await fetchUserAccessibleProducts();

    if (data?.success && data.data?.products) {
      accessibleProducts.value = data.data.products;

      // Auto-select first product if available
      if (accessibleProducts.value.length > 0) {
        selectedProductId.value = accessibleProducts.value[0].id;
      }
    } else {
      console.warn('Failed to fetch accessible products:', data);
      accessibleProducts.value = [];
    }
  } catch (error) {
    console.error('Error fetching accessible products:', error);
    accessibleProducts.value = [];
  } finally {
    isLoadingProducts.value = false;
    isInitializing.value = false;
  }
}

/**
 * Handle product selection change
 */
function handleProductChange(productId: number | null) {
  selectedProductId.value = productId;
}

// Initialize on mount
onMounted(() => {
  fetchAccessibleProducts();
});
</script>

<template>
  <div class="relative">
    <!-- Sticky Product Selection Bar -->
    <div class="sticky top-0 z-50 mb-6">
      <div class="bg-white/95 backdrop-blur-sm border-b border-gray-200 dark:bg-gray-900/95 dark:border-gray-700 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex items-center justify-between h-16">
            <!-- Page Title -->
            <div class="flex items-center gap-3">
              <SvgIcon icon="mdi:chart-box-outline" class="text-primary text-xl" />
              <h1 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ $t('page.game.reports.title') }}
              </h1>
            </div>

            <!-- Product Selection -->
            <div class="flex items-center gap-4">
              <!-- Product Info Display (when selected) -->
              <div v-if="selectedProduct" class="hidden sm:flex items-center gap-2 px-3 py-1.5 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                <SvgIcon icon="mdi:package-variant" class="text-blue-600 dark:text-blue-400 text-sm" />
                <div class="text-sm">
                  <span class="font-medium text-blue-900 dark:text-blue-100">{{ selectedProduct.name }}</span>
                  <span class="text-blue-600 dark:text-blue-400 ml-2">
                    ({{ selectedProduct.organisation.name }})
                  </span>
                </div>
              </div>

              <!-- Product Selector -->
              <div class="flex items-center gap-2">
                <label class="text-sm text-gray-600 font-medium dark:text-gray-300 whitespace-nowrap">
                  {{ $t('page.game.reports.selectProduct') }}:
                </label>

                <div class="w-64">
                  <NSpin v-if="isLoadingProducts" size="small">
                    <NSelect
                      :placeholder="$t('page.game.reports.loadingProducts')"
                      disabled
                      size="small"
                      class="w-full"
                    />
                  </NSpin>

                  <NSelect
                    v-else-if="hasProducts"
                    v-model:value="selectedProductId"
                    :options="productOptions"
                    :placeholder="$t('page.game.reports.selectProductPlaceholder')"
                    clearable
                    filterable
                    size="small"
                    class="w-full"
                    @update:value="handleProductChange"
                  />

                  <NSelect
                    v-else
                    :placeholder="$t('page.game.reports.noProductsAvailable')"
                    disabled
                    size="small"
                    class="w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Product Info (when selected) -->
          <div v-if="selectedProduct" class="sm:hidden pb-3 border-t border-gray-200 dark:border-gray-700 pt-3">
            <div class="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <SvgIcon icon="mdi:package-variant" class="text-blue-600 dark:text-blue-400 text-sm" />
              <div class="text-sm">
                <div class="font-medium text-blue-900 dark:text-blue-100">{{ selectedProduct.name }}</div>
                <div class="text-blue-600 dark:text-blue-400">
                  {{ selectedProduct.organisation.name }} ({{ selectedProduct.organisation.code }})
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Loading State -->
      <div v-if="isInitializing" class="flex justify-center py-12">
        <NSpin size="large">
          <div class="text-center">
            <p class="mt-4 text-gray-500">{{ $t('page.game.reports.loadingProducts') }}</p>
          </div>
        </NSpin>
      </div>

      <!-- No Products Available -->
      <div v-else-if="!hasProducts" class="py-12">
        <NEmpty
          :description="$t('page.game.reports.noProductsAvailable')"
          size="large"
        >
          <template #icon>
            <SvgIcon icon="mdi:package-variant-closed" class="text-6xl text-gray-400" />
          </template>
        </NEmpty>
      </div>

      <!-- No Product Selected -->
      <div v-else-if="!selectedProduct" class="py-12">
        <NEmpty
          :description="$t('page.game.reports.noProductSelected')"
          size="large"
        >
          <template #icon>
            <SvgIcon icon="mdi:chart-box-outline" class="text-6xl text-gray-400" />
          </template>
        </NEmpty>
      </div>

      <!-- Report Content Component -->
      <GameReportContent
        v-else
        :product="selectedProduct"
      />
    </div>
  </div>
</template>

<style scoped>
/* Sticky header styles */
.sticky {
  position: sticky;
  top: 0;
  z-index: 50;
}

/* Add some spacing for the content below the sticky header */
.mb-6 {
  margin-bottom: 1.5rem;
}

/* Ensure the sticky header has a background and shadow */
.bg-white\/95 {
  background-color: rgba(255, 255, 255, 0.95);
}

.dark .dark\:bg-gray-900\/95 {
  background-color: rgba(17, 24, 39, 0.95);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
</style>
