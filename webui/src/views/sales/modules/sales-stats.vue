<script setup lang="tsx">
import { computed, ref, onMounted, watch } from 'vue';
import { $t } from '@/locales';
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';
import { useReports } from '@/composables/use-reports';
import { fetchSalesReportEnhanced } from '@/service/api/reports';
import { formatCurrency, formatNumber } from '@/utils/chart-helpers';

defineOptions({
  name: 'SalesStats'
});

// Props for date range
interface Props {
  dateRange?: [number, number];
  currency?: string;
  organizationId?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  dateRange: () => [Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()],
  currency: 'all',
  organizationId: null
});

// Use reports composable
const {
  isLoading,
  defaultOrganizationId,
  formatDateRangeForApi,
  handleReportError,
  getLastNDaysRange,
  getCurrentMonthRange
} = useReports();

// Sales data from API
const salesData = ref<Api.Reports.SalesReportResponse | null>(null);
const todaySalesData = ref<Api.Reports.SalesReportResponse | null>(null);
const monthSalesData = ref<Api.Reports.SalesReportResponse | null>(null);

// Computed stats values
const totalSales = computed(() => {
  if (!salesData.value?.summary) return 0;
  return salesData.value.summary.total_amount || 0;
});

const totalOrders = computed(() => {
  if (!salesData.value?.summary) return 0;
  return salesData.value.summary.total_orders || 0;
});

const todaySales = computed(() => {
  if (!todaySalesData.value?.summary) return 0;
  return todaySalesData.value.summary.total_amount || 0;
});

const monthSales = computed(() => {
  if (!monthSalesData.value?.summary) return 0;
  return monthSalesData.value.summary.total_amount || 0;
});

/**
 * Fetch sales data for the specified date range
 */
async function fetchSalesData() {
  // Use props.organizationId if provided, otherwise fall back to defaultOrganizationId
  const organizationId = props.organizationId || defaultOrganizationId.value;

  if (!organizationId) {
    console.warn('No organization ID available for sales stats');
    return;
  }

  try {
    const dateParams = formatDateRangeForApi(props.dateRange);
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: organizationId,
      group_by: 'day'
    };

    // Only add currency parameter if not 'all'
    if (props.currency !== 'all') {
      params.currency = props.currency;
    }

    const { data, error } = await fetchSalesReportEnhanced(params, {
      onLoading: (loading) => { isLoading.value = loading; }
    });

    if (error) {
      handleReportError(error, $t('page.reports.salesDataLoadFailed'));
      return;
    }

    if (data?.data?.success) {
      salesData.value = data.data.data;
    }
  } catch (error) {
    handleReportError(error, $t('page.reports.salesDataLoadFailed'));
  }
}

/**
 * Fetch today's sales data
 */
async function fetchTodaySalesData() {
  // Use props.organizationId if provided, otherwise fall back to defaultOrganizationId
  const organizationId = props.organizationId || defaultOrganizationId.value;

  if (!organizationId) return;

  try {
    const todayRange = getLastNDaysRange(1);
    const dateParams = formatDateRangeForApi(todayRange);
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: organizationId,
      group_by: 'day'
    };

    // Only add currency parameter if not 'all'
    if (props.currency !== 'all') {
      params.currency = props.currency;
    }

    const { data, error } = await fetchSalesReportEnhanced(params);

    if (error) {
      console.warn('Failed to fetch today sales data:', error);
      return;
    }

    if (data?.data?.success) {
      todaySalesData.value = data.data.data;
    }
  } catch (error) {
    console.warn('Failed to fetch today sales data:', error);
  }
}

/**
 * Fetch current month's sales data
 */
async function fetchMonthSalesData() {
  // Use props.organizationId if provided, otherwise fall back to defaultOrganizationId
  const organizationId = props.organizationId || defaultOrganizationId.value;

  if (!organizationId) return;

  try {
    const monthRange = getCurrentMonthRange();
    const dateParams = formatDateRangeForApi(monthRange);
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: organizationId,
      group_by: 'day'
    };

    // Only add currency parameter if not 'all'
    if (props.currency !== 'all') {
      params.currency = props.currency;
    }

    const { data, error } = await fetchSalesReportEnhanced(params);

    if (error) {
      console.warn('Failed to fetch month sales data:', error);
      return;
    }

    if (data?.data?.success) {
      monthSalesData.value = data.data.data;
    }
  } catch (error) {
    console.warn('Failed to fetch month sales data:', error);
  }
}

/**
 * Initialize all data
 */
async function initData() {
  await Promise.all([
    fetchSalesData(),
    fetchTodaySalesData(),
    fetchMonthSalesData()
  ]);
}

// Watch for prop changes
watch(
  () => [props.dateRange, props.currency, props.organizationId],
  () => {
    initData();
  },
  { deep: true }
);

// Initialize on mount
onMounted(() => {
  initData();
});
</script>

<template>
  <StatsGrid>
    <StatsCard
      :title="$t('page.sales.totalSales')"
      :value="totalSales"
      :unit="props.currency"
      :unit-as-prefix="true"
      :color="{ start: '#007aff', end: '#0056cc' }"
      icon="mdi:currency-usd"
    />
    <StatsCard
      :title="$t('page.sales.totalOrders')"
      :value="totalOrders"
      :unit="$t('common.unit.orders')"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      icon="mdi:receipt"
    />
    <StatsCard
      :title="$t('page.sales.todaySales')"
      :value="todaySales"
      :unit="props.currency"
      :unit-as-prefix="true"
      :color="{ start: '#722ed1', end: '#531dab' }"
      icon="mdi:trending-up"
    />
    <StatsCard
      :title="$t('page.sales.monthSales')"
      :value="monthSales"
      :unit="props.currency"
      :unit-as-prefix="true"
      :color="{ start: '#fa8c16', end: '#d46b08' }"
      icon="mdi:calendar-month"
    />
  </StatsGrid>
</template>
