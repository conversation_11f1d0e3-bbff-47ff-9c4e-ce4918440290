<script setup lang="ts">
import { computed, reactive, ref, watch, onMounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useReports } from '@/composables/use-reports';
import { fetchSalesReportEnhanced } from '@/service/api/reports';
import { transformSalesChartData, chartColorSchemes } from '@/utils/chart-helpers';
import { useMessage } from 'naive-ui';

defineOptions({
  name: 'SalesCharts'
});

const appStore = useAppStore();
const message = useMessage();
const isMobile = computed(() => appStore.isMobile);

// Use reports composable
const {
  isLoading,
  defaultOrganizationId,
  formatDateRangeForApi,
  handleReportError,
  validateOrganizationAccess
} = useReports();

const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

const chartOptions = reactive({
  region: 'all',
  currency: 'USD',
  orderStatus: 'all'
});

// Chart data from API
const salesChartData = ref<Api.Reports.SalesReportResponse | null>(null);

// Unified color scheme, consistent with the above statistic cards
const colorScheme = chartColorSchemes;

// Computed data from API response
const regionData = computed(() => {
  if (!salesChartData.value?.regional_sales_amount_chart?.series?.[0]?.data) {
    return [];
  }
  return salesChartData.value.regional_sales_amount_chart.series[0].data.map((item, index) => ({
    name: item.name,
    value: item.value,
    color: [colorScheme.primary, colorScheme.success, colorScheme.purple, colorScheme.orange][index % 4]
  }));
});

const dailySalesData = computed(() => {
  if (!salesChartData.value?.daily_sales_chart) {
    return { xAxis: [], series: [] };
  }
  return salesChartData.value.daily_sales_chart;
});

const dualAxisData = computed(() => {
  if (!salesChartData.value?.dual_axis_chart) {
    return { xAxis: [], series: [] };
  }
  return salesChartData.value.dual_axis_chart;
});

/**
 * Fetch sales data from API
 */
async function fetchSalesData() {
  if (!defaultOrganizationId.value) {
    message.error($t('page.reports.noOrganization'));
    return;
  }

  try {
    const dateParams = formatDateRangeForApi(dateRange.value);
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: defaultOrganizationId.value,
      group_by: 'day'
    };

    // Only add currency parameter if not 'all'
    if (chartOptions.currency !== 'all') {
      params.currency = chartOptions.currency;
    }

    // Add country filter if region is selected
    if (chartOptions.region !== 'all') {
      const regionMap: Record<string, string[]> = {
        na: ['US', 'CA'],
        eu: ['GB', 'DE', 'FR', 'IT', 'ES'],
        asia: ['CN', 'JP', 'KR', 'IN']
      };
      params.countries = regionMap[chartOptions.region] || [];
    }

    const { data, error } = await fetchSalesReportEnhanced(params, {
      onLoading: (loading) => { isLoading.value = loading; }
    });

    if (error) {
      handleReportError(error, $t('page.reports.salesDataLoadFailed'));
      return;
    }

    if (data?.data?.success) {
      salesChartData.value = data.data.data;
      updateCharts();
    }
  } catch (error) {
    handleReportError(error, $t('page.reports.salesDataLoadFailed'));
  }
}

function handleDateRangeChange() {
  if (dateRange.value && dateRange.value.length === 2) {
    fetchSalesData();
  }
}

function handleExportData() {
  // TODO: Implement data export logic
  message.info($t('page.reports.exportNotImplemented'));
}

// Region distribution pie chart
const { domRef: regionChartRef, updateOptions: updateRegionChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: (params: any) => {
      return `${params.marker} ${params.name}: ${params.value} (${params.percent}%)`;
    }
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.regionDistribution'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// Dual axis chart (sales amount and order count)
const { domRef: dualAxisChartRef, updateOptions: updateDualAxisChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    },
    formatter: (params: any) => {
      let tooltip = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`;
      params.forEach((param: any) => {
        const value = param.seriesName.includes('金额') || param.seriesName.includes('Amount')
          ? `$${param.value.toLocaleString()}`
          : param.value.toLocaleString();
        tooltip += `<div>${param.marker} ${param.seriesName}: ${value}</div>`;
      });
      return tooltip;
    }
  },
  legend: {
    top: '5%',
    data: [$t('page.sales.salesAmount'), $t('page.sales.orderCount')]
  },
  xAxis: {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow'
    }
  },
  yAxis: [
    {
      type: 'value',
      name: $t('page.sales.salesAmount'),
      position: 'left',
      axisLabel: {
        formatter: '${value}'
      }
    },
    {
      type: 'value',
      name: $t('page.sales.orderCount'),
      position: 'right',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: []
}));

// Daily sales trend chart
const { domRef: dailySalesTrendChartRef, updateOptions: updateDailySalesTrendChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      return `${param.marker} ${param.axisValue}: $${param.value.toLocaleString()}`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: []
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '${value}'
    }
  },
  series: [
    {
      name: $t('page.sales.dailySales'),
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: colorScheme.primary
            },
            {
              offset: 1,
              color: 'rgba(42, 74, 103, 0.1)'
            }
          ]
        }
      },
      itemStyle: {
        color: colorScheme.primary
      },
      data: []
    }
  ]
}));

/**
 * Update all charts with API data
 */
function updateCharts() {
  if (!salesChartData.value) return;

  // Update region distribution chart
  updateRegionChart(opts => {
    if (regionData.value.length > 0) {
      opts.series[0].data = regionData.value.map(item => ({
        name: item.name,
        value: item.value
      }));
      (opts as any).color = regionData.value.map(item => item.color);
    }
    return opts;
  });

  // Update dual axis chart (sales amount and order count)
  updateDualAxisChart(opts => {
    const chartData = dualAxisData.value;
    if (chartData.xAxis && chartData.series) {
      opts.xAxis.data = chartData.xAxis.data;
      opts.series = chartData.series.map((series: any, index: number) => ({
        ...series,
        type: index === 0 ? 'bar' : 'line',
        yAxisIndex: index,
        itemStyle: {
          color: index === 0 ? colorScheme.primary : colorScheme.success
        }
      }));
    }
    return opts;
  });

  // Update daily sales trend chart
  updateDailySalesTrendChart(opts => {
    const chartData = dailySalesData.value;
    if (chartData.xAxis && chartData.series) {
      opts.xAxis.data = chartData.xAxis.data;
      opts.series[0].data = chartData.series[0].data;
    }
    return opts;
  });
}

/**
 * Initialize charts with data
 */
async function initCharts() {
  await fetchSalesData();
}

/**
 * Update locale for charts
 */
function updateLocale() {
  updateRegionChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });

  updateDualAxisChart((opts, factory) => {
    const originOpts = factory();
    opts.legend.data = originOpts.legend.data;
    opts.yAxis[0].name = originOpts.yAxis[0].name;
    opts.yAxis[1].name = originOpts.yAxis[1].name;
    return opts;
  });

  updateDailySalesTrendChart((opts, factory) => {
    const originOpts = factory();
    opts.series[0].name = originOpts.series[0].name;
    return opts;
  });
}

// Watch for locale changes
watch(
  () => appStore.locale,
  () => {
    updateLocale();
  }
);

// Watch for chart options changes
watch(
  () => [chartOptions.region, chartOptions.currency, chartOptions.orderStatus],
  () => {
    fetchSalesData();
  },
  { deep: true }
);

// Initialize on mount
onMounted(() => {
  initCharts();
});
</script>

<template>
  <div class="space-y-6">
    <!-- Compact filter -->
    <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <!-- Search fields and buttons -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="flex flex-col gap-1 min-w-64 flex-1">
          <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.timeRange') }}</span>
          <NDatePicker
            v-model:value="dateRange"
            type="daterange"
            size="small"
            clearable
            @update:value="handleDateRangeChange"
          />
        </div>

        <div class="flex flex-col gap-1 min-w-32 flex-1">
          <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.region') }}</span>
          <NSelect
            v-model:value="chartOptions.region"
            :placeholder="$t('page.sales.selectRegion')"
            :options="[
              { label: $t('page.sales.allRegions'), value: 'all' },
              { label: $t('page.sales.northAmerica'), value: 'na' },
              { label: $t('page.sales.europe'), value: 'eu' },
              { label: $t('page.sales.asia'), value: 'asia' }
            ]"
            size="small"
          />
        </div>

        <div class="flex flex-col gap-1 min-w-28 flex-1">
          <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.currency') }}</span>
          <NSelect
            v-model:value="chartOptions.currency"
            :placeholder="$t('page.sales.selectCurrency')"
            :options="[
              { label: 'USD', value: 'USD' },
              { label: 'EUR', value: 'EUR' },
              { label: 'CNY', value: 'CNY' }
            ]"
            size="small"
          />
        </div>

        <!-- Action buttons -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton type="primary" size="small" :loading="isLoading" @click="fetchSalesData">
            <template #icon>
              <SvgIcon icon="mdi:refresh" />
            </template>
            {{ $t('page.sales.refresh') }}
          </NButton>
          <NButton size="small" @click="handleExportData">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.sales.exportData') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- Chart area -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <!-- Regional sales distribution -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionDistribution') }}</span>
          </template>
          <NSpin :show="isLoading">
            <div ref="regionChartRef" class="h-350px overflow-hidden"></div>
          </NSpin>
        </NCard>
      </NGi>

      <!-- Sales amount and order count comparison -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.salesVsOrders') }}</span>
          </template>
          <NSpin :show="isLoading">
            <div ref="dualAxisChartRef" class="h-350px overflow-hidden"></div>
          </NSpin>
        </NCard>
      </NGi>
    </NGrid>

    <!-- Daily sales trend chart -->
    <NCard :bordered="false">
      <template #header>
        <span class="text-16px font-semibold">{{ $t('page.sales.dailySalesTrend') }}</span>
      </template>
      <NSpin :show="isLoading">
        <div ref="dailySalesTrendChartRef" class="h-300px overflow-hidden"></div>
      </NSpin>
    </NCard>
  </div>
</template>

<style scoped></style>
