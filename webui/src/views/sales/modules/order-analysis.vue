<script setup lang="ts">
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import { useReports } from '@/composables/use-reports';
import { fetchOrderStatusReportEnhanced } from '@/service/api/reports';
import { useMessage } from 'naive-ui';

defineOptions({
  name: 'OrderAnalysis'
});

// Props to receive filter options from parent
interface FilterOptions {
  dateRange: [number, number];
  currency: string;
  region: string;
  countries: string[]; // Add countries array
  period: string;
  orderStatus: string;
  organizationId: number | null;
}

const props = defineProps<{
  filterOptions: FilterOptions;
  refreshTrigger?: number;
}>();

const message = useMessage();

// Use reports composable
const {
  isLoading,
  defaultOrganizationId,
  formatDateRangeForApi,
  handleReportError
} = useReports();



// Order status data from API
const orderStatusData = ref<Api.Reports.OrderStatusReportResponse | null>(null);



// Computed data from API response
const orderStatusChartData = computed(() => {
  if (!orderStatusData.value?.order_status_chart?.data) return [];
  return orderStatusData.value.order_status_chart.data;
});

const paymentStatusChartData = computed(() => {
  if (!orderStatusData.value?.payment_status_chart?.data) return [];
  return orderStatusData.value.payment_status_chart.data;
});

/**
 * Fetch order status data from API
 */
async function fetchOrderStatusData() {
  const organizationId = props.filterOptions.organizationId || defaultOrganizationId.value;
  if (!organizationId) {
    message.error('No organization selected');
    return;
  }

  try {
    const dateParams = formatDateRangeForApi(props.filterOptions.dateRange);
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: organizationId,
      group_by: 'day'
    };

    // Only add currency parameter if not 'all'
    if (props.filterOptions.currency !== 'all') {
      params.currency = props.filterOptions.currency;
    }

    // Add country filter if countries are selected
    if (props.filterOptions.countries && props.filterOptions.countries.length > 0) {
      params.countries = props.filterOptions.countries;
    }

    const { data, error } = await fetchOrderStatusReportEnhanced(params, {
      onLoading: (loading) => { isLoading.value = loading; }
    });

    if (error) {
      handleReportError(error, 'Failed to load order status data');
      return;
    }

    if (data?.data?.success) {
      orderStatusData.value = data.data.data as Api.Reports.OrderStatusReportResponse;
      updateCharts();
    }
  } catch (error) {
    handleReportError(error, 'Failed to load order status data');
  }
}





// 订单状态分布饼图
const { domRef: orderStatusChartRef, updateOptions: updateOrderStatusChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: (params: any) => {
      return `${params.marker} ${params.name}: ${params.value} (${params.percent}%)`;
    }
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.orderStatus'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 支付状态分布饼图
const { domRef: paymentStatusChartRef, updateOptions: updatePaymentStatusChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: (params: any) => {
      return `${params.marker} ${params.name}: ${params.value} (${params.percent}%)`;
    }
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '支付状态',
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

/**
 * Update all charts with API data
 */
function updateCharts() {
  if (!orderStatusData.value) return;

  // Add a small delay to ensure DOM is ready
  setTimeout(() => {
    // Update order status chart
    updateOrderStatusChart(opts => {
      const chartData = orderStatusChartData.value;
      if (chartData.length > 0) {
        opts.series[0].data = chartData.map((item: Api.Reports.OrderStatusChartDataItem) => ({
          name: item.name,
          value: item.value
        }));
        (opts as any).color = chartData.map((item: Api.Reports.OrderStatusChartDataItem) => item.color);
      }
      return opts;
    });

    // Update payment status chart
    updatePaymentStatusChart(opts => {
      const chartData = paymentStatusChartData.value;
      if (chartData.length > 0) {
        opts.series[0].data = chartData.map((item: Api.Reports.PaymentStatusChartDataItem) => ({
          name: item.name,
          value: item.value
        }));
        (opts as any).color = chartData.map((item: Api.Reports.PaymentStatusChartDataItem) => item.color);
      }
      return opts;
    });
  }, 200);
}

/**
 * Initialize charts with data
 */
async function initCharts() {
  await fetchOrderStatusData();
}





// Watch for filter options changes
watch(() => props.filterOptions, () => {
  fetchOrderStatusData();
}, { deep: true });

// Watch for refresh trigger changes
watch(() => props.refreshTrigger, () => {
  if (props.refreshTrigger !== undefined) {
    fetchOrderStatusData();
  }
});

// Initialize on mount
onMounted(async () => {
  // Wait for next tick to ensure DOM is fully rendered
  await nextTick();

  // Add a small delay to ensure tab transition is complete
  setTimeout(() => {
    initCharts();
  }, 100);
});
</script>

<template>
  <div class="space-y-6">

    <!-- 图表区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <!-- 订单状态分布 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.orderStatus') }}</span>
          </template>
          <NSpin :show="isLoading">
            <div ref="orderStatusChartRef" class="h-350px w-full overflow-hidden"></div>
          </NSpin>
        </NCard>
      </NGi>

      <!-- 支付状态分布 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.paymentStatus') }}</span>
          </template>
          <NSpin :show="isLoading">
            <div ref="paymentStatusChartRef" class="h-350px w-full overflow-hidden"></div>
          </NSpin>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
