<script setup lang="ts">
import { computed, ref, provide, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import TopSalesAmountRanking from '@/views/home/<USER>/top-sales-amount-ranking.vue';
import TopSalesQuantityRanking from '@/views/home/<USER>/top-sales-quantity-ranking.vue';

defineOptions({
  name: 'TopRanking'
});

// Props to receive filter options from parent
interface FilterOptions {
  dateRange: [number, number];
  currency: string;
  region: string;
  countries: string[]; // Add countries array
  period: string;
  orderStatus: string;
  organizationId: number | null;
}

const props = defineProps<{
  filterOptions: FilterOptions;
  refreshTrigger?: number;
}>();

// Define emits for game click events
const emit = defineEmits<{
  'game-click': [gameData: any];
}>();

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// Provide filter options to child components
const selectedOrganizationId = ref<number | null>(null);
const selectedDateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

// Update provided values when props change
watch(() => props.filterOptions.organizationId, (newId) => {
  selectedOrganizationId.value = newId;
}, { immediate: true });

watch(() => props.filterOptions.dateRange, (newRange) => {
  selectedDateRange.value = newRange;
}, { immediate: true });

// Refresh trigger for child components
const refreshTrigger = ref(0);
watch(() => props.refreshTrigger, () => {
  if (props.refreshTrigger !== undefined) {
    refreshTrigger.value++;
  }
});

// Provide values to child components (same as home page)
provide('selectedOrganizationId', selectedOrganizationId);
provide('selectedDateRange', selectedDateRange);
provide('refreshTrigger', refreshTrigger);

// Handle game click events from child components
function handleGameClick(gameData: any) {
  // Emit the game click event to parent component
  emit('game-click', gameData);
}
</script>

<template>
  <div class="space-y-6">
    <!-- 标题栏 -->
    <div class="flex justify-between items-center">
      <h3 class="text-lg font-semibold">{{ $t('page.sales.productRanking') }}</h3>
    </div>

    <!-- TOP10排行榜 -->
    <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
      <!-- 销售金额排名 -->
      <NGi span="24 s:24 m:12">
        <NCard :bordered="false" class="card-wrapper">
          <TopSalesAmountRanking @game-click="handleGameClick" />
        </NCard>
      </NGi>

      <!-- 销售数量排名 -->
      <NGi span="24 s:24 m:12">
        <NCard :bordered="false" class="card-wrapper">
          <TopSalesQuantityRanking @game-click="handleGameClick" />
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
