<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import { useReports } from '@/composables/use-reports';
import { fetchRefundsReportEnhanced } from '@/service/api/reports';
import { transformRefundChartData, chartColorSchemes } from '@/utils/chart-helpers';
import { useMessage } from 'naive-ui';

defineOptions({
  name: 'RefundAnalysis'
});

// Props to receive filter options from parent
interface FilterOptions {
  dateRange: [number, number];
  currency: string;
  region: string;
  countries: string[]; // Add countries array
  period: string;
  orderStatus: string;
  organizationId: number | null;
}

const props = defineProps<{
  filterOptions: FilterOptions;
  refreshTrigger?: number;
}>();

const message = useMessage();

// Use reports composable
const {
  isLoading,
  defaultOrganizationId,
  formatDateRangeForApi,
  handleReportError
} = useReports();

// Refund data from API
const refundData = ref<Api.Reports.RefundReportResponse | null>(null);

// Unified color scheme
const colorScheme = chartColorSchemes;

// Computed data from API response
const refundTrendData = computed(() => {
  if (!refundData.value) return { xAxis: { data: [] }, series: [] };
  const transformedData = transformRefundChartData(refundData.value);
  return transformedData?.refundTrendChart || { xAxis: { data: [] }, series: [] };
});

const refundReasonsData = computed(() => {
  if (!refundData.value) return { data: [] };
  const transformedData = transformRefundChartData(refundData.value);
  return transformedData?.refundReasonsChart || { data: [] };
});

// Computed summary data for cards
const summaryData = computed(() => {
  if (!refundData.value?.summary) return null;
  return refundData.value.summary;
});

/**
 * Fetch refund data from API
 */
async function fetchRefundData() {
  const organizationId = props.filterOptions.organizationId || defaultOrganizationId.value;
  if (!organizationId) {
    message.error('No organization selected');
    return;
  }

  try {
    const dateParams = formatDateRangeForApi(props.filterOptions.dateRange);
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: organizationId,
      group_by: 'day'
    };

    // Only add currency parameter if not 'all'
    if (props.filterOptions.currency !== 'all') {
      params.currency = props.filterOptions.currency;
    }

    // Add country filter if countries are selected
    if (props.filterOptions.countries && props.filterOptions.countries.length > 0) {
      params.countries = props.filterOptions.countries;
    }

    const { data, error } = await fetchRefundsReportEnhanced(params, {
      onLoading: (loading) => { isLoading.value = loading; }
    });

    if (error) {
      handleReportError(error, 'Failed to load refund data');
      return;
    }

    if (data?.data?.success) {
      const responseData = data.data.data as Api.Reports.RefundReportResponse;
      // Validate the response data structure
      if (responseData && typeof responseData === 'object') {
        refundData.value = responseData;
        updateCharts();
      } else {
        console.warn('Invalid refund data structure received:', responseData);
        message.warning('Received invalid data format from server');
      }
    }
  } catch (error) {
    handleReportError(error, 'Failed to load refund data');
  }
}



function handleExportData() {
  message.info('Export functionality not implemented yet');
}

// Refund reasons pie chart
const { domRef: refundReasonsChartRef, updateOptions: updateRefundReasonsChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    data: []
  },
  series: [
    {
      name: 'Refund Reason',
      type: 'pie',
      radius: '50%',
      center: ['50%', '60%'],
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}));

// Refund trend chart (amount and count)
const { domRef: refundTrendChartRef, updateOptions: updateRefundTrendChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    },
    formatter: (params: any) => {
      let tooltip = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`;
      params.forEach((param: any) => {
        const value = param.seriesName.includes('金额') || param.seriesName.includes('Amount')
          ? `$${param.value.toLocaleString()}`
          : param.value.toLocaleString();
        tooltip += `<div>${param.marker} ${param.seriesName}: ${value}</div>`;
      });
      return tooltip;
    }
  },
  legend: {
    top: '5%',
    data: []
  },
  xAxis: {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow'
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '',
      position: 'left',
      axisLabel: {
        formatter: '${value}'
      }
    },
    {
      type: 'value',
      name: '',
      position: 'right',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: []
}));

/**
 * Update all charts with API data
 */
function updateCharts() {
  if (!refundData.value) return;

  // Update refund trend chart
  updateRefundTrendChart(opts => {
    const chartData = refundTrendData.value;

    // Update Y-axis labels with internationalization
    (opts.yAxis as any)[0].name = $t('page.sales.refundAmount' as any);
    (opts.yAxis as any)[1].name = $t('page.sales.refundCount' as any);

    // Update legend with internationalization
    (opts.legend as any).data = [$t('page.sales.refundAmount' as any), $t('page.sales.refundCount' as any)];

    if (chartData.xAxis && chartData.series) {
      (opts.xAxis as any).data = chartData.xAxis.data;
      (opts.series as any) = chartData.series.map((series: any, index: number) => ({
        ...series,
        name: index === 0 ? $t('page.sales.refundAmount' as any) : $t('page.sales.refundCount' as any),
        type: index === 0 ? 'bar' : 'line',
        yAxisIndex: index,
        itemStyle: {
          color: index === 0 ? colorScheme.orange : colorScheme.red
        }
      }));
    }
    return opts;
  });

  // Update refund reasons chart
  updateRefundReasonsChart(opts => {
    const reasonsData = refundReasonsData.value;
    if (reasonsData.data && Array.isArray(reasonsData.data)) {
      (opts.series as any)[0].data = reasonsData.data.map((item: any) => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: item.color || colorScheme.blue
        }
      }));
      (opts.legend as any).data = reasonsData.data.map((item: any) => item.name);
    }
    return opts;
  });
}

/**
 * Initialize charts with data
 */
async function initCharts() {
  await fetchRefundData();
}



// Watch for filter options changes
watch(() => props.filterOptions, () => {
  fetchRefundData();
}, { deep: true });

// Watch for refresh trigger changes
watch(() => props.refreshTrigger, () => {
  if (props.refreshTrigger !== undefined) {
    fetchRefundData();
  }
});

// Initialize on mount
onMounted(() => {
  initCharts();
});
</script>

<template>
  <div class="space-y-6">

    <!-- Refund statistics cards -->
    <div class="grid grid-cols-1 mb-6 gap-4 md:grid-cols-3">
      <!-- Refund rate card -->
      <div
        class="relative overflow-hidden border-l-4 border-red-500 rounded-lg from-red-50 to-red-100 bg-gradient-to-br p-4 dark:from-red-900/20 dark:to-red-800/20"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="mb-3 flex items-center gap-3">
              <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-red-500/10 dark:bg-red-500/20">
                <SvgIcon icon="mdi:undo-variant" class="text-lg text-red-500" />
              </div>
              <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                {{ $t('page.sales.refundRate') }}
              </span>
            </div>
            <div class="flex items-baseline gap-1">
              <span class="text-2xl text-red-600 font-bold dark:text-red-400">
                {{ summaryData?.refund_rate?.toFixed(1) || '0.0' }}
              </span>
              <span class="text-base text-red-500 font-medium dark:text-red-400">%</span>
            </div>
          </div>
          <div class="absolute h-16 w-16 rounded-full bg-red-500/5 -right-3 -top-3 dark:bg-red-500/10"></div>
        </div>
      </div>

      <!-- Refund amount card -->
      <div
        class="relative overflow-hidden border-l-4 border-orange-500 rounded-lg from-orange-50 to-orange-100 bg-gradient-to-br p-4 dark:from-orange-900/20 dark:to-orange-800/20"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="mb-3 flex items-center gap-3">
              <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-orange-500/10 dark:bg-orange-500/20">
                <SvgIcon icon="mdi:currency-usd" class="text-lg text-orange-500" />
              </div>
              <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                {{ $t('page.sales.refundAmount') }}
              </span>
            </div>
            <div class="flex items-baseline gap-1">
              <span class="text-base text-orange-500 font-medium dark:text-orange-400">$</span>
              <span class="text-2xl text-orange-600 font-bold dark:text-orange-400">
                {{ summaryData?.total_refund_amount?.toLocaleString() || '0' }}
              </span>
            </div>
          </div>
          <div class="absolute h-16 w-16 rounded-full bg-orange-500/5 -right-3 -top-3 dark:bg-orange-500/10"></div>
        </div>
      </div>

      <!-- Refund count card -->
      <div
        class="relative overflow-hidden border-l-4 border-purple-500 rounded-lg from-purple-50 to-purple-100 bg-gradient-to-br p-4 dark:from-purple-900/20 dark:to-purple-800/20"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="mb-3 flex items-center gap-3">
              <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-purple-500/10 dark:bg-purple-500/20">
                <SvgIcon icon="mdi:receipt" class="text-lg text-purple-500" />
              </div>
              <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                {{ $t('page.sales.refundCount') }}
              </span>
            </div>
            <div class="flex items-baseline gap-1">
              <span class="text-2xl text-purple-600 font-bold dark:text-purple-400">
                {{ summaryData?.total_refunds?.toLocaleString() || '0' }}
              </span>
              <span class="text-sm text-purple-500 font-medium dark:text-purple-400">
                {{ $t('common.unit.orders') }}
              </span>
            </div>
          </div>
          <div class="absolute h-16 w-16 rounded-full bg-purple-500/5 -right-3 -top-3 dark:bg-purple-500/10"></div>
        </div>
      </div>
    </div>

    <!-- Chart area -->
    <div class="space-y-6">
      <!-- Refund trend chart - full width -->
      <NCard :bordered="false" class="shadow-sm">
        <template #header>
          <div class="flex items-center gap-2">
            <SvgIcon icon="mdi:chart-line" class="text-lg text-blue-500" />
            <span class="text-16px font-semibold">{{ $t('page.sales.refundTrend') }}</span>
          </div>
        </template>
        <NSpin :show="isLoading">
          <div ref="refundTrendChartRef" class="h-400px overflow-hidden"></div>
        </NSpin>
      </NCard>

      <!-- Refund reasons analysis chart -->
      <NCard :bordered="false" class="shadow-sm">
        <template #header>
          <div class="flex items-center gap-2">
            <SvgIcon icon="mdi:chart-pie" class="text-lg text-green-500" />
            <span class="text-16px font-semibold">{{ $t('page.sales.refundReasonsAnalysis') }}</span>
          </div>
        </template>
        <NSpin :show="isLoading">
          <div ref="refundReasonsChartRef" class="h-400px overflow-hidden"></div>
        </NSpin>
      </NCard>
    </div>
  </div>
</template>

<style scoped></style>
