<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'RegionalAnalysis'
});

// Props to receive filter options from parent
interface FilterOptions {
  dateRange: [number, number];
  currency: string;
  region: string;
  countries: string[]; // Add countries array
  period: string;
  orderStatus: string;
  organizationId: number | null;
}

const props = defineProps<{
  filterOptions: FilterOptions;
  refreshTrigger?: number;
}>();

// Unified color scheme
const colorScheme = {
  primary: '#2a4a67',
  success: '#52c41a',
  purple: '#722ed1',
  orange: '#fa8c16',
  red: '#ff4d4f',
  gray: '#8c8c8c'
};

// Simulated sales data by region
const regionalSalesData = computed(() => [
  { region: $t('page.sales.northAmerica'), amount: 125680 },
  { region: $t('page.sales.europe'), amount: 98450 },
  { region: $t('page.sales.asia'), amount: 156720 },
  { region: $t('page.sales.others'), amount: 45890 }
]);

// Simulated sales volume data by region
const regionalVolumeData = computed(() => [
  { region: $t('page.sales.northAmerica'), volume: 2580 },
  { region: $t('page.sales.europe'), volume: 1950 },
  { region: $t('page.sales.asia'), volume: 3120 },
  { region: $t('page.sales.others'), volume: 890 }
]);

// Simulated regional sales growth rate data
const regionalGrowthData = computed(() => [
  { region: $t('page.sales.northAmerica'), growth: 15.2 },
  { region: $t('page.sales.europe'), growth: 8.7 },
  { region: $t('page.sales.asia'), growth: 22.5 },
  { region: $t('page.sales.others'), growth: -3.2 }
]);

// Simulated regional average order value data
const regionalAvgOrderData = computed(() => [
  { region: $t('page.sales.northAmerica'), avgOrder: 48.7 },
  { region: $t('page.sales.europe'), avgOrder: 50.5 },
  { region: $t('page.sales.asia'), value: 50.2 },
  { region: $t('page.sales.others'), value: 51.6 }
]);

function handleDateRangeChange() {
  // TODO: Implement logic for date range change
}

function handleExportData() {
  // TODO: Implement data export logic
}

// Bar chart for sales comparison by region
const { domRef: regionalSalesChartRef, updateOptions: updateRegionalSalesChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: ${c}'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '${value}'
    }
  },
  series: [
    {
      name: $t('page.sales.salesAmount'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.primary
      },
      data: [] as number[]
    }
  ]
}));

// Bar chart for sales volume comparison by region
const { domRef: regionalVolumeChartRef, updateOptions: updateRegionalVolumeChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      return `${param.seriesName} <br/>${param.name}: ${param.value}${$t('common.unit.orders')}`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: any) => `${value}${$t('common.unit.orders')}`
    }
  },
  series: [
    {
      name: $t('page.sales.orderVolume'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.success
      },
      data: [] as number[]
    }
  ]
}));

// Bar chart for regional sales growth rate comparison
const { domRef: regionalGrowthChartRef, updateOptions: updateRegionalGrowthChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: {c}%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: $t('page.sales.growthRate'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: (params: any) => {
          return params.value >= 0 ? colorScheme.success : colorScheme.red;
        }
      },
      data: [] as number[]
    }
  ]
}));

// Radar chart for regional average order value comparison
const { domRef: regionalRadarChartRef, updateOptions: updateRegionalRadarChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  radar: {
    indicator: [
      { name: $t('page.sales.northAmerica'), max: 60 },
      { name: $t('page.sales.europe'), max: 60 },
      { name: $t('page.sales.asia'), max: 60 },
      { name: $t('page.sales.others'), max: 60 }
    ],
    center: ['50%', '50%'],
    radius: '60%'
  },
  series: [
    {
      name: $t('page.sales.regionalAvgOrderValue'),
      type: 'radar',
      data: [
        {
          value: [] as number[],
          name: $t('page.sales.avgOrderValue'),
          itemStyle: {
            color: colorScheme.purple
          },
          areaStyle: {
            color: colorScheme.purple,
            opacity: 0.3
          }
        }
      ]
    }
  ]
}));

// Initialize chart data
async function initCharts() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  // Update sales comparison chart by region
  updateRegionalSalesChart(opts => {
    opts.xAxis.data = regionalSalesData.value.map(item => item.region);
    opts.series[0].data = regionalSalesData.value.map(item => item.amount);
    return opts;
  });

  // Update sales volume comparison chart by region
  updateRegionalVolumeChart(opts => {
    opts.xAxis.data = regionalVolumeData.value.map(item => item.region);
    opts.series[0].data = regionalVolumeData.value.map(item => item.volume);
    return opts;
  });

  // Update regional sales growth rate comparison chart
  updateRegionalGrowthChart(opts => {
    opts.xAxis.data = regionalGrowthData.value.map(item => item.region);
    opts.series[0].data = regionalGrowthData.value.map(item => item.growth);
    return opts;
  });

  // Update regional average order value radar chart
  updateRegionalRadarChart(opts => {
    opts.series[0].data[0].value = [48.7, 50.5, 50.2, 51.6];
    return opts;
  });
}

// Initialization
initCharts();
</script>

<template>
  <div class="space-y-6">

    <!-- Chart area -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <!-- Sales comparison by region -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalSalesComparison') }}</span>
          </template>
          <div ref="regionalSalesChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- Sales volume comparison by region -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalVolumeComparison') }}</span>
          </template>
          <div ref="regionalVolumeChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- Regional sales growth rate comparison -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalGrowthRate') }}</span>
          </template>
          <div ref="regionalGrowthChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- Regional average order value radar chart -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.regionalAvgOrderValue') }}</span>
          </template>
          <div ref="regionalRadarChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
