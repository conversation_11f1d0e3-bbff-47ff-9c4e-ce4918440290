<script setup lang="ts">
import { computed, ref } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'CustomerAnalysis'
});

// Props to receive filter options from parent
interface FilterOptions {
  dateRange: [number, number];
  currency: string;
  region: string;
  countries: string[]; // Add countries array
  period: string;
  orderStatus: string;
  organizationId: number | null;
}

const props = defineProps<{
  filterOptions: FilterOptions;
  refreshTrigger?: number;
}>();

// 统一的配色方案
const colorScheme = {
  primary: '#2a4a67',
  success: '#52c41a',
  purple: '#722ed1',
  orange: '#fa8c16',
  red: '#ff4d4f',
  gray: '#8c8c8c'
};

// 模拟新老用户销售贡献数据
const userTypeData = computed(() => [
  { type: $t('page.sales.newUsers'), amount: 125680, volume: 2580 },
  { type: $t('page.sales.oldUsers'), amount: 298450, volume: 4520 }
]);

// 模拟用户复购率数据
const repurchaseData = computed(() => [
  { month: 'Jan', rate: 25.6 },
  { month: 'Feb', rate: 28.3 },
  { month: 'Mar', rate: 31.2 },
  { month: 'Apr', rate: 29.8 },
  { month: 'May', rate: 33.5 },
  { month: 'Jun', rate: 35.2 },
  { month: 'Jul', rate: 32.8 }
]);

// 模拟客单价分布数据
const orderValueDistribution = computed(() => [
  { range: '0-20', count: 1250 },
  { range: '20-50', count: 2180 },
  { range: '50-100', count: 1680 },
  { range: '100-200', count: 890 },
  { range: '200+', count: 320 }
]);

// 模拟用户地区分布数据
const userRegionData = computed(() => [
  { name: $t('page.sales.northAmerica'), value: 35, color: colorScheme.primary },
  { name: $t('page.sales.europe'), value: 28, color: colorScheme.success },
  { name: $t('page.sales.asia'), value: 25, color: colorScheme.purple },
  { name: $t('page.sales.others'), value: 12, color: colorScheme.orange }
]);

function handleDateRangeChange() {
  // TODO: 实现日期范围变更逻辑
}

function handleExportData() {
  // TODO: 实现数据导出逻辑
}

// 新老用户销售贡献对比图
const { domRef: userTypeChartRef, updateOptions: updateUserTypeChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter(params: any) {
      let result = `${params[0].name}<br/>`;
      params.forEach((param: any) => {
        result += `${param.seriesName}: `;
        if (param.seriesName.includes('Sales') || param.seriesName.includes('销售额')) {
          result += `$${param.value}<br/>`;
        } else {
          result += `${param.value} orders<br/>`;
        }
      });
      return result;
    }
  },
  legend: {
    data: [$t('page.sales.salesAmount'), $t('page.sales.orderVolume')]
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: [
    {
      type: 'value',
      name: $t('page.sales.salesAmount'),
      axisLabel: {
        formatter: '${value}'
      }
    },
    {
      type: 'value',
      name: $t('page.sales.orderVolume'),
      axisLabel: {
        formatter: (value: any) => `${value}${$t('common.unit.orders')}`
      }
    }
  ],
  series: [
    {
      name: $t('page.sales.salesAmount'),
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        color: colorScheme.primary
      },
      data: [] as number[]
    },
    {
      name: $t('page.sales.orderVolume'),
      type: 'bar',
      barWidth: '40%',
      yAxisIndex: 1,
      itemStyle: {
        color: colorScheme.success
      },
      data: [] as number[]
    }
  ]
}));

// 用户复购率趋势图
const { domRef: repurchaseChartRef, updateOptions: updateRepurchaseChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: '{a} <br/>{b}: {c}%'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: $t('page.sales.repurchaseRate'),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: colorScheme.purple
      },
      lineStyle: {
        color: colorScheme.purple
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: colorScheme.purple
            },
            {
              offset: 1,
              color: 'rgba(114, 46, 209, 0.1)'
            }
          ]
        }
      },
      data: [] as number[]
    }
  ]
}));

// 客单价分布直方图
const { domRef: orderValueChartRef, updateOptions: updateOrderValueChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      return `${param.seriesName} <br/>${param.name}: ${param.value}${$t('common.unit.people')}`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: any) => `${value}${$t('common.unit.people')}`
    }
  },
  series: [
    {
      name: $t('page.sales.userCount'),
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: colorScheme.orange
      },
      data: [] as number[]
    }
  ]
}));

// 用户地区分布饼图
const { domRef: userRegionChartRef, updateOptions: updateUserRegionChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}% ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.userRegionDistribution'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 初始化图表数据
async function initCharts() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  // 更新新老用户销售贡献对比图
  updateUserTypeChart(opts => {
    opts.xAxis.data = userTypeData.value.map(item => item.type);
    opts.series[0].data = userTypeData.value.map(item => item.amount);
    opts.series[1].data = userTypeData.value.map(item => item.volume);
    return opts;
  });

  // 更新用户复购率趋势图
  updateRepurchaseChart(opts => {
    opts.xAxis.data = repurchaseData.value.map(item => item.month);
    opts.series[0].data = repurchaseData.value.map(item => item.rate);
    return opts;
  });

  // 更新客单价分布直方图
  updateOrderValueChart(opts => {
    opts.xAxis.data = orderValueDistribution.value.map(item => item.range);
    opts.series[0].data = orderValueDistribution.value.map(item => item.count);
    return opts;
  });

  // 更新用户地区分布饼图
  updateUserRegionChart(opts => {
    opts.series[0].data = userRegionData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    (opts as any).color = userRegionData.value.map(item => item.color);
    return opts;
  });
}





// 初始化
initCharts();
</script>

<template>
  <div class="space-y-6">

    <!-- 图表区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <!-- 新老用户销售贡献对比 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.newVsOldUsers') }}</span>
          </template>
          <div ref="userTypeChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 用户复购率趋势 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.repurchaseRate') }}</span>
          </template>
          <div ref="repurchaseChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 客单价分布 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.orderValueDistribution') }}</span>
          </template>
          <div ref="orderValueChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>

      <!-- 用户地区分布 -->
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <span class="text-16px font-semibold">{{ $t('page.sales.userRegionDistribution') }}</span>
          </template>
          <div ref="userRegionChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
