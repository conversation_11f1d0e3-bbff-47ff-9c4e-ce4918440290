<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useReports } from '@/composables/use-reports';
import { fetchSalesReportEnhanced } from '@/service/api/reports';
import { chartColorSchemes } from '@/utils/chart-helpers';
import { useMessage } from 'naive-ui';

defineOptions({
  name: 'SalesOverview'
});

// Props to receive filter options from parent
interface FilterOptions {
  dateRange: [number, number];
  currency: string;
  region: string;
  countries: string[]; // Add countries array
  period: string;
  orderStatus: string;
  organizationId: number | null;
}

const props = defineProps<{
  filterOptions: FilterOptions;
  refreshTrigger?: number;
}>();

const message = useMessage();

// Use reports composable for API utilities
const {
  isLoading,
  defaultOrganizationId,
  formatDateRangeForApi,
  handleReportError
} = useReports();

// API data storage
const salesChartData = ref<Api.Reports.SalesReportResponse | null>(null);

// 统一的配色方案，与其他图表保持一致
const colorScheme = chartColorSchemes;

// 更柔和的主题色配色方案，贴近项目主题色 #2a4a67
const softColorScheme = {
  primary: '#2a4a67',      // 主题色 - 深蓝灰
  secondary: '#6b9b6b',    // 次要色 - 柔和绿（与主色形成对比）
  accent: '#7ba7cc',       // 强调色 - 浅蓝
  warning: '#d4a574',      // 警告色 - 柔和橙
  info: '#6b8db5',         // 信息色 - 柔和蓝
  muted: '#8a9ba8'         // 静音色 - 灰蓝
};

// 地区分布数据 - 从API获取
const regionData = computed(() => {
  if (!salesChartData.value?.regional_sales_amount_chart?.series?.[0]?.data) {
    return [];
  }
  return salesChartData.value.regional_sales_amount_chart.series[0].data.map((item, index) => ({
    name: item.name,
    value: item.value,
    color: [softColorScheme.primary, softColorScheme.secondary, softColorScheme.warning, softColorScheme.accent][index % 4]
  }));
});

// 货币分布数据 - 暂时保持模拟数据（API暂未提供）
const currencyData = computed(() => [
  { name: 'USD', value: 45, color: softColorScheme.primary },
  { name: 'EUR', value: 25, color: softColorScheme.secondary },
  { name: 'CNY', value: 20, color: softColorScheme.warning },
  { name: $t('page.sales.others'), value: 10, color: softColorScheme.accent }
]);

// 双Y轴图表数据 - 从API获取
const dualAxisChartData = computed(() => {
  if (!salesChartData.value?.dual_axis_chart) {
    return { xAxis: [], amountSeries: [], quantitySeries: [] };
  }

  const chartData = salesChartData.value.dual_axis_chart;
  return {
    xAxis: chartData.xAxis?.data || [],
    amountSeries: chartData.series?.[0]?.data || [],
    quantitySeries: chartData.series?.[1]?.data || []
  };
});



// 模拟分时数据
const hourlyData = computed(() => [
  { time: '00:00', value: 120 },
  { time: '02:00', value: 80 },
  { time: '04:00', value: 60 },
  { time: '06:00', value: 100 },
  { time: '08:00', value: 180 },
  { time: '10:00', value: 220 },
  { time: '12:00', value: 280 },
  { time: '14:00', value: 320 },
  { time: '16:00', value: 290 },
  { time: '18:00', value: 250 },
  { time: '20:00', value: 200 },
  { time: '22:00', value: 150 }
]);

// API数据获取函数
async function fetchSalesData() {
  const organizationId = props.filterOptions.organizationId || defaultOrganizationId.value;
  if (!organizationId) {
    console.warn('No organization ID available');
    return;
  }

  try {
    const dateParams = formatDateRangeForApi(props.filterOptions.dateRange);
    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: organizationId,
      group_by: 'day'
    };

    // Only add currency parameter if not 'all'
    if (props.filterOptions.currency !== 'all') {
      params.currency = props.filterOptions.currency;
    }

    // Add country filter if countries are selected
    if (props.filterOptions.countries && props.filterOptions.countries.length > 0) {
      params.countries = props.filterOptions.countries;
    }

    const { data, error } = await fetchSalesReportEnhanced(params, {
      onLoading: (loading) => { isLoading.value = loading; }
    });

    if (error) {
      handleReportError(error, $t('page.reports.salesDataLoadFailed'));
      return;
    }

    if (data?.data?.data) {
      salesChartData.value = data.data.data;
      updateChartsWithApiData();
    }
  } catch (error) {
    console.error('Failed to fetch sales data:', error);
    message.error($t('page.reports.salesDataLoadFailed'));
  }
}



// 地区分布饼图
const { domRef: regionChartRef, updateOptions: updateRegionChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}% ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.regionDistribution'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 货币分布饼图
const { domRef: currencyChartRef, updateOptions: updateCurrencyChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}% ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemStyle: {
      borderWidth: 0
    },
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: $t('page.sales.currencyDistribution'),
      type: 'pie',
      radius: ['35%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

// 日销售双Y轴柱状图 (金额 + 数量)
const { domRef: dailySalesChartRef, updateOptions: updateDailySalesChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function(params: any) {
      let result = `${params[0].axisValue}<br/>`;
      params.forEach((param: any) => {
        const value = param.yAxisIndex === 0
          ? `$${param.value}`
          : `${param.value}`;
        result += `${param.marker}${param.seriesName}: ${value}<br/>`;
      });
      return result;
    }
  },
  legend: {
    data: [$t('page.sales.salesAmount'), $t('page.sales.orderCount')],
    top: 10
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [] as string[],
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: [
    {
      type: 'value',
      name: $t('page.sales.salesAmount'),
      position: 'left',
      axisLabel: {
        formatter: '${value}'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: softColorScheme.primary
        }
      }
    },
    {
      type: 'value',
      name: $t('page.sales.orderCount'),
      position: 'right',
      axisLabel: {
        formatter: '{value}'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: softColorScheme.secondary
        }
      }
    }
  ],
  series: [
    {
      name: $t('page.sales.salesAmount'),
      type: 'bar',
      yAxisIndex: 0,
      barWidth: '40%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: softColorScheme.primary
            },
            {
              offset: 1,
              color: softColorScheme.accent
            }
          ]
        }
      },
      data: [] as number[]
    },
    {
      name: $t('page.sales.orderCount'),
      type: 'bar',
      yAxisIndex: 1,
      barWidth: '40%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: softColorScheme.secondary
            },
            {
              offset: 1,
              color: '#8bb68b' // 更浅的绿色渐变
            }
          ]
        }
      },
      data: [] as number[]
    }
  ]
}));

// 分时曲线图
const { domRef: hourlyTrendChartRef, updateOptions: updateHourlyTrendChart } = useEcharts(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [] as string[]
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: $t('page.sales.hourlyTrend'),
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: softColorScheme.accent + '80' // 添加透明度
            },
            {
              offset: 1,
              color: softColorScheme.accent + '10' // 更淡的透明度
            }
          ]
        }
      },
      itemStyle: {
        color: softColorScheme.primary
      },
      data: [] as number[]
    }
  ]
}));

// 使用API数据更新图表
function updateChartsWithApiData() {
  // 更新地区分布饼图
  updateRegionChart(opts => {
    opts.series[0].data = regionData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    (opts as any).color = regionData.value.map(item => item.color);
    return opts;
  });

  // 更新货币分布饼图（暂时保持模拟数据）
  updateCurrencyChart(opts => {
    opts.series[0].data = currencyData.value.map(item => ({
      name: item.name,
      value: item.value
    }));
    (opts as any).color = currencyData.value.map(item => item.color);
    return opts;
  });

  // 更新双Y轴日销售图表
  updateDailySalesChart(opts => {
    const chartData = dualAxisChartData.value;
    opts.xAxis.data = chartData.xAxis;
    opts.series[0].data = chartData.amountSeries;
    opts.series[1].data = chartData.quantitySeries;
    return opts;
  });

  // 更新分时曲线图（暂时保持模拟数据）
  updateHourlyTrendChart(opts => {
    opts.xAxis.data = hourlyData.value.map(item => item.time);
    opts.series[0].data = hourlyData.value.map(item => item.value);
    return opts;
  });
}

// 初始化图表数据
async function initCharts() {
  // 首先获取API数据
  await fetchSalesData();
}





// 数据导出功能
function exportRegionData() {
  if (!regionData.value.length) {
    message.warning($t('page.sales.noDataToExport'));
    return;
  }

  const csvContent = [
    ['Region', 'Sales Amount'].join(','),
    ...regionData.value.map(item => [item.name, item.value].join(','))
  ].join('\n');

  downloadCSV(csvContent, 'region-distribution.csv');
}

function exportCurrencyData() {
  if (!currencyData.value.length) {
    message.warning($t('page.sales.noDataToExport'));
    return;
  }

  const csvContent = [
    ['Currency', 'Percentage'].join(','),
    ...currencyData.value.map(item => [item.name, item.value].join(','))
  ].join('\n');

  downloadCSV(csvContent, 'currency-distribution.csv');
}

function exportDailySalesData() {
  const chartData = dualAxisChartData.value;
  if (!chartData.xAxis.length) {
    message.warning($t('page.sales.noDataToExport'));
    return;
  }

  const csvContent = [
    ['Date', 'Sales Amount', 'Order Count'].join(','),
    ...chartData.xAxis.map((date, index) => [
      date,
      chartData.amountSeries[index] || 0,
      chartData.quantitySeries[index] || 0
    ].join(','))
  ].join('\n');

  downloadCSV(csvContent, 'daily-sales-data.csv');
}

function exportHourlyTrendData() {
  // 模拟小时趋势数据导出
  const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);
  const values = Array.from({ length: 24 }, () => Math.floor(Math.random() * 1000));

  const csvContent = [
    ['Hour', 'Sales Amount'].join(','),
    ...hours.map((hour, index) => [hour, values[index]].join(','))
  ].join('\n');

  downloadCSV(csvContent, 'hourly-trend-data.csv');
}

function downloadCSV(content: string, filename: string) {
  const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  message.success($t('page.sales.exportSuccess'));
}

// 监听筛选条件变化
watch(() => props.filterOptions, () => {
  fetchSalesData();
}, { deep: true });

// Watch for refresh trigger changes
watch(() => props.refreshTrigger, () => {
  if (props.refreshTrigger !== undefined) {
    fetchSalesData();
  }
});

// 组件挂载时初始化
onMounted(() => {
  initCharts();
});

// 初始化
initCharts();
</script>

<template>
  <div class="space-y-6">

    <!-- 饼图区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-16px font-semibold">{{ $t('page.sales.regionDistribution') }}</span>
              <NButton size="small" quaternary @click="exportRegionData">
                <template #icon>
                  <SvgIcon icon="mdi:download" class="text-14px" />
                </template>
              </NButton>
            </div>
          </template>
          <div ref="regionChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
      <NGi>
        <NCard :bordered="false" class="h-420px">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-16px font-semibold">{{ $t('page.sales.currencyDistribution') }}</span>
              <NButton size="small" quaternary @click="exportCurrencyData">
                <template #icon>
                  <SvgIcon icon="mdi:download" class="text-14px" />
                </template>
              </NButton>
            </div>
          </template>
          <div ref="currencyChartRef" class="h-350px overflow-hidden"></div>
        </NCard>
      </NGi>
    </NGrid>

    <!-- 日销售双Y轴图表 -->
    <NCard :bordered="false">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-16px font-semibold">{{ $t('page.sales.salesVsOrders') }}</span>
          <NButton size="small" quaternary @click="exportDailySalesData">
            <template #icon>
              <SvgIcon icon="mdi:download" class="text-14px" />
            </template>
          </NButton>
        </div>
      </template>
      <div ref="dailySalesChartRef" class="h-300px overflow-hidden"></div>
    </NCard>

    <!-- 分时曲线图 -->
    <NCard :bordered="false">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-16px font-semibold">{{ $t('page.sales.hourlyTrend') }}</span>
          <NButton size="small" quaternary @click="exportHourlyTrendData">
            <template #icon>
              <SvgIcon icon="mdi:download" class="text-14px" />
            </template>
          </NButton>
        </div>
      </template>
      <div ref="hourlyTrendChartRef" class="h-300px overflow-hidden"></div>
    </NCard>
  </div>
</template>

<style scoped></style>
