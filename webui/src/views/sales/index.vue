<script setup lang="tsx">
import { computed, ref, reactive, watch, onMounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { usePermission } from '@/hooks/common/permission';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import OrganizationSelector from '@/components/common/organization-selector.vue';
import RegionSelector from '@/components/custom/region-selector.vue';
import GameStatsDrawer from '@/views/game/modules/game-stats-drawer.vue';
import SalesStats from './modules/sales-stats.vue';
import SalesOverview from './modules/sales-overview.vue';
import OrderAnalysis from './modules/order-analysis.vue';
import RefundAnalysis from './modules/refund-analysis.vue';
import RegionalAnalysis from './modules/regional-analysis.vue';
import CustomerAnalysis from './modules/customer-analysis.vue';
import TopRanking from './modules/top-ranking.vue';

defineOptions({
  name: 'Sales'
});

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);
const { isSystemAdmin, userOrganizations } = usePermission();



// Unified filter state for all components
const filterOptions = reactive({
  dateRange: [Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()] as [number, number],
  currency: 'all',
  region: 'all', // Keep for backward compatibility with existing components
  countries: [] as string[], // New field for RegionSelector component
  period: 'month',
  orderStatus: 'all',
  organizationId: null as number | null
});

// Refresh trigger - increment this to force refresh all components
const refreshTrigger = ref(0);

// Game stats drawer state
const gameStatsDrawerVisible = ref(false);
const selectedGameForStats = ref<any>(null);

// Game data interface for compatibility
interface GameData {
  id: string;
  product_id?: number;
  organisation_id?: number;
  name: string;
  code: string;
  coverImage: string;
  price: number;
  sales: number;
  enabled: boolean;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
}

// Computed property to get organization IDs for the selector based on user role
const allowedOrganizationIds = computed(() => {
  // If current user is root or admin, allow all organizations (no restriction)
  if (isSystemAdmin()) {
    return undefined;
  }

  // For non-admin users, return their organization IDs
  const orgIds = userOrganizations.value.map(org => org.id);

  // If user has no organizations, return empty array to prevent loading all organizations
  if (orgIds.length === 0) {
    return [];
  }

  return orgIds;
});

// Initialize organization ID for non-admin users
onMounted(() => {
  // For non-admin users, set the first organization as default
  if (!isSystemAdmin() && userOrganizations.value.length > 0) {
    filterOptions.organizationId = userOrganizations.value[0].id;
    console.log('Auto-selected organization for non-admin user:', userOrganizations.value[0].id, userOrganizations.value[0].name);
  }
});

// Watch for organization selection changes
watch(() => filterOptions.organizationId, (newOrgId) => {
  console.log('Organization changed to:', newOrgId);
});

// Watch for countries changes and sync with region field for backward compatibility
watch(
  () => filterOptions.countries,
  (newCountries) => {
    // For backward compatibility, set region to 'all' when no countries selected,
    // or to a representative value when countries are selected
    if (newCountries.length === 0) {
      filterOptions.region = 'all';
    } else {
      // Set to a generic value when countries are selected
      filterOptions.region = 'selected';
    }
  },
  { deep: true }
);

// Disable future dates in date picker
function isDateDisabled(current: number, _phase: 'start' | 'end', _value: [number, number] | null): boolean {
  const today = new Date();
  today.setHours(23, 59, 59, 999); // Set to end of today
  return current > today.getTime();
}

// Validate and adjust date range to ensure it's within reasonable bounds
function validateAndAdjustDateRange(dateRange: [number, number] | null): [number, number] {
  if (!dateRange || dateRange.length !== 2) {
    // Return default range if invalid
    return [Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()];
  }

  let [startDate, endDate] = dateRange;
  const now = Date.now();
  const maxPastDate = now - 365 * 24 * 60 * 60 * 1000; // 1 year ago

  // Ensure start date is not too far in the past (max 1 year)
  if (startDate < maxPastDate) {
    startDate = maxPastDate;
  }

  // Ensure end date is not in the future
  if (endDate > now) {
    endDate = now;
  }

  // Ensure start date is before end date
  if (startDate >= endDate) {
    // If start date is after end date, adjust start date to be 30 days before end date
    startDate = endDate - 30 * 24 * 60 * 60 * 1000;

    // If this makes start date too far in the past, adjust both dates
    if (startDate < maxPastDate) {
      startDate = maxPastDate;
      endDate = startDate + 30 * 24 * 60 * 60 * 1000;

      // Ensure end date doesn't go into future
      if (endDate > now) {
        endDate = now;
      }
    }
  }

  return [startDate, endDate];
}

// Handle date range change with validation
function handleDateRangeChange(value: [number, number] | null) {
  const adjustedRange = validateAndAdjustDateRange(value);

  // Only update if the range actually changed
  if (!value || adjustedRange[0] !== value[0] || adjustedRange[1] !== value[1]) {
    console.log('Date range adjusted:', {
      original: value,
      adjusted: adjustedRange,
      originalDates: value ? [new Date(value[0]), new Date(value[1])] : null,
      adjustedDates: [new Date(adjustedRange[0]), new Date(adjustedRange[1])]
    });

    // Update the filter options with the adjusted range
    filterOptions.dateRange = adjustedRange;
  }
}

// Refresh status
const isRefreshing = ref(false);

// Default filter values
const defaultFilterOptions = {
  dateRange: [Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()] as [number, number],
  currency: 'all',
  region: 'all',
  countries: [] as string[],
  period: 'month',
  orderStatus: 'all',
  organizationId: null as number | null
};

// Tab related status
const activeTab = ref('overview');

// Tab filter configuration - defines which filters are visible for each tab
const tabFilterConfig: Record<string, string[]> = {
  overview: ['organizationId', 'dateRange', 'currency', 'countries'],
  orders: ['organizationId', 'dateRange', 'currency', 'countries'],
  refunds: ['organizationId', 'dateRange', 'currency', 'countries'],
  regional: ['organizationId', 'dateRange', 'currency', 'period'],
  customers: ['organizationId', 'dateRange', 'currency', 'countries'],
  topRanking: ['organizationId', 'dateRange', 'currency']
};

// Computed property to get visible filters for current tab
const visibleFilters = computed(() => {
  return tabFilterConfig[activeTab.value] || [];
});

// Filter options for selectors
const currencyOptions = computed(() => [
  { label: $t('page.sales.allCurrencies'), value: 'all' },
  { label: 'USD', value: 'USD' },
  { label: 'EUR', value: 'EUR' },
  { label: 'GBP', value: 'GBP' },
  { label: 'JPY', value: 'JPY' }
]);

const periodOptions = computed(() => [
  { label: '按月统计', value: 'month' },
  { label: '按季度统计', value: 'quarter' },
  { label: '按年统计', value: 'year' }
]);

// Refresh data
async function handleRefresh() {
  isRefreshing.value = true;
  try {
    // Increment refresh trigger to force all sub-components to refetch data
    refreshTrigger.value++;

    // Wait for a short period to ensure all API calls are completed
    await new Promise(resolve => setTimeout(resolve, 1000));
  } finally {
    isRefreshing.value = false;
  }
}

// Reset filter
function handleReset() {
  // Reset the filter to the default value, but keep the organization selection
  filterOptions.dateRange = [...defaultFilterOptions.dateRange] as [number, number];
  filterOptions.currency = defaultFilterOptions.currency;
  filterOptions.region = defaultFilterOptions.region;
  filterOptions.countries = [...defaultFilterOptions.countries];
  filterOptions.period = defaultFilterOptions.period;
  filterOptions.orderStatus = defaultFilterOptions.orderStatus;
  // Do not reset organizationId, keep the currently selected organization
}

// Handle game click events from child components
function handleGameClick(gameData: GameData) {
  selectedGameForStats.value = gameData;
  gameStatsDrawerVisible.value = true;
}

// Tab options
const tabOptions = computed(() => [
  {
    key: 'overview',
    label: $t('page.sales.tabs.overview'),
    icon: 'mdi:chart-line'
  },
  {
    key: 'orders',
    label: $t('page.sales.tabs.orders'),
    icon: 'mdi:receipt'
  },
  {
    key: 'refunds',
    label: $t('page.sales.tabs.refunds'),
    icon: 'mdi:undo-variant'
  },
  {
    key: 'regional',
    label: $t('page.sales.tabs.regional'),
    icon: 'mdi:earth'
  },
  {
    key: 'customers',
    label: $t('page.sales.tabs.customers'),
    icon: 'mdi:account-group'
  },
  {
    key: 'topRanking',
    label: $t('page.sales.tabs.topRanking'),
    icon: 'mdi:trophy'
  }
]);
</script>

<template>
  <NSpace vertical :size="16">
    <!-- Top statistics card area (keep unchanged) -->
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:chart-line" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('route.sales') }}</span>
        </div>
      </template>
      <SalesStats
        :date-range="filterOptions.dateRange"
        :currency="filterOptions.currency"
        :organization-id="filterOptions.organizationId"
      />
    </NCard>

    <!-- Unified filter toolbar -->
    <NCard :bordered="false" class="card-wrapper">
      <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
          <!-- Organization selector -->
          <div v-if="visibleFilters.includes('organizationId')" class="flex flex-col gap-1 min-w-48 flex-1">
            <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('common.organization') }}</span>
            <OrganizationSelector
              v-model="filterOptions.organizationId"
              :organization-ids="allowedOrganizationIds"
              :show-all-option="isSystemAdmin()"
              :all-option-value="null"
              :all-option-label="$t('common.allOrganizations')"
              size="small"
              clearable
            />
          </div>

          <!-- Date range picker -->
          <div v-if="visibleFilters.includes('dateRange')" class="flex flex-col gap-1 min-w-64 flex-1">
            <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.timeRange') }}</span>
            <NDatePicker
              v-model:value="filterOptions.dateRange"
              type="daterange"
              size="small"
              clearable
              :is-date-disabled="isDateDisabled"
              @update:value="handleDateRangeChange"
            />
          </div>

          <!-- Region selector -->
          <div v-if="visibleFilters.includes('countries')" class="flex flex-col gap-1 min-w-32 flex-1">
            <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.game.statistics.countries') }}</span>
            <RegionSelector
              v-model:value="filterOptions.countries"
              multiple
              size="small"
            />
          </div>

          <!-- Currency selector -->
          <div v-if="visibleFilters.includes('currency')" class="flex flex-col gap-1 min-w-28 flex-1">
            <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.currency') }}</span>
            <NSelect
              v-model:value="filterOptions.currency"
              :placeholder="$t('page.sales.selectCurrency')"
              :options="currencyOptions"
              size="small"
            />
          </div>

          <!-- Statistical period selector -->
          <div v-if="visibleFilters.includes('period')" class="flex flex-col gap-1 min-w-28 flex-1">
            <span class="text-sm text-gray-600 dark:text-gray-300 font-medium">{{ $t('page.sales.statisticalPeriod') }}</span>
            <NSelect
              v-model:value="filterOptions.period"
              :options="periodOptions"
              size="small"
            />
          </div>

          <!-- Action buttons -->
          <div class="flex gap-2" :class="isMobile ? 'w-full justify-center mt-2' : 'flex-shrink-0'">
            <NTooltip trigger="hover">
              <template #trigger>
                <NButton size="small" quaternary circle @click="handleRefresh" :loading="isRefreshing">
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" class="text-16px" />
                  </template>
                </NButton>
              </template>
              {{ $t('common.refresh') }}
            </NTooltip>

            <NTooltip trigger="hover">
              <template #trigger>
                <NButton size="small" quaternary circle @click="handleReset">
                  <template #icon>
                    <SvgIcon icon="mdi:backup-restore" class="text-16px" />
                  </template>
                </NButton>
              </template>
              {{ $t('common.reset') }}
            </NTooltip>
          </div>
        </div>
      </div>
    </NCard>

    <!-- Chart area (add Tab switch) -->
    <NCard :bordered="false" class="card-wrapper">
      <NTabs v-model:value="activeTab" type="line" animated>
        <NTabPane v-for="tab in tabOptions" :key="tab.key" :name="tab.key">
          <template #tab>
            <div class="flex items-center gap-2">
              <SvgIcon :icon="tab.icon" class="text-16px" />
              <span>{{ tab.label }}</span>
            </div>
          </template>

          <!-- Display corresponding content according to the selected tab -->
          <SalesOverview v-if="tab.key === 'overview'" :filter-options="filterOptions" :refresh-trigger="refreshTrigger" />
          <OrderAnalysis v-else-if="tab.key === 'orders'" :filter-options="filterOptions" :refresh-trigger="refreshTrigger" />
          <RefundAnalysis v-else-if="tab.key === 'refunds'" :filter-options="filterOptions" :refresh-trigger="refreshTrigger" />
          <RegionalAnalysis v-else-if="tab.key === 'regional'" :filter-options="filterOptions" :refresh-trigger="refreshTrigger" />
          <CustomerAnalysis v-else-if="tab.key === 'customers'" :filter-options="filterOptions" :refresh-trigger="refreshTrigger" />
          <TopRanking v-else-if="tab.key === 'topRanking'" :filter-options="filterOptions" :refresh-trigger="refreshTrigger" @game-click="handleGameClick" />
        </NTabPane>
      </NTabs>
    </NCard>

    <!-- 游戏统计抽屉 -->
    <GameStatsDrawer v-model:visible="gameStatsDrawerVisible" :game="selectedGameForStats" />
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
