<script setup lang="tsx">
import { computed, nextTick, onErrorCaptured, ref } from 'vue';
import { NButton, NCard, NModal, NSpace, NTabPane, NTabs } from 'naive-ui';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import FinanceReports from './modules/finance-reports.vue';
import FinanceStats from './modules/finance-stats.vue';
import OrderList from './modules/order-list.vue';
import ReportGenerator from './modules/report-generator.vue';

defineOptions({
  name: 'FinancePage'
});

interface OrderData {
  id: string;
  orderDate: string;
  buyerName: string;
  buyerCountry: string;
  currency: string;
  distributor: string;
  gameName: string;
  quantity: number;
  originalPrice: number;
  discount: number;
  finalPrice: number;
  orderAmount: number;
  orderStatus: 'completed' | 'processing' | 'cancelled' | 'refunded';
}

// Currently selected order
const selectedOrders = ref<OrderData[]>([]);

// Currently active tab
const activeTab = ref('orders');

// Report generation modal status
const showReportModal = ref(false);

// Report generator reference
const reportGeneratorRef = ref();

// Edit mode status
const isEditMode = ref(false);
const editingReportId = ref<string>('');
const editingReportData = ref<any>(null);

// Flag to prevent recursive updates
const isUpdatingState = ref(false);

// Multilingual computed properties
const editReportTitle = computed(() => {
  try {
    return $t('page.finance.editReport' as any);
  } catch {
    return 'Edit Financial Report';
  }
});

const updateReportText = computed(() => {
  try {
    return $t('page.finance.updateReport' as any);
  } catch {
    return 'Update Report';
  }
});

const reportUpdateSuccessText = computed(() => {
  try {
    return $t('page.finance.reportUpdateSuccess' as any);
  } catch {
    return 'Financial report updated successfully';
  }
});

const reportGenerateSuccessText = computed(() => {
  try {
    return $t('page.finance.reportGenerateSuccess' as any);
  } catch {
    return 'Financial report generated successfully';
  }
});

// Handle order selection
function handleOrdersSelected(orders: OrderData[]) {
  // Avoid triggering during state update
  if (!isUpdatingState.value) {
    selectedOrders.value = [...orders];
  }
}

// Handle order updates from the report generator
function handleOrdersUpdated(orders: OrderData[]) {
  // Avoid triggering a loop during state update
  if (isUpdatingState.value) return;

  // Check if data has actually changed to avoid unnecessary updates
  if (JSON.stringify(selectedOrders.value) === JSON.stringify(orders)) return;

  // Use debounce mechanism to avoid frequent updates
  nextTick(() => {
    if (!isUpdatingState.value) {
      selectedOrders.value = [...orders];
    }
  });
}

// Open report generation modal
function handleOpenReportModal() {
  if (!selectedOrders.value || selectedOrders.value.length === 0) {
    window.$message?.warning($t('page.finance.noOrdersSelected'));
    return;
  }
  showReportModal.value = true;
}

// Handle report generation
function handleReportGenerated(reportData: any) {
  // Additional report processing logic can be added here, such as saving to database, displaying preview, etc.
  if (reportData.isUpdate) {
    window.$message?.success(reportUpdateSuccessText.value);
  } else {
    window.$message?.success(reportGenerateSuccessText.value);
  }

  showReportModal.value = false;

  // Reset edit mode status
  isEditMode.value = false;
  editingReportId.value = '';
  editingReportData.value = null;

  // Switch to reports tab to view results
  activeTab.value = 'reports';
}

// Call the generate function of the report generator
function handleConfirmGenerate() {
  if (reportGeneratorRef.value) {
    reportGeneratorRef.value.handleGenerateReport();
  }
}

// Handle switching to the order list tab
function handleSwitchToOrders() {
  activeTab.value = 'orders';
  // Reset edit mode
  isEditMode.value = false;
  editingReportId.value = '';
  editingReportData.value = null;
  selectedOrders.value = [];
}

// Handle editing report
function handleEditReport(reportId: string) {
  // Prevent duplicate calls
  if (isUpdatingState.value) {
    return;
  }

  isUpdatingState.value = true;

  try {
    // Simulate fetching report data
    const mockReportData = {
      id: reportId,
      reportTitle: `财务报告 - ${reportId}`,
      startDate: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 days ago
      endDate: Date.now(),
      reportType: 'monthly',
      distributor: '',
      notes: 'This is a sample report',
      orders: [
        {
          id: 'ORD001',
          orderDate: '2024-01-15',
          buyerName: 'Zhang San',
          buyerCountry: 'CN',
          currency: 'CNY',
          distributor: 'Steam',
          gameName: 'Sample Game 1',
          quantity: 2,
          originalPrice: 100,
          discount: 0.1,
          finalPrice: 90,
          orderAmount: 180,
          orderStatus: 'completed' as const
        },
        {
          id: 'ORD002',
          orderDate: '2024-01-16',
          buyerName: 'Li Si',
          buyerCountry: 'CN',
          currency: 'CNY',
          distributor: 'Epic Games',
          gameName: 'Sample Game 2',
          quantity: 1,
          originalPrice: 200,
          discount: 0.05,
          finalPrice: 190,
          orderAmount: 190,
          orderStatus: 'completed' as const
        }
      ]
    };

    // Atomically batch update all states
    isEditMode.value = true;
    editingReportId.value = reportId;
    editingReportData.value = mockReportData;
    selectedOrders.value = [...mockReportData.orders];
    activeTab.value = 'orders';

    // Delay opening the modal to ensure all states are updated
    nextTick(() => {
      setTimeout(() => {
        showReportModal.value = true;
        // Reset update flag
        isUpdatingState.value = false;
      }, 100); // Give enough time to ensure state stability
    });
  } catch {
    isUpdatingState.value = false;
    window.$message?.error('编辑报告时发生错误，请重试');
  }
}

// Error handling
onErrorCaptured((error, _instance, info) => {
  // 记录错误信息用于调试
  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.error('Finance module error:', error, info);
  }
  window.$message?.error('An error occurred in the finance module. Please try again.');
  return false;
});
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 财务统计 -->
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:chart-line" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('page.finance.stats') }}</span>
        </div>
      </template>
      <FinanceStats />
    </NCard>

    <!-- 主要功能区域 -->
    <NCard :bordered="false" class="card-wrapper">
      <NTabs v-model:value="activeTab" type="line" animated>
        <!-- 订单列表标签页 -->
        <NTabPane name="orders" :tab="$t('page.finance.orders')">
          <OrderList
            :pre-selected-orders="isEditMode ? selectedOrders : []"
            :is-edit-mode="isEditMode"
            @orders-selected="handleOrdersSelected"
            @generate-report="handleOpenReportModal"
          />
        </NTabPane>

        <!-- 财务报表标签页 -->
        <NTabPane name="reports" :tab="$t('page.finance.reports')">
          <FinanceReports @switch-to-orders="handleSwitchToOrders" @edit-report="handleEditReport" />
        </NTabPane>
      </NTabs>
    </NCard>

    <!-- 报表生成弹窗 -->
    <NModal
      v-model:show="showReportModal"
      preset="card"
      :title="isEditMode ? `${editReportTitle} - ${editingReportId}` : $t('page.finance.generateReport')"
      class="max-w-5xl w-full"
      :bordered="false"
      size="huge"
      :segmented="true"
      :mask-closable="false"
      :close-on-esc="false"
    >
      <template #header-extra>
        <div class="flex items-center gap-2 text-sm text-gray-500">
          <SvgIcon icon="mdi:information-outline" />
          <span>{{ $t('page.finance.selectedCount', { count: selectedOrders.length }) }}</span>
        </div>
      </template>

      <ReportGenerator
        ref="reportGeneratorRef"
        :selected-orders="selectedOrders"
        :is-edit-mode="isEditMode"
        :editing-report-data="editingReportData"
        @report-generated="handleReportGenerated"
        @orders-updated="handleOrdersUpdated"
      />

      <template #action>
        <div class="flex justify-end gap-2">
          <NButton @click="showReportModal = false">
            {{ $t('common.cancel') }}
          </NButton>
          <NButton type="primary" @click="handleConfirmGenerate">
            <template #icon>
              <SvgIcon :icon="isEditMode ? 'mdi:content-save' : 'mdi:file-plus'" />
            </template>
            {{ isEditMode ? updateReportText : $t('page.finance.generateReport') }}
          </NButton>
        </div>
      </template>
    </NModal>
  </NSpace>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
