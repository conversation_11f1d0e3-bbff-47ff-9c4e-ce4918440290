<script setup lang="tsx">
import { ref, reactive } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'AdminSettings'
});

const activeTab = ref('email');

const emailSettings = reactive({
  smtpHost: 'smtp.example.com',
  smtpPort: 587,
  smtpUser: '<EMAIL>',
  smtpPassword: '',
  enableTLS: true,
  inviteTemplate: '欢迎加入我们的平台...',
  activationTemplate: '请点击以下链接激活您的账户...'
});

const reportSettings = reactive({
  autoGenerate: true,
  generateDay: 1,
  generateTime: null as number | null,
  autoAudit: false,
  autoPublish: false,
  notifyUsers: true
});

const notificationSettings = reactive({
  enableEmail: true,
  enableSMS: false,
  enablePush: true,
  orderNotification: true,
  reportNotification: true,
  systemNotification: true
});

function handleSaveEmailSettings() {
  console.log('Save email settings', emailSettings);
}

function handleSaveReportSettings() {
  console.log('Save report settings', reportSettings);
}

function handleSaveNotificationSettings() {
  console.log('Save notification settings', notificationSettings);
}

function handleTestEmail() {
  console.log('Test email sending');
}
</script>

<template>
  <div>
    <NTabs v-model:value="activeTab" type="line" animated>
      <!-- 邮件模板设置 -->
      <NTabPane name="email" :tab="$t('page.admin.emailSettings')">
        <div class="space-y-6">
          <NCard :title="$t('page.admin.smtpConfig')" size="small">
            <NGrid :cols="2" :x-gap="16" :y-gap="16">
              <NGi>
                <NFormItem :label="$t('page.admin.smtpHost')">
                  <NInput v-model:value="emailSettings.smtpHost" :placeholder="$t('page.admin.smtpHost')" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.admin.smtpPort')">
                  <NInputNumber v-model:value="emailSettings.smtpPort" :min="1" :max="65535" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.admin.smtpUser')">
                  <NInput v-model:value="emailSettings.smtpUser" :placeholder="$t('page.admin.smtpUser')" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.admin.smtpPassword')">
                  <NInput
                    v-model:value="emailSettings.smtpPassword"
                    type="password"
                    :placeholder="$t('page.admin.smtpPassword')"
                    show-password-on="click"
                  />
                </NFormItem>
              </NGi>
              <NGi :span="2">
                <NFormItem :label="$t('page.admin.enableTLS')">
                  <NSwitch v-model:value="emailSettings.enableTLS" />
                </NFormItem>
              </NGi>
            </NGrid>
            <div class="flex justify-end gap-2 mt-4">
              <NButton @click="handleTestEmail">{{ $t('page.admin.testConnection') }}</NButton>
              <NButton type="primary" @click="handleSaveEmailSettings">{{ $t('page.admin.saveConfig') }}</NButton>
            </div>
          </NCard>

          <NCard :title="$t('page.admin.emailTemplates')" size="small">
            <NGrid :cols="1" :y-gap="16">
              <NGi>
                <NFormItem :label="$t('page.admin.inviteTemplate')">
                  <NInput
                    v-model:value="emailSettings.inviteTemplate"
                    type="textarea"
                    :rows="4"
                    :placeholder="$t('page.admin.inviteTemplate')"
                  />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.admin.activationTemplate')">
                  <NInput
                    v-model:value="emailSettings.activationTemplate"
                    type="textarea"
                    :rows="4"
                    :placeholder="$t('page.admin.activationTemplate')"
                  />
                </NFormItem>
              </NGi>
            </NGrid>
          </NCard>
        </div>
      </NTabPane>

      <!-- 报表自动生成设置 -->
      <NTabPane name="report" :tab="$t('page.admin.reportSettings')">
        <NCard :title="$t('page.admin.autoGenerate')" size="small">
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGi>
              <NFormItem :label="$t('page.admin.autoGenerate')">
                <NSwitch v-model:value="reportSettings.autoGenerate" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.generateDay')">
                <NInputNumber
                  v-model:value="reportSettings.generateDay"
                  :min="1"
                  :max="31"
                  :disabled="!reportSettings.autoGenerate"
                />
                <span class="ml-2 text-gray-500">{{ $t('page.admin.monthlyDay') }}</span>
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.generateTime')">
                <NTimePicker
                  v-model:value="reportSettings.generateTime"
                  format="HH:mm"
                  :disabled="!reportSettings.autoGenerate"
                />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.autoAudit')">
                <NSwitch
                  v-model:value="reportSettings.autoAudit"
                  :disabled="!reportSettings.autoGenerate"
                />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.autoPublish')">
                <NSwitch
                  v-model:value="reportSettings.autoPublish"
                  :disabled="!reportSettings.autoGenerate || !reportSettings.autoAudit"
                />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.notifyUsers')">
                <NSwitch
                  v-model:value="reportSettings.notifyUsers"
                  :disabled="!reportSettings.autoGenerate"
                />
              </NFormItem>
            </NGi>
          </NGrid>
          <div class="flex justify-end mt-4">
            <NButton type="primary" @click="handleSaveReportSettings">{{ $t('common.update') }}</NButton>
          </div>
        </NCard>
      </NTabPane>

      <!-- 系统通知设置 -->
      <NTabPane name="notification" :tab="$t('page.admin.notificationSettings')">
        <NCard :title="$t('page.admin.notificationChannels')" size="small">
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGi>
              <NFormItem :label="$t('page.admin.emailNotification')">
                <NSwitch v-model:value="notificationSettings.enableEmail" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.smsNotification')">
                <NSwitch v-model:value="notificationSettings.enableSMS" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.pushNotification')">
                <NSwitch v-model:value="notificationSettings.enablePush" />
              </NFormItem>
            </NGi>
          </NGrid>
        </NCard>

        <NCard :title="$t('page.admin.notificationTypes')" size="small" class="mt-4">
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGi>
              <NFormItem :label="$t('page.admin.orderNotification')">
                <NSwitch v-model:value="notificationSettings.orderNotification" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.reportNotification')">
                <NSwitch v-model:value="notificationSettings.reportNotification" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.admin.systemNotification')">
                <NSwitch v-model:value="notificationSettings.systemNotification" />
              </NFormItem>
            </NGi>
          </NGrid>
          <div class="flex justify-end mt-4">
            <NButton type="primary" @click="handleSaveNotificationSettings">{{ $t('common.update') }}</NButton>
          </div>
        </NCard>
      </NTabPane>
    </NTabs>
  </div>
</template>

<style scoped></style>
