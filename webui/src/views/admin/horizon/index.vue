<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'AdminHorizon'
});

const appStore = useAppStore();

// 构建 Horizon 监控页面的 URL
const horizonUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_SERVICE_BASE_URL;
  return `${baseUrl}/horizon/dashboard`;
});
</script>

<template>
  <div class="h-full">
      <iframe
        :src="horizonUrl"
        class="size-full border-0"
        title="Horizon Dashboard"
      />
  </div>
</template>

<style scoped></style>
