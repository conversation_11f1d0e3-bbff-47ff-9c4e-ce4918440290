<script setup lang="tsx">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import GameImportLogs from './modules/game-import-logs.vue';

defineOptions({
  name: 'AdminGameImportLogs'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <div class="h-full">
    <NCard :bordered="false" class="card-wrapper h-full">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:database-import" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('page.admin.gameImportLogs') }}</span>
        </div>
      </template>
      <GameImportLogs />
    </NCard>
  </div>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
