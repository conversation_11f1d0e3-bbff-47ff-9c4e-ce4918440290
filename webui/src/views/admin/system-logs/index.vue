<script setup lang="tsx">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import SystemLogs from './modules/system-logs.vue';

defineOptions({
  name: 'AdminSystemLogs'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <div class="h-full">
    <NCard :bordered="false" class="card-wrapper h-full">
      <template #header>
        <div class="flex items-center gap-2">
          <SvgIcon icon="mdi:file-document-outline" class="text-20px" />
          <span class="text-18px font-semibold">{{ $t('page.admin.systemLogs') }}</span>
        </div>
      </template>
      <SystemLogs />
    </NCard>
  </div>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
