<script setup lang="tsx">
import { computed, h, reactive, ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NTag } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'SystemLogs'
});

interface LogData {
  id: string;
  operationTime: string;
  operationUser: string;
  operationType: string;
  operationContent: string;
  ipAddress: string;
  operationResult: 'success' | 'failed';
}

const searchForm = reactive({
  user: '',
  type: '',
  result: '',
  dateRange: null as [number, number] | null
});

const loading = ref(false);

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

const tableData = ref<LogData[]>([
  {
    id: '1',
    operationTime: '2024-01-15 10:30:25',
    operationUser: 'admin',
    operationType: 'UM',
    operationContent: 'Created user: user001',
    ipAddress: '*************',
    operationResult: 'success'
  },
  {
    id: '2',
    operationTime: '2024-01-15 10:25:18',
    operationUser: 'admin',
    operationType: 'OM',
    operationContent: 'Edited organization: TechCorp Solutions',
    ipAddress: '*************',
    operationResult: 'success'
  },
  {
    id: '3',
    operationTime: '2024-01-15 10:20:45',
    operationUser: 'user001',
    operationType: 'LOGIN',
    operationContent: 'User login attempt',
    ipAddress: '*************',
    operationResult: 'failed'
  }
]);

const columns = computed<DataTableColumns<LogData>>(() => [
  {
    title: $t('page.admin.operationTime'),
    key: 'operationTime',
    width: 160,
    sorter: true
  },
  {
    title: $t('page.admin.operationUser'),
    key: 'operationUser',
    width: 120
  },
  {
    title: $t('page.admin.operationType'),
    key: 'operationType',
    width: 120,
    render: row => {
      const typeColorMap: Record<string, string> = {
        'User Management': 'info',
        'Organization Management': 'success',
        'Game Management': 'warning',
        Login: 'default',
        'System Settings': 'error'
      };
      return h(NTag, { type: typeColorMap[row.operationType] as any }, { default: () => row.operationType });
    }
  },
  {
    title: $t('page.admin.operationContent'),
    key: 'operationContent',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.admin.ipAddress'),
    key: 'ipAddress',
    width: 140
  },
  {
    title: $t('page.admin.operationResult'),
    key: 'operationResult',
    width: 100,
    render: row => {
      return h(
        NTag,
        {
          type: row.operationResult === 'success' ? 'success' : 'error'
        },
        {
          default: () => (row.operationResult === 'success' ? $t('page.admin.success') : $t('page.admin.failed'))
        }
      );
    }
  },
  {
    title: $t('common.action'),
    key: 'actions',
    width: 80,
    render: () => {
      return h(ButtonIcon, {
        icon: 'mdi:eye',
        tooltipContent: $t('common.check'),
        class: 'text-primary'
      });
    }
  }
]);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

function handleSearch() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

function handleReset() {
  Object.assign(searchForm, {
    user: '',
    type: '',
    result: '',
    dateRange: null
  });
}

function handleExportLogs() {
  console.log('Export logs');
}

function handleClearLogs() {
  console.log('Clear logs');
}

function handleRefresh() {
  loading.value = true;
  // 模拟刷新数据
  setTimeout(() => {
    loading.value = false;
    // 这里可以调用实际的数据刷新API
  }, 1000);
}
</script>

<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-16px font-semibold">{{ $t('page.admin.systemLogs') }}</h3>
      <div class="flex items-center gap-2">
        <NButton @click="handleRefresh" :loading="loading">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
        <NButton @click="handleExportLogs">
          <template #icon>
            <SvgIcon icon="mdi:download" />
          </template>
          {{ $t('page.admin.exportLogs') }}
        </NButton>
        <NButton type="error" @click="handleClearLogs">
          <template #icon>
            <SvgIcon icon="mdi:delete" />
          </template>
          {{ $t('page.admin.clearLogs') }}
        </NButton>
      </div>
    </div>

    <!-- 紧凑日志筛选器 -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <!-- 搜索字段和按钮 -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.admin.operationUser') }}</span>
          <NInput
            v-model:value="searchForm.user"
            :placeholder="$t('page.admin.userPlaceholder')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:account" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-36 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.admin.operationType') }}</span>
          <NSelect
            v-model:value="searchForm.type"
            :placeholder="$t('page.admin.typePlaceholder')"
            :options="[
              { label: $t('page.admin.userManagement'), value: 'user' },
              { label: $t('page.admin.organizationManagement'), value: 'organization' },
              { label: $t('page.admin.gameManagement'), value: 'game' },
              { label: $t('page.admin.login'), value: 'login' },
              { label: $t('page.admin.systemSettings'), value: 'system' }
            ]"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-24 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
            {{ $t('page.admin.operationResult') }}
          </span>
          <NSelect
            v-model:value="searchForm.result"
            :placeholder="$t('page.admin.resultPlaceholder')"
            :options="[
              { label: $t('page.admin.success'), value: 'success' },
              { label: $t('page.admin.failed'), value: 'failed' }
            ]"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-64 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.admin.timeRange') }}</span>
          <NDatePicker v-model:value="searchForm.dateRange" type="datetimerange" size="small" clearable />
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton size="small" @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="mdi:magnify" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 日志列表表格 -->
    <NDataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      size="small"
      flex-height
      style="height: 500px"
    />
  </div>
</template>

<style scoped></style>
