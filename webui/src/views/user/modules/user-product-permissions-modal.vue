<script setup lang="tsx">
import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NSpace, NTag, NInput, NSelect, NDatePicker, NModal, NTabs, NTabPane, NDataTable, useMessage, useDialog } from 'naive-ui';
import {
  fetchUserProductPermissions,
  fetchGrantMultipleProductPermissions,
  fetchRevokeMultipleProductPermissions,
  fetchProductsList
} from '@/service/api';
import type Api from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { useAppStore } from '@/store/modules/app';
import { usePermission } from '@/hooks/common/permission';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import OrganizationSelector from '@/components/common/organization-selector.vue';

defineOptions({
  name: 'UserProductPermissionsModal'
});

interface Props {
  /** Modal visibility */
  modelValue: boolean;
  /** User data */
  user?: Api.Organization.OrganizationUser | null;
}

interface Emits {
  (e: 'update:modelValue', visible: boolean): void;
  (e: 'updated'): void;
}

const props = withDefaults(defineProps<Props>(), {
  user: null
});

const emit = defineEmits<Emits>();

const loading = ref(false);
const message = useMessage();
const dialog = useDialog();
const appStore = useAppStore();
const permission = usePermission();

// Modal visibility
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emit('update:modelValue', value);
  }
});

// Current user permissions
const userPermissions = ref<Api.ProductPermission.UserProductPermission[]>([]);
const loadingPermissions = ref(false);

// Available products for selection
const availableProducts = ref<Api.Product.ProductInfo[]>([]);
const loadingProducts = ref(false);

// Selected products for batch operations
const selectedProductIds = ref<number[]>([]);

// Selected permissions for batch operations
const selectedPermissionIds = ref<number[]>([]);

// Form data for granting permissions
const grantForm = reactive({
  permission_type: 'view-reports' as Api.ProductPermission.PermissionType,
  expires_at: null as string | null,
  notes: ''
});

// Search and filter
const searchForm = reactive({
  search: '',
  organisation_id: undefined as number | undefined
});

// Permission columns for current permissions table
const permissionColumns = computed<DataTableColumns<Api.ProductPermission.UserProductPermission>>(() => [
  {
    type: 'selection',
    width: 50
  },
  {
    title: $t('page.product.name'),
    key: 'product.name',
    render: row => row.product.name
  },
  {
    title: $t('page.user.organization'),
    key: 'product.organisation.name',
    render: row => row.product.organisation.name
  },
  {
    title: $t('page.user.permissionType'),
    key: 'permission_type',
    render: row => {
      return h(
        NTag,
        { type: 'info', size: 'small' },
        { default: () => row.permission_type }
      );
    }
  },
  {
    title: $t('page.user.expiresAt'),
    key: 'expires_at',
    render: row => {
      if (!row.expires_at) return $t('page.user.never');
      return new Date(row.expires_at).toLocaleDateString();
    }
  },
  {
    title: $t('page.user.grantedAt'),
    key: 'granted_at',
    render: row => new Date(row.granted_at).toLocaleDateString()
  },
  {
    title: $t('page.user.actions'),
    key: 'actions',
    width: 100,
    render: row => {
      return h(
        ButtonIcon,
        {
          icon: 'mdi:delete',
          tooltipContent: $t('page.user.revokePermission'),
          class: 'text-error',
          onClick: () => handleRevokePermission(row.product.id)
        }
      );
    }
  }
]);

// Product columns for available products table
const productColumns = computed<DataTableColumns<Api.Product.ProductInfo>>(() => [
  {
    type: 'selection',
    width: 50
  },
  {
    title: $t('page.product.name'),
    key: 'name'
  },
  {
    title: $t('page.user.organization'),
    key: 'organisation.name',
    render: row => row.organisation?.name || ''
  },
  {
    title: $t('page.product.code'),
    key: 'code'
  },
  {
    title: $t('page.product.sku'),
    key: 'sku'
  }
]);

// Load user permissions
async function loadUserPermissions() {
  if (!props.user) return;

  try {
    loadingPermissions.value = true;
    const params: any = {};

    // Only add organisation_id parameter if it has a value
    if (searchForm.organisation_id) {
      params.organisation_id = searchForm.organisation_id;
    }

    const { data } = await fetchUserProductPermissions(props.user.id, params);

    if (data?.success) {
      userPermissions.value = data.data?.permissions || [];
    } else {
      handleApiError({ response: { data } }, message, $t('page.user.loadPermissionsFailed'));
    }
  } catch (error) {
    handleApiError(error, message, $t('page.user.loadPermissionsFailed'));
  } finally {
    loadingPermissions.value = false;
  }
}

// Load available products
async function loadAvailableProducts() {
  try {
    loadingProducts.value = true;
    const params: any = {
      enabled: true,
      per_page: 100
    };

    // Only add search parameter if it has a value
    if (searchForm.search && searchForm.search.trim()) {
      params.search = searchForm.search.trim();
    }

    // Only add organisation_id parameter if it has a value
    if (searchForm.organisation_id) {
      params.organisation_id = searchForm.organisation_id;
    }

    const { data } = await fetchProductsList(params);

    if (data?.success) {
      availableProducts.value = data.data?.data || [];
    } else {
      handleApiError({ response: { data } }, message, $t('page.product.loadFailed'));
    }
  } catch (error) {
    handleApiError(error, message, $t('page.product.loadFailed'));
  } finally {
    loadingProducts.value = false;
  }
}

// Handle search
function handleSearch() {
  loadUserPermissions();
  loadAvailableProducts();
}

// Handle reset
function handleReset() {
  Object.assign(searchForm, {
    search: '',
    organisation_id: undefined
  });
  handleSearch();
}

// Handle grant permissions
async function handleGrantPermissions() {
  if (!props.user || selectedProductIds.value.length === 0) {
    message.warning($t('page.user.selectProductsFirst'));
    return;
  }

  try {
    loading.value = true;
    const { data } = await fetchGrantMultipleProductPermissions(props.user.id, {
      user_id: props.user.id,
      product_ids: selectedProductIds.value,
      permission_type: grantForm.permission_type,
      expires_at: grantForm.expires_at,
      notes: grantForm.notes || undefined
    });

    if (data?.success) {
      message.success($t('page.user.permissionsGranted', { count: data.data?.granted_count || 0 }));
      selectedProductIds.value = [];
      loadUserPermissions();
      emit('updated');
    } else {
      handleApiError({ response: { data } }, message, $t('page.user.grantPermissionsFailed'));
    }
  } catch (error) {
    handleApiError(error, message, $t('page.user.grantPermissionsFailed'));
  } finally {
    loading.value = false;
  }
}

// Handle revoke permission
async function handleRevokePermission(productId: number) {
  if (!props.user) return;

  dialog.warning({
    title: $t('common.tip'),
    content: $t('page.user.confirmRevokePermission'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        loading.value = true;
        const { data } = await fetchRevokeMultipleProductPermissions(props.user!.id, {
          product_ids: [productId],
          permission_type: 'view-reports'
        });

        if (data?.success) {
          message.success($t('page.user.permissionRevoked'));
          loadUserPermissions();
          emit('updated');
        } else {
          handleApiError({ response: { data } }, message, $t('page.user.revokePermissionFailed'));
        }
      } catch (error) {
        handleApiError(error, message, $t('page.user.revokePermissionFailed'));
      } finally {
        loading.value = false;
      }
    }
  });
}

// Handle batch revoke current permissions
async function handleBatchRevokeCurrentPermissions() {
  if (!props.user || selectedPermissionIds.value.length === 0) {
    message.warning($t('page.user.selectPermissionsFirst'));
    return;
  }

  // Get product IDs from selected permissions
  const selectedPermissions = userPermissions.value.filter(permission =>
    selectedPermissionIds.value.includes(permission.id)
  );
  const productIds = selectedPermissions.map(permission => permission.product.id);

  dialog.warning({
    title: $t('common.tip'),
    content: $t('page.user.confirmBatchRevokePermissions', { count: selectedPermissionIds.value.length }),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        loading.value = true;
        const { data } = await fetchRevokeMultipleProductPermissions(props.user!.id, {
          product_ids: productIds,
          permission_type: 'view-reports'
        });

        if (data?.success) {
          message.success($t('page.user.permissionsRevoked', { count: data.data?.revoked_count || 0 }));
          selectedPermissionIds.value = [];
          loadUserPermissions();
          emit('updated');
        } else {
          handleApiError({ response: { data } }, message, $t('page.user.revokePermissionsFailed'));
        }
      } catch (error) {
        handleApiError(error, message, $t('page.user.revokePermissionsFailed'));
      } finally {
        loading.value = false;
      }
    }
  });
}

// Handle batch revoke
async function handleBatchRevoke() {
  if (!props.user || selectedProductIds.value.length === 0) {
    message.warning($t('page.user.selectProductsFirst'));
    return;
  }

  dialog.warning({
    title: $t('common.tip'),
    content: $t('page.user.confirmBatchRevokePermissions', { count: selectedProductIds.value.length }),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        loading.value = true;
        const { data } = await fetchRevokeMultipleProductPermissions(props.user!.id, {
          product_ids: selectedProductIds.value,
          permission_type: 'view-reports'
        });

        if (data?.success) {
          message.success($t('page.user.permissionsRevoked', { count: data.data?.revoked_count || 0 }));
          selectedProductIds.value = [];
          loadUserPermissions();
          emit('updated');
        } else {
          handleApiError({ response: { data } }, message, $t('page.user.revokePermissionsFailed'));
        }
      } catch (error) {
        handleApiError(error, message, $t('page.user.revokePermissionsFailed'));
      } finally {
        loading.value = false;
      }
    }
  });
}

// Watch for modal visibility changes
watch(visible, (newVisible) => {
  if (newVisible && props.user) {
    loadUserPermissions();
    loadAvailableProducts();
  }
});

// Watch for user changes
watch(() => props.user, (newUser) => {
  if (newUser && visible.value) {
    loadUserPermissions();
    loadAvailableProducts();
  }
});
</script>

<template>
  <NModal
    v-model:show="visible"
    preset="card"
    :title="$t('page.user.manageProductPermissions')"
    class="w-[90vw] max-w-6xl"
    :mask-closable="false"
  >
    <div v-if="props.user" class="space-y-6">
      <!-- Enhanced user info and search filters section -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-4 border border-blue-100 dark:border-gray-600 shadow-sm">
        <div class="flex gap-6 items-center">
          <!-- Enhanced user info with avatar-style icon -->
          <div class="flex items-center gap-3 min-w-0 flex-shrink-0 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-600">
            <div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <SvgIcon icon="mdi:account" class="text-xl text-primary" />
            </div>
            <div class="min-w-0">
              <div class="font-semibold text-sm text-gray-900 dark:text-gray-100 truncate">{{ props.user.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ props.user.email }}</div>
            </div>
          </div>

          <!-- Enhanced search input -->
          <div class="flex-1 min-w-0">
            <div class="space-y-2">
              <label class="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                {{ $t('page.product.search') }}
              </label>
              <NInput
                v-model:value="searchForm.search"
                :placeholder="$t('page.product.searchPlaceholder')"
                size="medium"
                clearable
                class="search-input"
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <SvgIcon icon="mdi:magnify" class="text-gray-400" />
                </template>
              </NInput>
            </div>
          </div>

          <!-- Enhanced organization selector -->
          <div class="min-w-48 flex-shrink-0">
            <div class="space-y-2">
              <label class="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                {{ $t('page.user.organization') }}
              </label>
              <OrganizationSelector
                v-model="searchForm.organisation_id"
                :placeholder="$t('page.user.organizationPlaceholder')"
                size="medium"
                clearable
                show-all-option
                :all-option-value="undefined"
                class="org-selector"
              />
            </div>
          </div>

          <!-- Enhanced action buttons -->
          <div class="flex gap-3 flex-shrink-0 items-end pb-1">
            <NButton
              size="medium"
              class="reset-btn"
              @click="handleReset"
            >
              <template #icon>
                <SvgIcon icon="mdi:refresh" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton
              type="primary"
              size="medium"
              class="search-btn"
              :loading="loadingPermissions || loadingProducts"
              @click="handleSearch"
            >
              <template #icon>
                <SvgIcon icon="mdi:magnify" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </div>
        </div>
      </div>

      <!-- Enhanced tabs for current permissions and available products -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <NTabs default-value="current" type="line" class="enhanced-tabs">
          <NTabPane name="current" :tab="$t('page.user.currentPermissions')">
            <div class="p-6 space-y-6">
              <!-- Enhanced current permissions header -->
              <div class="flex justify-between items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                <div class="flex items-center gap-6">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span class="text-sm font-semibold text-gray-800 dark:text-gray-200">
                      {{ $t('page.user.currentPermissions') }}
                    </span>
                    <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
                      {{ userPermissions.length }}
                    </span>
                  </div>

                  <div v-if="selectedPermissionIds.length > 0" class="flex items-center gap-3">
                    <div class="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      {{ $t('page.user.selectedCount') }}
                    </span>
                    <span class="px-2 py-1 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs font-medium rounded-full">
                      {{ selectedPermissionIds.length }}
                    </span>
                    <NButton
                      size="small"
                      type="error"
                      class="danger-btn"
                      :loading="loading"
                      @click="handleBatchRevokeCurrentPermissions"
                    >
                      <template #icon>
                        <SvgIcon icon="mdi:delete" />
                      </template>
                      {{ $t('page.user.batchRevoke') }}
                    </NButton>
                  </div>
                </div>

                <NButton
                  size="small"
                  class="refresh-btn"
                  :loading="loadingPermissions"
                  @click="loadUserPermissions"
                >
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" />
                  </template>
                  {{ $t('common.refresh') }}
                </NButton>
              </div>

              <!-- Enhanced current permissions table -->
              <div class="table-container">
                <NDataTable
                  v-model:checked-row-keys="selectedPermissionIds"
                  :columns="permissionColumns"
                  :data="userPermissions"
                  :loading="loadingPermissions"
                  :bordered="false"
                  size="small"
                  :row-key="row => row.id"
                  class="enhanced-table min-h-[350px]"
                  :row-class-name="() => 'table-row'"
                />
              </div>
            </div>
          </NTabPane>

        <NTabPane name="grant" :tab="$t('page.user.grantPermissions')">
          <div class="p-6 space-y-6">
            <!-- Enhanced grant form -->
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-5 border border-green-200 dark:border-gray-600 shadow-sm">
              <div class="flex items-center gap-2 mb-4">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <h3 class="text-sm font-semibold text-gray-800 dark:text-gray-200">
                  {{ $t('page.user.grantPermissions') }}
                </h3>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="space-y-2">
                  <label class="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                    {{ $t('page.user.permissionType') }}
                  </label>
                  <NSelect
                    v-model:value="grantForm.permission_type"
                    :options="[
                      { label: 'view-reports', value: 'view-reports' }
                    ]"
                    size="medium"
                    class="permission-select"
                  />
                </div>

                <div class="space-y-2">
                  <label class="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                    {{ $t('page.user.expiresAt') }}
                  </label>
                  <NDatePicker
                    v-model:value="grantForm.expires_at"
                    type="datetime"
                    size="medium"
                    clearable
                    class="date-picker"
                    :placeholder="$t('page.user.selectExpiryDate')"
                  />
                </div>

                <div class="space-y-2">
                  <label class="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                    {{ $t('page.user.notes') }}
                  </label>
                  <NInput
                    v-model:value="grantForm.notes"
                    :placeholder="$t('page.user.notesPlaceholder')"
                    size="medium"
                    clearable
                    class="notes-input"
                  />
                </div>
              </div>
            </div>

            <!-- Enhanced action buttons -->
            <div class="flex gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
              <NButton
                type="primary"
                size="medium"
                class="grant-btn"
                :disabled="selectedProductIds.length === 0"
                :loading="loading"
                @click="handleGrantPermissions"
              >
                <template #icon>
                  <SvgIcon icon="mdi:plus" />
                </template>
                {{ $t('page.user.grantSelected') }}
                <span v-if="selectedProductIds.length > 0" class="ml-1 px-2 py-0.5 bg-white/20 rounded-full text-xs">
                  {{ selectedProductIds.length }}
                </span>
              </NButton>

              <NButton
                type="error"
                size="medium"
                class="revoke-btn"
                :disabled="selectedProductIds.length === 0"
                :loading="loading"
                @click="handleBatchRevoke"
              >
                <template #icon>
                  <SvgIcon icon="mdi:minus" />
                </template>
                {{ $t('page.user.revokeSelected') }}
                <span v-if="selectedProductIds.length > 0" class="ml-1 px-2 py-0.5 bg-white/20 rounded-full text-xs">
                  {{ selectedProductIds.length }}
                </span>
              </NButton>

              <div class="flex-1"></div>

              <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <SvgIcon icon="mdi:information" class="text-blue-500" />
                <span>{{ $t('page.user.selectProducts') }}</span>
              </div>
            </div>

            <!-- Enhanced available products table -->
            <div class="table-container">
              <div class="flex items-center gap-2 mb-4 px-1">
                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span class="text-sm font-semibold text-gray-800 dark:text-gray-200">
                  {{ $t('page.product.availableProducts') }}
                </span>
                <span class="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs font-medium rounded-full">
                  {{ availableProducts.length }}
                </span>
              </div>

              <NDataTable
                v-model:checked-row-keys="selectedProductIds"
                :columns="productColumns"
                :data="availableProducts"
                :loading="loadingProducts"
                :bordered="false"
                size="small"
                :row-key="row => row.id"
                class="enhanced-table min-h-[450px]"
                :row-class-name="() => 'table-row'"
              />
            </div>
          </div>
        </NTabPane>
      </NTabs>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end bg-gray-50 dark:bg-gray-800 -m-6 mt-4 p-4 rounded-b-xl border-t border-gray-200 dark:border-gray-700">
        <NButton
          size="medium"
          class="close-btn"
          @click="visible = false"
        >
          <template #icon>
            <SvgIcon icon="mdi:close" />
          </template>
          {{ $t('common.close') }}
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
/* Enhanced modal styling */
:deep(.n-modal-card) {
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

:deep(.n-card-header) {
  border-radius: 16px 16px 0 0;
  font-weight: 600;
}

/* Enhanced tabs styling */
.enhanced-tabs :deep(.n-tabs-nav) {
  background: linear-gradient(to right, #f8fafc, #f1f5f9);
  border-radius: 12px 12px 0 0;
  padding: 0 16px;
  margin: 0;
}

.enhanced-tabs :deep(.n-tabs-tab) {
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

.enhanced-tabs :deep(.n-tabs-tab--active) {
  color: #3b82f6;
  font-weight: 600;
}

.enhanced-tabs :deep(.n-tabs-tab-pane) {
  padding: 0;
}

/* Enhanced input styling */
.search-input :deep(.n-input) {
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.search-input :deep(.n-input:hover) {
  border-color: #cbd5e1;
}

.search-input :deep(.n-input--focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.org-selector :deep(.n-base-selection) {
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.org-selector :deep(.n-base-selection:hover) {
  border-color: #cbd5e1;
}

.org-selector :deep(.n-base-selection--focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Simplified button styling - consistent with project style */
.search-btn {
  border-radius: 6px;
  font-weight: 500;
}

.reset-btn {
  border-radius: 6px;
  font-weight: 500;
}

.refresh-btn {
  border-radius: 6px;
  font-weight: 500;
}

.danger-btn {
  border-radius: 6px;
  font-weight: 500;
}

.grant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.revoke-btn {
  border-radius: 6px;
  font-weight: 500;
}

.close-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* Enhanced form controls */
.permission-select :deep(.n-base-selection),
.date-picker :deep(.n-input),
.notes-input :deep(.n-input) {
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.permission-select :deep(.n-base-selection:hover),
.date-picker :deep(.n-input:hover),
.notes-input :deep(.n-input:hover) {
  border-color: #cbd5e1;
}

.permission-select :deep(.n-base-selection--focus),
.date-picker :deep(.n-input--focus),
.notes-input :deep(.n-input--focus) {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Enhanced table styling */
.table-container {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  background: white;
}

.enhanced-table :deep(.n-data-table-thead) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.enhanced-table :deep(.n-data-table-th) {
  background: transparent;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  padding: 16px 12px;
}

.enhanced-table :deep(.table-row) {
  transition: all 0.2s ease;
}

.enhanced-table :deep(.table-row:hover) {
  background: #f8fafc;
}

.enhanced-table :deep(.n-data-table-td) {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
}

/* Dark mode adjustments */
.dark .enhanced-tabs :deep(.n-tabs-nav) {
  background: linear-gradient(to right, #2d3748, #374151);
}

.dark .enhanced-tabs :deep(.n-tabs-tab) {
  color: #9ca3af;
}

.dark .enhanced-tabs :deep(.n-tabs-tab--active) {
  color: #3b82f6;
}

.dark .table-container {
  border-color: #374151;
  background: #1f2937;
}

.dark .enhanced-table :deep(.n-data-table-thead) {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
}

.dark .enhanced-table :deep(.n-data-table-th) {
  color: #f3f4f6;
  border-bottom-color: #4b5563;
}

.dark .enhanced-table :deep(.table-row:hover) {
  background: #374151;
}

.dark .enhanced-table :deep(.n-data-table-td) {
  border-bottom-color: #374151;
}

.dark .search-input :deep(.n-input),
.dark .org-selector :deep(.n-base-selection),
.dark .permission-select :deep(.n-base-selection),
.dark .date-picker :deep(.n-input),
.dark .notes-input :deep(.n-input) {
  border-color: #4b5563;
  background: #374151;
}

.dark .search-input :deep(.n-input:hover),
.dark .org-selector :deep(.n-base-selection:hover),
.dark .permission-select :deep(.n-base-selection:hover),
.dark .date-picker :deep(.n-input:hover),
.dark .notes-input :deep(.n-input:hover) {
  border-color: #6b7280;
}

/* Buttons use default Naive UI styling for consistency */
</style>
