<script setup lang="tsx">
import { computed, onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { fetchUsersList } from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { $t } from '@/locales';
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';

defineOptions({
  name: 'UserStats'
});

const loading = ref(false);
const message = useMessage();

// Statistics data
const stats = ref({
  total: 0,
  active: 0,
  pending: 0,
  newThisMonth: 0
});

// Helper function to get user status
function getUserStatus(user: Api.Organization.OrganizationUser): 'active' | 'inactive' | 'pending' {
  const isVerified = user.email_verified_at !== null;
  const hasOrganizations = user.organisations && user.organisations.length > 0;

  if (!isVerified) {
    return 'pending';
  }
  if (!hasOrganizations) {
    return 'inactive';
  }
  return 'active';
}

// Check if it's a new user this month
function isNewThisMonth(user: Api.Organization.OrganizationUser): boolean {
  const createdDate = new Date(user.created_at);
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();

  return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;
}

// Load user statistics data
async function loadUserStats() {
  try {
    loading.value = true;

    // Get all user data to calculate statistics
    const { data } = await fetchUsersList({ per_page: 1000 });

    if (data.success && data.data?.data) {
      const users = data.data.data;

      // Calculate various statistics
      const totalUsers = users.length;
      let activeUsers = 0;
      let pendingUsers = 0;
      let newThisMonth = 0;

      users.forEach(user => {
        const status = getUserStatus(user);

        if (status === 'active') {
          activeUsers++;
        } else if (status === 'pending') {
          pendingUsers++;
        }

        if (isNewThisMonth(user)) {
          newThisMonth++;
        }
      });

      stats.value = {
        total: totalUsers,
        active: activeUsers,
        pending: pendingUsers,
        newThisMonth
      };
    } else {
      handleApiError({ response: { data } }, message, $t('page.user.stats.loadFailed' as any));
    }
  } catch (error) {
    handleApiError(error, message, $t('page.user.stats.loadFailed' as any));
  } finally {
    loading.value = false;
  }
}

// Load data when component is mounted
onMounted(() => {
  loadUserStats();
});

// 暴露刷新方法给父组件
defineExpose({
  refreshStats: loadUserStats
});
</script>

<template>
  <StatsGrid>
    <StatsCard
      :title="$t('page.user.totalUsers')"
      :value="stats.total"
      :unit="$t('common.unit.people')"
      :color="{ start: '#007aff', end: '#0056cc' }"
      icon="mdi:account-group"
      :loading="loading"
    />
    <StatsCard
      :title="$t('page.user.activeUsers')"
      :value="stats.active"
      :unit="$t('common.unit.people')"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      icon="mdi:account-check"
      :loading="loading"
    />
    <StatsCard
      :title="$t('page.user.pendingActivation')"
      :value="stats.pending"
      :unit="$t('common.unit.people')"
      :color="{ start: '#fa8c16', end: '#d46b08' }"
      icon="mdi:account-clock"
      :loading="loading"
    />
    <StatsCard
      :title="$t('page.user.newUsers')"
      :value="stats.newThisMonth"
      :unit="$t('common.unit.people')"
      :color="{ start: '#722ed1', end: '#531dab' }"
      icon="mdi:account-plus"
      :loading="loading"
    />
  </StatsGrid>
</template>
