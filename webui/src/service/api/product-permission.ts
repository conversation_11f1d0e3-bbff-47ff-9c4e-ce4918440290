import { request } from '../request';

/**
 * Get authorized users for a product
 * @param productId Product ID
 */
export function fetchProductAuthorizedUsers(productId: number) {
  return request<Api.ApiResponse<Api.ProductPermission.AuthorizedUsersResponse>>({
    url: `/api/v1/products/${productId}/authorized-users`,
    method: 'get'
  });
}

/**
 * Grant product access permission to a user
 * @param productId Product ID
 * @param data Grant permission request data
 */
export function fetchGrantProductPermission(productId: number, data: Api.ProductPermission.GrantPermissionRequest) {
  return request<Api.ApiResponse<Api.ProductPermission.PermissionResponse>>({
    url: `/api/v1/products/${productId}/permissions`,
    method: 'post',
    data
  });
}

/**
 * Revoke product access permission from a user
 * @param productId Product ID
 * @param userId User ID
 * @param data Revoke permission request data
 */
export function fetchRevokeProductPermission(productId: number, userId: number, data: Api.ProductPermission.RevokePermissionRequest) {
  return request<Api.ApiResponse<null>>({
    url: `/api/v1/products/${productId}/permissions/${userId}`,
    method: 'delete',
    data
  });
}

/**
 * Get current user's accessible products
 */
export function fetchUserAccessibleProducts() {
  return request<Api.ApiResponse<{ products: Api.ProductPermission.AccessibleProduct[] }>>({
    url: '/api/v1/products/accessible',
    method: 'get'
  });
}
