import { request } from '../request';

/**
 * Sync log data interface
 */
export interface SyncLogData {
  id: number;
  sync_type: string;
  batch_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  total_records: number;
  success_records: number;
  failed_records: number;
  progress_percentage: number;
  started_at: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  records?: SyncRecordData[];
}

/**
 * Sync record data interface
 */
export interface SyncRecordData {
  id: number;
  status: 'success' | 'failed';
  error_message?: string;
  data: Record<string, any>;
  created_at: string;
}

/**
 * Sync logs list response interface
 */
export interface SyncLogsResponse {
  data: SyncLogData[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    from: number;
    to: number;
  };
}

/**
 * Active sync job interface
 */
export interface ActiveSyncJob {
  job_id: string;
  batch_id: string;
  sync_type: string;
  status: string;
  percentage: number;
  processed_chunks?: number;
  total_chunks?: number;
  elapsed_time?: number;
  started_at: string | null;
  updated_at?: string;
}

/**
 * Sync progress interface
 */
export interface SyncProgress {
  job_id: string;
  batch_id: string;
  status: string;
  percentage: number;
  processed_chunks: number;
  total_chunks: number;
  elapsed_time?: number;
  started_at: string;
  updated_at?: string;
  // Legacy fields for backward compatibility
  progress_percentage?: number;
  total_records?: number;
  processed_records?: number;
  success_records?: number;
  failed_records?: number;
  estimated_completion?: string;
}

/**
 * Get sync logs list
 * @param params Query parameters
 */
export function fetchSyncLogs(params?: {
  per_page?: number;
  sync_type?: string;
  status?: string;
}) {
  return request<Api.ApiResponse<SyncLogsResponse>>({
    url: '/api/v1/sync/logs',
    method: 'get',
    params
  });
}

/**
 * Get sync log details
 * @param id Sync log ID
 */
export function fetchSyncLogDetail(id: number) {
  return request<Api.ApiResponse<SyncLogData>>({
    url: `/api/v1/sync/logs/${id}`,
    method: 'get'
  });
}

/**
 * Trigger manual sync
 * @param data Sync configuration
 */
export function fetchTriggerSync(data?: {
  sync_type?: string;
  incremental?: boolean;
  batch_size?: number;
  timeout?: number;
}) {
  return request<Api.ApiResponse<{
    batch_id: string;
    status: string;
    message: string;
  }>>({
    url: '/api/v1/sync/trigger',
    method: 'post',
    data
  });
}

/**
 * Retry failed sync
 * @param id Sync log ID
 */
export function fetchRetrySync(id: number) {
  return request<Api.ApiResponse<{
    new_batch_id: string;
    original_batch_id: string;
    status: string;
    message: string;
  }>>({
    url: `/api/v1/sync/retry/${id}`,
    method: 'post'
  });
}

/**
 * Get sync progress
 * @param params Query parameters
 */
export function fetchSyncProgress(params: {
  job_id?: string;
  batch_id?: string;
}) {
  return request<Api.ApiResponse<SyncProgress>>({
    url: '/api/v1/sync/progress',
    method: 'get',
    params
  }).catch(error => {
    console.log('fetchSyncProgress caught error:', error);
    console.log('Error response status:', error?.response?.status);

    // If it's a 404 error, throw a specific error that can be caught
    if (error?.response?.status === 404 || error.message?.includes('Resource not found')) {
      console.log('Converting 404 to custom error');
      const notFoundError = new Error('Sync progress not found');
      (notFoundError as any).status = 404;
      (notFoundError as any).isNotFound = true;
      throw notFoundError;
    }
    // Re-throw other errors
    throw error;
  });
}

/**
 * Get active sync jobs
 */
export function fetchActiveSyncJobs() {
  return request<Api.ApiResponse<{
    active_jobs: ActiveSyncJob[];
    count: number;
  }>>({
    url: '/api/v1/sync/active-jobs',
    method: 'get'
  });
}
