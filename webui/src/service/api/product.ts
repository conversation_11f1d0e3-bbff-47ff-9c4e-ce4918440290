import { request } from '../request';

/**
 * Get products list with pagination and filtering
 * @param params Query parameters for filtering and pagination
 */
export function fetchProductsList(params?: {
  search?: string;
  organisation_id?: number;
  enabled?: boolean;
  per_page?: number;
  page?: number;
}) {
  return request<Api.ApiResponse<Api.Product.ProductListResponse>>({
    url: '/api/v1/products',
    method: 'get',
    params
  });
}

/**
 * Get product details by ID
 * @param id Product ID
 */
export function fetchProductDetails(id: number) {
  return request<Api.ApiResponse<Api.Product.ProductDetails>>({
    url: `/api/v1/products/${id}`,
    method: 'get'
  });
}

/**
 * Get current user's accessible products
 */
export function fetchUserAccessibleProducts() {
  return request<Api.ApiResponse<{ products: Api.Product.AccessibleProduct[] }>>({
    url: '/api/v1/products/accessible',
    method: 'get'
  });
}

/**
 * Get product statistics by fetching all products and calculating stats
 * @param params Optional parameters for filtering statistics
 */
export async function fetchProductStatistics(params?: {
  organisation_id?: number;
}) {
  try {
    // 获取所有产品来计算统计数据
    const allProductsResponse = await fetchProductsList({
      per_page: 100, // 获取最大允许的数据量来计算统计
      organisation_id: params?.organisation_id
    });

    if (allProductsResponse.data?.success && allProductsResponse.data.data) {
      const products = allProductsResponse.data.data.data;
      const total = allProductsResponse.data.data.meta.total;

      // 计算统计数据
      const enabledProducts = products.filter(p => p.enabled).length;
      const disabledProducts = products.filter(p => !p.enabled).length;

      // 计算本月新产品（简化版本，使用创建时间）
      const thisMonth = new Date();
      thisMonth.setDate(1);
      const newProductsThisMonth = products.filter(p => {
        const createdAt = new Date(p.created_at);
        return createdAt >= thisMonth;
      }).length;

      const statistics: Api.Product.ProductStatistics = {
        total_products: total,
        enabled_products: enabledProducts,
        disabled_products: disabledProducts,
        new_products_this_month: newProductsThisMonth
      };

      return {
        data: {
          success: true,
          message: 'Product statistics calculated successfully',
          data: statistics,
          timestamp: new Date().toISOString()
        }
      };
    } else {
      throw new Error('Failed to fetch products for statistics');
    }
  } catch (error) {
    console.error('Error calculating product statistics:', error);
    throw error;
  }
}
