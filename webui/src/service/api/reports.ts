import { request } from '../request';

/**
 * Get sales report data
 * @param params Report filter parameters
 */
export function fetchSalesReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.SalesReportResponse>>({
    url: '/api/v1/reports/sales',
    method: 'get',
    params
  });
}

/**
 * Get volume report data
 * @param params Report filter parameters
 */
export function fetchVolumeReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.VolumeReportResponse>>({
    url: '/api/v1/reports/volume',
    method: 'get',
    params
  });
}

/**
 * Get refunds report data
 * @param params Report filter parameters
 */
export function fetchRefundsReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.RefundReportResponse>>({
    url: '/api/v1/reports/refunds',
    method: 'get',
    params
  });
}

/**
 * Get order status report data
 * @param params Report filter parameters
 */
export function fetchOrderStatusReport(params: Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.OrderStatusReportResponse>>({
    url: '/api/v1/reports/order-status',
    method: 'get',
    params
  });
}

/**
 * Get product ranking report data
 * @param params Product ranking filter parameters
 */
export function fetchProductRanking(params: Api.Reports.ProductRankingFilterParams) {
  return request<Api.ApiResponse<Api.Reports.ProductRankingReportResponse>>({
    url: '/api/v1/reports/product-ranking',
    method: 'get',
    params
  });
}

/**
 * Export report data
 * @param data Export request data including filter parameters
 */
export function fetchExportReport(data: Api.Reports.ExportReportRequest & Api.Reports.ReportFilterParams) {
  return request<Api.ApiResponse<Api.Reports.ExportReportResponse>>({
    url: '/api/v1/reports/export',
    method: 'post',
    data
  });
}

/**
 * Helper function to get default report parameters
 * @param organisationId Organization ID (required)
 * @param daysBack Number of days to go back from today (default: 7)
 */
export function getDefaultReportParams(organisationId: number, daysBack: number = 7): Api.Reports.ReportFilterParams {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - daysBack);

  return {
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
    group_by: 'day',
    organisation_id: organisationId, // Pass single organization ID
    currency: 'USD'
  };
}

/**
 * Helper function to get last 7 days sales data for charts
 * @param organisationId Organization ID (required)
 */
export function fetchLast7DaysSalesData(organisationId: number) {
  const params = getDefaultReportParams(organisationId, 6); // Last 7 days including today
  return fetchSalesReport(params);
}

/**
 * Helper function to get last 7 days volume data for charts
 * @param organisationId Organization ID (required)
 */
export function fetchLast7DaysVolumeData(organisationId: number) {
  const params = getDefaultReportParams(organisationId, 6); // Last 7 days including today
  return fetchVolumeReport(params);
}

/**
 * Helper function to get current month sales data by country for pie charts
 * @param organisationId Organization ID (required)
 */
export function fetchCurrentMonthSalesByCountry(organisationId: number) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const params: Api.Reports.ReportFilterParams = {
    start_date: startOfMonth.toISOString().split('T')[0],
    end_date: now.toISOString().split('T')[0],
    group_by: 'month',
    organisation_id: organisationId, // Pass single organization ID
    currency: 'USD'
  };

  return fetchSalesReport(params);
}

/**
 * Helper function to get current month volume data by country for pie charts
 * @param organisationId Organization ID (required)
 */
export function fetchCurrentMonthVolumeByCountry(organisationId: number) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const params: Api.Reports.ReportFilterParams = {
    start_date: startOfMonth.toISOString().split('T')[0],
    end_date: now.toISOString().split('T')[0],
    group_by: 'month',
    organisation_id: organisationId // Pass single organization ID
  };

  return fetchVolumeReport(params);
}

/**
 * Helper function to get current month product ranking data for ranking lists
 * @param organisationId Organization ID (required)
 * @param limit Number of products to return (default: 10)
 */
export function fetchCurrentMonthProductRanking(organisationId: number, limit: number = 10) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const params: Api.Reports.ProductRankingFilterParams = {
    start_date: startOfMonth.toISOString().split('T')[0],
    end_date: now.toISOString().split('T')[0],
    organisation_id: organisationId,
    limit,
    currency: 'USD'
  };

  return fetchProductRanking(params);
}

/**
 * Helper function to get product ranking data for a specific date range
 * @param organisationId Organization ID (required)
 * @param dateRange Date range as [startTimestamp, endTimestamp]
 * @param limit Number of products to return (default: 10)
 */
export function fetchProductRankingByDateRange(organisationId: number, dateRange: [number, number], limit: number = 10) {
  const [startTimestamp, endTimestamp] = dateRange;
  const startDate = new Date(startTimestamp);
  const endDate = new Date(endTimestamp);

  const params: Api.Reports.ProductRankingFilterParams = {
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
    organisation_id: organisationId,
    limit,
    currency: 'USD'
  };

  return fetchProductRanking(params);
}

/**
 * Enhanced API functions with better error handling and loading states
 */

/**
 * Fetch sales report with enhanced error handling
 * @param params Report filter parameters
 * @param options Additional options like loading state management
 */
export async function fetchSalesReportEnhanced(
  params: Api.Reports.ReportFilterParams,
  options?: { onLoading?: (loading: boolean) => void }
) {
  try {
    options?.onLoading?.(true);
    const response = await fetchSalesReport(params);
    return { data: response, error: null };
  } catch (error) {
    console.error('Sales report fetch error:', error);
    return { data: null, error };
  } finally {
    options?.onLoading?.(false);
  }
}

/**
 * Fetch volume report with enhanced error handling
 * @param params Report filter parameters
 * @param options Additional options like loading state management
 */
export async function fetchVolumeReportEnhanced(
  params: Api.Reports.ReportFilterParams,
  options?: { onLoading?: (loading: boolean) => void }
) {
  try {
    options?.onLoading?.(true);
    const response = await fetchVolumeReport(params);
    return { data: response, error: null };
  } catch (error) {
    console.error('Volume report fetch error:', error);
    return { data: null, error };
  } finally {
    options?.onLoading?.(false);
  }
}

/**
 * Fetch refunds report with enhanced error handling
 * @param params Report filter parameters
 * @param options Additional options like loading state management
 */
export async function fetchRefundsReportEnhanced(
  params: Api.Reports.ReportFilterParams,
  options?: { onLoading?: (loading: boolean) => void }
) {
  try {
    options?.onLoading?.(true);
    const response = await fetchRefundsReport(params);
    return { data: response, error: null };
  } catch (error) {
    console.error('Refunds report fetch error:', error);
    return { data: null, error };
  } finally {
    options?.onLoading?.(false);
  }
}

/**
 * Fetch order status report with enhanced error handling
 * @param params Report filter parameters
 * @param options Additional options like loading state management
 */
export async function fetchOrderStatusReportEnhanced(
  params: Api.Reports.ReportFilterParams,
  options?: { onLoading?: (loading: boolean) => void }
) {
  try {
    options?.onLoading?.(true);
    const response = await fetchOrderStatusReport(params);
    return { data: response, error: null };
  } catch (error) {
    console.error('Order status report fetch error:', error);
    return { data: null, error };
  } finally {
    options?.onLoading?.(false);
  }
}

/**
 * Fetch product ranking with enhanced error handling
 * @param params Product ranking filter parameters
 * @param options Additional options like loading state management
 */
export async function fetchProductRankingEnhanced(
  params: Api.Reports.ProductRankingFilterParams,
  options?: { onLoading?: (loading: boolean) => void }
) {
  try {
    options?.onLoading?.(true);
    const response = await fetchProductRanking(params);
    return { data: response, error: null };
  } catch (error) {
    console.error('Product ranking fetch error:', error);
    return { data: null, error };
  } finally {
    options?.onLoading?.(false);
  }
}
