import { computed } from 'vue';
import { useAuthStore } from '@/store/modules/auth';

/**
 * Permission control composition function
 */
export function usePermission() {
  const authStore = useAuthStore();

  // Current user info
  const userInfo = computed(() => authStore.userInfo);

  // Complete user info (includes ID and detailed role information)
  const completeUserInfo = computed(() => authStore.completeUserInfo);

  // Current user roles
  const userRoles = computed(() => authStore.userInfo.roles || []);

  // Current user organizations
  const userOrganizations = computed(() => authStore.userOrganisations || []);

  // Current user system roles
  const userSystemRoles = computed(() => authStore.userSystemRoles || []);

  // Current user organisation roles
  const userOrganisationRoles = computed(() => authStore.userOrganisationRoles || []);

  // Get organization IDs where current user has owner role
  const ownedOrganizationIds = computed(() => {
    return userOrganisationRoles.value
      .filter(role => role.name === 'owner' && role.organisation_id !== null)
      .map(role => role.organisation_id!);
  });

  /**
   * Check if user has the specified role
   * @param role Role name
   * @returns Whether the user has the role
   */
  function hasRole(role: string): boolean {
    return userRoles.value.includes(role);
  }

  /**
   * Check if user has any of the specified roles
   * @param roles Array of role names
   * @returns Whether the user has any of the roles
   */
  function hasAnyRole(roles: string[]): boolean {
    return roles.some(role => userRoles.value.includes(role));
  }

  /**
   * Check if user has all of the specified roles
   * @param roles Array of role names
   * @returns Whether the user has all of the roles
   */
  function hasAllRoles(roles: string[]): boolean {
    return roles.every(role => userRoles.value.includes(role));
  }

  /**
   * Check if user is a system administrator (root or admin)
   * @returns Whether the user is a system administrator
   */
  function isSystemAdmin(): boolean {
    return hasAnyRole(['root', 'admin']);
  }

  /**
   * Check if user is an organization owner
   * @param organizationId Organization ID (optional, if not provided checks if user is owner in any organization)
   * @returns Whether the user is an organization owner
   */
  function isOrganizationOwner(organizationId?: number): boolean {
    if (!hasRole('owner')) {
      return false;
    }

    if (organizationId) {
      return ownedOrganizationIds.value.includes(organizationId);
    }

    return ownedOrganizationIds.value.length > 0;
  }

  /**
   * Check if user can manage users
   * @returns Whether the user can manage users
   */
  function canManageUsers(): boolean {
    return isSystemAdmin() || hasRole('owner');
  }

  /**
   * Check if user can create users
   * @returns Whether the user can create users
   */
  function canCreateUser(): boolean {
    return isSystemAdmin() || hasRole('owner');
  }

  /**
   * Check if user can edit the specified user
   * @param targetUser Target user
   * @returns Whether the user can edit
   */
  function canEditUser(targetUser?: Api.Auth.ApiUserInfo): boolean {
    // System admin can edit all users
    if (isSystemAdmin()) {
      return true;
    }

    // User can edit themselves
    if (targetUser && completeUserInfo.value && targetUser.id === completeUserInfo.value.id) {
      return true;
    }

    // Organization owner can edit users in organizations they own
    if (hasRole('owner') && targetUser) {
      const targetUserOrgIds = targetUser.organisations?.map(org => org.id) || [];
      return targetUserOrgIds.some(orgId => ownedOrganizationIds.value.includes(orgId));
    }

    return false;
  }

  /**
   * Check if user can delete the specified user
   * @param targetUser Target user
   * @returns Whether the user can delete
   */
  function canDeleteUser(targetUser?: Api.Auth.ApiUserInfo): boolean {
    // User cannot delete themselves
    if (targetUser && completeUserInfo.value && targetUser.id === completeUserInfo.value.id) {
      return false;
    }

    // System admin can delete users (except themselves)
    if (isSystemAdmin()) {
      return true;
    }

    // Organization owner can delete users in organizations they own (except themselves)
    if (hasRole('owner') && targetUser) {
      const targetUserOrgIds = targetUser.organisations?.map(org => org.id) || [];
      return targetUserOrgIds.some(orgId => ownedOrganizationIds.value.includes(orgId));
    }

    return false;
  }

  /**
   * Check if user can manage organizations
   * @returns Whether the user can manage organizations
   */
  function canManageOrganizations(): boolean {
    return isSystemAdmin();
  }

  /**
   * Check if user can assign the specified role
   * @param targetRole Target role
   * @returns Whether the user can assign the role
   */
  function canAssignRole(targetRole?: string): boolean {
    // System admin can assign all roles
    if (isSystemAdmin()) {
      return true;
    }

    // Organization owner can assign the member role
    if (hasRole('owner') && targetRole === 'member') {
      return true;
    }

    return false;
  }

  /**
   * Check if user can view user statistics
   * @returns Whether the user can view user statistics
   */
  function canViewUserStats(): boolean {
    return canManageUsers();
  }

  /**
   * Check if user can perform batch operations on users
   * @returns Whether the user can perform batch operations
   */
  function canBatchOperateUsers(): boolean {
    return isSystemAdmin() || hasRole('owner');
  }

  /**
   * Check if user can manage the organizations of the specified user
   * @param targetUser Target user
   * @returns Whether the user can manage organizations
   */
  function canManageUserOrganizations(targetUser?: Api.Auth.ApiUserInfo): boolean {
    // System admin can manage all users' organizations
    if (isSystemAdmin()) {
      return true;
    }

    // Organization owner can manage organizations of users in organizations they own
    if (hasRole('owner') && targetUser) {
      const targetUserOrgIds = targetUser.organisations?.map(org => org.id) || [];
      // console.log('canManageUserOrganizations',{
      //   targetUserOrgIds,
      //   ownedOrganizationIds: ownedOrganizationIds.value,
      //   userOrganisationRoles: userOrganisationRoles.value
      // })
      return targetUserOrgIds.some(orgId => ownedOrganizationIds.value.includes(orgId));
    }

    return false;
  }

  /**
   * Check if user can manage the roles of the specified user
   * @param targetUser Target user
   * @returns Whether the user can manage roles
   */
  function canManageUserRoles(targetUser?: Api.Auth.ApiUserInfo): boolean {
    return canManageUserOrganizations(targetUser);
  }

  /**
   * Check if user can permanently delete the specified user
   * @param targetUser Target user
   * @returns Whether the user can permanently delete
   */
  function canDeleteUserPermanently(targetUser?: Api.Auth.ApiUserInfo): boolean {
    // User cannot delete themselves
    if (targetUser && completeUserInfo.value && targetUser.id === completeUserInfo.value.id) {
      return false;
    }

    // Only system admin can permanently delete users
    return isSystemAdmin();
  }

  /**
   * Check if user can remove a user from an organization
   * @param targetUser Target user
   * @param organizationId Organization ID (optional)
   * @returns Whether the user can remove the user
   */
  function canRemoveUserFromOrganization(targetUser?: Api.Auth.ApiUserInfo, organizationId?: number): boolean {
    // System admin can remove any user
    if (isSystemAdmin()) {
      return true;
    }

    // Organization owner can remove users in their organizations (except themselves)
    if (hasRole('owner') && targetUser) {
      // User cannot remove themselves
      if (completeUserInfo.value && targetUser.id === completeUserInfo.value.id) {
        return false;
      }

      if (organizationId) {
        // Check if current user is the owner of the organization
        return ownedOrganizationIds.value.includes(organizationId);
      } else {
        // Check if the target user is in any organization owned by the current user
        const targetUserOrgIds = targetUser.organisations?.map(org => org.id) || [];
        return targetUserOrgIds.some(orgId => ownedOrganizationIds.value.includes(orgId));
      }
    }

    return false;
  }

  /**
   * Check if user can view all organizations
   * @returns Whether the user can view all organizations
   */
  function canViewAllOrganizations(): boolean {
    return isSystemAdmin();
  }

  /**
   * Get the list of organization IDs the user can manage
   * @returns List of organization IDs
   */
  function getManagedOrganizationIds(): number[] {
    if (isSystemAdmin()) {
      // System admin can theoretically manage all organizations, but return an empty array here to indicate API fetch is needed
      return [];
    }

    // Organization owner can only manage organizations they own
    if (hasRole('owner')) {
      return ownedOrganizationIds.value;
    }

    return [];
  }

  /**
   * Check if user has visitor permission for a specific product
   * @param productId Product ID (optional, if not provided checks if user has any visitor permission)
   * @returns Whether the user has visitor permission
   */
  function hasVisitorPermission(productId?: number): boolean {
    if (!completeUserInfo.value?.roles.permissions) {
      return false;
    }

    const permissions = completeUserInfo.value.roles.permissions;

    if (productId) {
      // Check for specific product permission
      return permissions.some(permission =>
        permission.name === 'visitor' && permission.product_id === productId
      );
    }

    // Check if user has any visitor permission
    return permissions.some(permission => permission.name === 'visitor');
  }

  /**
   * Get user's visitor permissions
   * @returns Array of visitor permissions
   */
  function getVisitorPermissions(): Api.Auth.Permission[] {
    if (!completeUserInfo.value?.roles.permissions) {
      return [];
    }

    return completeUserInfo.value.roles.permissions.filter(permission =>
      permission.name === 'visitor'
    );
  }

  /**
   * Get product IDs that user has visitor access to
   * @returns Array of product IDs
   */
  function getVisitorProductIds(): number[] {
    return getVisitorPermissions().map(permission => permission.product_id);
  }

  return {
    userInfo,
    completeUserInfo,
    userRoles,
    userOrganizations,
    userSystemRoles,
    userOrganisationRoles,
    ownedOrganizationIds,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isSystemAdmin,
    isOrganizationOwner,
    canManageUsers,
    canCreateUser,
    canEditUser,
    canDeleteUser,
    canManageOrganizations,
    canAssignRole,
    canViewUserStats,
    canBatchOperateUsers,
    canManageUserOrganizations,
    canManageUserRoles,
    canDeleteUserPermanently,
    canRemoveUserFromOrganization,
    canViewAllOrganizations,
    getManagedOrganizationIds,
    hasVisitorPermission,
    getVisitorPermissions,
    getVisitorProductIds
  };
}

/**
 * Permission directive
 * Used to control element display/hide in templates
 */
export const vPermission = {
  mounted(el: HTMLElement, binding: { value: string | string[] }) {
    const { hasAnyRole } = usePermission();

    const roles = Array.isArray(binding.value) ? binding.value : [binding.value];
    const hasPermission = hasAnyRole(roles);

    if (!hasPermission) {
      el.style.display = 'none';
    }
  },

  updated(el: HTMLElement, binding: { value: string | string[] }) {
    const { hasAnyRole } = usePermission();

    const roles = Array.isArray(binding.value) ? binding.value : [binding.value];
    const hasPermission = hasAnyRole(roles);

    if (!hasPermission) {
      el.style.display = 'none';
    } else {
      el.style.display = '';
    }
  }
};
