import { $t } from '@/locales';

/**
 * Chart data transformation utilities
 * Provides functions to transform API data to chart-compatible formats
 */

/**
 * Color schemes for charts
 */
export const chartColorSchemes = {
  primary: '#007aff',
  success: '#52c41a',
  warning: '#fa8c16',
  error: '#ff4d4f',
  info: '#1890ff',
  purple: '#722ed1',
  cyan: '#13c2c2',
  orange: '#fa8c16',
  red: '#ff4d4f',
  green: '#52c41a',
  blue: '#1890ff',
  gray: '#8c8c8c'
};

/**
 * Default color palette for multi-series charts
 */
export const defaultColorPalette = [
  '#007aff', '#52c41a', '#fa8c16', '#ff4d4f', '#722ed1',
  '#13c2c2', '#eb2f96', '#f5222d', '#fa541c', '#faad14'
];

/**
 * Transform sales report data to chart format
 */
export function transformSalesChartData(apiData: Api.Reports.SalesReportResponse) {
  if (!apiData) return null;

  return {
    dailySalesChart: apiData.daily_sales_chart,
    dualAxisChart: apiData.dual_axis_chart,
    regionalSalesChart: apiData.regional_sales_amount_chart
  };
}

/**
 * Transform volume report data to chart format
 */
export function transformVolumeChartData(apiData: Api.Reports.VolumeReportResponse) {
  if (!apiData) return null;

  return {
    dailyQuantityChart: apiData.daily_quantity_chart,
    regionalQuantityChart: apiData.regional_sales_quantity_chart
  };
}

/**
 * Transform refund report data to chart format
 */
export function transformRefundChartData(apiData: Api.Reports.RefundReportResponse) {
  if (!apiData) return null;

  // Use the new API format if available, otherwise fallback to old format
  let refundTrendChart: any;
  let refundReasonsChart: any;

  // Check if new API format is available
  if (apiData.refund_trend_chart) {
    refundTrendChart = apiData.refund_trend_chart;
  } else {
    // Fallback to old format - check if data array exists and is valid
    if (!apiData.data || !Array.isArray(apiData.data)) {
      refundTrendChart = {
        xAxis: { data: [] },
        series: [
          {
            name: $t('page.sales.refundAmount'),
            data: []
          },
          {
            name: $t('page.sales.refundCount'),
            data: []
          }
        ]
      };
    } else {
      // Transform old format data
      refundTrendChart = {
        xAxis: { data: apiData.data.map(item => item.period_label) },
        series: [
          {
            name: $t('page.sales.refundAmount'),
            data: apiData.data.map(item => item.refund_amount)
          },
          {
            name: $t('page.sales.refundCount'),
            data: apiData.data.map(item => item.refund_count)
          }
        ]
      };
    }
  }

  // Handle refund reasons chart
  if (apiData.refund_reasons_chart) {
    refundReasonsChart = apiData.refund_reasons_chart;
  } else {
    // Default empty reasons chart
    refundReasonsChart = {
      data: []
    };
  }

  return {
    refundTrendChart,
    refundReasonsChart,
    summary: apiData.summary || null
  };
}

/**
 * Transform order status report data to chart format
 */
export function transformOrderStatusChartData(apiData: Api.Reports.OrderStatusReportResponse) {
  if (!apiData) return null;

  // Transform order status chart data
  const orderStatusChart = {
    data: apiData.order_status_chart.map(item => ({
      name: getOrderStatusLabel(item.status),
      value: item.count,
      color: getOrderStatusColor(item.status)
    }))
  };

  // Transform payment status chart data
  const paymentStatusChart = {
    data: apiData.payment_status_chart.map(item => ({
      name: getPaymentStatusLabel(item.status),
      value: item.count,
      color: getPaymentStatusColor(item.status)
    }))
  };

  return {
    orderStatusChart,
    paymentStatusChart,
    summary: apiData.summary
  };
}

/**
 * Transform product ranking data to chart format
 */
export function transformProductRankingChartData(apiData: Api.Reports.ProductRankingReportResponse) {
  if (!apiData) return null;

  return {
    salesRankingChart: apiData.sales_ranking_chart,
    quantityRankingChart: apiData.quantity_ranking_chart,
    salesRankings: apiData.sales_rankings,
    quantityRankings: apiData.quantity_rankings,
    summary: apiData.summary
  };
}

/**
 * Get localized order status label
 */
export function getOrderStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    completed: $t('page.sales.orderStatusLabels.completed'),
    processing: $t('page.sales.orderStatusLabels.processing'),
    pending: $t('page.sales.orderStatusLabels.pending'),
    cancelled: $t('page.sales.orderStatusLabels.cancelled')
  };
  return statusMap[status] || status;
}

/**
 * Get order status color
 */
export function getOrderStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    completed: chartColorSchemes.success,
    processing: chartColorSchemes.warning,
    pending: chartColorSchemes.purple,
    cancelled: chartColorSchemes.error
  };
  return colorMap[status] || chartColorSchemes.gray;
}

/**
 * Get localized payment status label
 */
export function getPaymentStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    completed: $t('page.sales.paymentStatusLabels.completed'),
    pending: $t('page.sales.paymentStatusLabels.pending'),
    failed: $t('page.sales.paymentStatusLabels.failed'),
    cancelled: $t('page.sales.paymentStatusLabels.cancelled')
  };
  return statusMap[status] || status;
}

/**
 * Get payment status color
 */
export function getPaymentStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    completed: chartColorSchemes.success,
    pending: chartColorSchemes.warning,
    failed: chartColorSchemes.error,
    cancelled: chartColorSchemes.purple
  };
  return colorMap[status] || chartColorSchemes.gray;
}

/**
 * Create empty chart data structure
 */
export function createEmptyChartData(type: 'line' | 'bar' | 'pie') {
  switch (type) {
    case 'line':
    case 'bar':
      return {
        xAxis: { data: [] },
        series: [{ data: [] }]
      };
    case 'pie':
      return {
        series: [{ data: [] }]
      };
    default:
      return null;
  }
}

/**
 * Format chart tooltip
 */
export function formatChartTooltip(params: any, currency: string = 'USD') {
  if (Array.isArray(params)) {
    // Multi-series tooltip
    let tooltip = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`;
    params.forEach((param: any) => {
      const value = typeof param.value === 'number'
        ? (param.seriesName.includes('Amount') || param.seriesName.includes('Amount')
           ? formatCurrency(param.value, currency)
           : formatNumber(param.value))
        : param.value;
      tooltip += `<div>${param.marker} ${param.seriesName}: ${value}</div>`;
    });
    return tooltip;
  } else {
    // Single series tooltip
    const value = typeof params.value === 'number'
      ? (params.seriesName.includes('Amount') || params.seriesName.includes('Amount')
         ? formatCurrency(params.value, currency)
         : formatNumber(params.value))
      : params.value;
    return `${params.marker} ${params.name}: ${value}`;
  }
}

/**
 * Format currency value
 */
export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(value);
}

/**
 * Format number with thousand separators
 */
export function formatNumber(value: number): string {
  return new Intl.NumberFormat('en-US').format(value);
}

/**
 * Format percentage
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Get responsive chart height based on screen size
 */
export function getResponsiveChartHeight(baseHeight: number = 300): string {
  if (typeof window === 'undefined') return `${baseHeight}px`;

  const width = window.innerWidth;
  if (width < 768) {
    return `${Math.max(baseHeight * 0.8, 250)}px`;
  } else if (width < 1024) {
    return `${Math.max(baseHeight * 0.9, 280)}px`;
  }
  return `${baseHeight}px`;
}

/**
 * Debounce function for chart resize
 */
export function debounceChartResize(callback: () => void, delay: number = 300) {
  let timeoutId: NodeJS.Timeout;
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(callback, delay);
  };
}

/**
 * Generate chart grid configuration
 */
export function getChartGrid(options?: {
  top?: string | number;
  right?: string | number;
  bottom?: string | number;
  left?: string | number;
}) {
  return {
    top: options?.top || '10%',
    right: options?.right || '3%',
    bottom: options?.bottom || '10%',
    left: options?.left || '3%',
    containLabel: true
  };
}
