/**
 * Browser language detection utilities
 */

/**
 * Get browser's preferred languages
 * @returns Array of language codes from navigator.languages
 */
export function getBrowserLanguages(): readonly string[] {
  // Use navigator.languages if available (modern browsers)
  if (navigator.languages && navigator.languages.length > 0) {
    return navigator.languages;
  }

  // Fallback to navigator.language (older browsers)
  if (navigator.language) {
    return [navigator.language];
  }

  // Final fallback
  return ['en'];
}

/**
 * Map browser language codes to application supported language types
 * @param browserLang Browser language code (e.g., 'zh-CN', 'zh', 'en-US', 'en')
 * @returns Application language type or null if not supported
 */
export function mapBrowserLangToAppLang(browserLang: string): App.I18n.LangType | null {
  // Normalize the language code to lowercase
  const normalizedLang = browserLang.toLowerCase();

  // Direct mapping for exact matches
  const directMappings: Record<string, App.I18n.LangType> = {
    'zh-cn': 'zh-C<PERSON>',
    'zh-hans': 'zh-C<PERSON>',
    'zh-hans-cn': 'zh-CN',
    'zh': 'zh-CN',
    'en-us': 'en-US',
    'en': 'en-US',
    'en-gb': 'en-US',
    'en-au': 'en-US',
    'en-ca': 'en-US'
  };

  // Check direct mapping first
  if (directMappings[normalizedLang]) {
    return directMappings[normalizedLang];
  }

  // Extract primary language code (e.g., 'zh' from 'zh-TW')
  const primaryLang = normalizedLang.split('-')[0];

  // Map primary language codes
  const primaryMappings: Record<string, App.I18n.LangType> = {
    'zh': 'zh-CN',
    'en': 'en-US'
  };

  return primaryMappings[primaryLang] || null;
}

/**
 * Detect the best matching application language from browser preferences
 * @returns Application language type or default fallback
 */
export function detectBrowserLanguage(): App.I18n.LangType {
  const browserLanguages = getBrowserLanguages();

  // Try to find the first supported language
  for (const browserLang of browserLanguages) {
    const appLang = mapBrowserLangToAppLang(browserLang);
    if (appLang) {
      return appLang;
    }
  }

  // Default fallback to English
  return 'en-US';
}

/**
 * Get application language with browser detection fallback
 * This function checks localStorage first, then falls back to browser detection
 * @param storageValue Current value from localStorage
 * @returns Application language type
 */
export function getAppLanguageWithDetection(storageValue: string | null): App.I18n.LangType {
  // If storage has a value, use it
  if (storageValue) {
    return storageValue as App.I18n.LangType;
  }

  // Otherwise, detect from browser
  return detectBrowserLanguage();
}
