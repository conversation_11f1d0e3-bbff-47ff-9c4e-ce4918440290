/**
 * Product data adapters
 * 
 * Adapters to convert API product data to UI game data format
 */

/**
 * Convert API product data to UI game data format
 * @param product API product data
 * @returns UI game data format
 */
export function adaptProductToGameData(product: Api.Product.ProductInfo): Api.Product.GameData {
  // Generate a placeholder cover image based on product name
  const coverImage = `https://picsum.photos/seed/${product.id}/300/200`;
  
  // Create a basic game data structure
  return {
    id: String(product.id),
    coverImage,
    name: product.name,
    code: product.code,
    multiLangName: { en: product.name, zh: product.name }, // Default to same name for all languages
    languages: ['en'], // Default language
    onHand: 0, // Placeholder for inventory
    enabled: product.enabled,
    price: 0, // Placeholder for price
    sales: 0, // Placeholder for sales
    variants: [
      {
        id: product.store_variant_id || `variant-${product.id}`,
        name: product.name,
        code: product.code,
        onHand: 0,
        price: 0,
        enabled: product.enabled,
        type: 'default',
        description: product.package
      }
    ],
    organisation: product.organisation
  };
}

/**
 * Convert API product list to UI game data list
 * @param products API product list
 * @returns UI game data list
 */
export function adaptProductListToGameDataList(products: Api.Product.ProductInfo[]): Api.Product.GameData[] {
  return products.map(product => adaptProductToGameData(product));
}

/**
 * Generate mock game statistics based on product list
 * @param stats API product statistics
 * @returns UI game statistics
 */
export function adaptProductStatisticsToGameStats(stats: Api.Product.ProductStatistics) {
  return {
    totalGames: stats.total_products,
    enabledGames: stats.enabled_products,
    totalInventory: 0, // Placeholder
    newGames: stats.new_products_this_month
  };
}
