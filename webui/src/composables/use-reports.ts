import { computed, ref, watch } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { useMessage } from 'naive-ui';
import { handleApiError } from '@/utils/error-handler';
import { $t } from '@/locales';

/**
 * Composable for reports functionality
 * Provides common utilities for report components
 */
export function useReports() {
  const authStore = useAuthStore();
  const message = useMessage();

  // Loading states
  const isLoading = ref(false);
  const isExporting = ref(false);

  // Get current user's organization IDs
  const userOrganizations = computed(() => {
    return authStore.completeUserInfo?.organisations || [];
  });

  // Get first organization ID (for single organization selection)
  const defaultOrganizationId = computed(() => {
    const orgs = userOrganizations.value;
    return orgs.length > 0 ? orgs[0].id : null;
  });

  // Check if user is system admin
  const isSystemAdmin = computed(() => {
    const roles = authStore.completeUserInfo?.roles?.all_role_names || [];
    return roles.includes('root') || roles.includes('admin');
  });

  /**
   * Convert timestamp to YYYY-MM-DD format for API
   */
  function formatDateForApi(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toISOString().split('T')[0];
  }

  /**
   * Convert date range [startTimestamp, endTimestamp] to API format
   */
  function formatDateRangeForApi(dateRange: [number, number]): { start_date: string; end_date: string } {
    const [startTimestamp, endTimestamp] = dateRange;
    return {
      start_date: formatDateForApi(startTimestamp),
      end_date: formatDateForApi(endTimestamp)
    };
  }

  /**
   * Get default report parameters with organization ID
   */
  function getDefaultReportParams(
    organisationId?: number,
    daysBack: number = 6
  ): Partial<Api.Reports.ReportFilterParams> {
    const orgId = organisationId || defaultOrganizationId.value;

    if (!orgId) {
      throw new Error('No organization ID available');
    }

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - daysBack);

    return {
      start_date: formatDateForApi(startDate.getTime()),
      end_date: formatDateForApi(endDate.getTime()),
      organisation_id: orgId,
      group_by: 'day',
      currency: 'USD' // Keep USD as default for API calls
    };
  }

  /**
   * Handle API errors with user-friendly messages
   */
  function handleReportError(error: any, fallbackMessage?: string) {
    console.error('Report API Error:', error);

    const defaultMessage = fallbackMessage || $t('page.reports.loadFailed');
    handleApiError(error, message, defaultMessage);
  }

  /**
   * Validate organization access
   */
  function validateOrganizationAccess(organisationId: number): boolean {
    // System admins can access any organization
    if (isSystemAdmin.value) {
      return true;
    }

    // Check if user belongs to the organization
    const userOrgIds = userOrganizations.value.map(org => org.id);
    return userOrgIds.includes(organisationId);
  }

  /**
   * Get accessible organization IDs for current user
   */
  function getAccessibleOrganizationIds(): number[] {
    return userOrganizations.value.map(org => org.id);
  }

  /**
   * Format currency value for display
   */
  function formatCurrency(value: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value);
  }

  /**
   * Format number with thousand separators
   */
  function formatNumber(value: number): string {
    return new Intl.NumberFormat('en-US').format(value);
  }

  /**
   * Format percentage value
   */
  function formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`;
  }

  /**
   * Get date range for last N days
   */
  function getLastNDaysRange(days: number): [number, number] {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - (days - 1)); // Include today

    return [startDate.getTime(), endDate.getTime()];
  }

  /**
   * Get date range for current month
   */
  function getCurrentMonthRange(): [number, number] {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Don't go beyond today
    const today = new Date();
    const actualEndDate = endOfMonth.getTime() > today.getTime() ? today : endOfMonth;

    return [startOfMonth.getTime(), actualEndDate.getTime()];
  }

  /**
   * Get date range for last month
   */
  function getLastMonthRange(): [number, number] {
    const now = new Date();
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    return [startOfLastMonth.getTime(), endOfLastMonth.getTime()];
  }

  /**
   * Debounce function for API calls
   */
  function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  return {
    // State
    isLoading,
    isExporting,
    userOrganizations,
    defaultOrganizationId,
    isSystemAdmin,

    // Date utilities
    formatDateForApi,
    formatDateRangeForApi,
    getLastNDaysRange,
    getCurrentMonthRange,
    getLastMonthRange,

    // Report utilities
    getDefaultReportParams,
    validateOrganizationAccess,
    getAccessibleOrganizationIds,

    // Formatting utilities
    formatCurrency,
    formatNumber,
    formatPercentage,

    // Error handling
    handleReportError,

    // Utilities
    debounce
  };
}

/**
 * Composable for chart data processing
 * Provides utilities for transforming API data to chart format
 */
export function useChartData() {
  /**
   * Transform API chart data to ECharts format
   */
  function transformChartData(apiData: any) {
    if (!apiData) return null;

    // Handle different chart types
    if (apiData.xAxis && apiData.series) {
      return apiData; // Already in correct format
    }

    // Handle pie chart data
    if (Array.isArray(apiData)) {
      return {
        series: [{
          data: apiData.map((item: any) => ({
            name: item.name,
            value: item.value,
            itemStyle: item.color ? { color: item.color } : undefined
          }))
        }]
      };
    }

    return apiData;
  }

  /**
   * Merge chart options with data
   */
  function mergeChartOptions(baseOptions: any, data: any) {
    if (!data) return baseOptions;

    return {
      ...baseOptions,
      ...data
    };
  }

  return {
    transformChartData,
    mergeChartOptions
  };
}
