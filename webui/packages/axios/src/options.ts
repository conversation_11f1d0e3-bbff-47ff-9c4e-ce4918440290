import type { CreateAxiosDefaults } from 'axios';
import type { IAxiosRetryConfig } from 'axios-retry';
import { isHttpSuccess } from './shared';
import type { RequestOption } from './type';

export function createDefaultOptions<ResponseData = any>(options?: Partial<RequestOption<ResponseData>>) {
  const opts: RequestOption<ResponseData> = {
    onRequest: async config => config,
    isBackendSuccess: _response => true,
    onBackendFail: async () => {},
    transformBackendResponse: async response => response.data,
    onError: async () => {}
  };

  Object.assign(opts, options);

  return opts;
}

export function createRetryOptions(config?: Partial<CreateAxiosDefaults>) {
  const retryConfig: IAxiosRetryConfig = {
    retries: 0
  };

  Object.assign(retryConfig, config);

  return retryConfig;
}

export function createAxiosConfig(config?: Partial<CreateAxiosDefaults>) {
  const TEN_SECONDS = 10 * 1000;

  const axiosConfig: CreateAxiosDefaults = {
    timeout: TEN_SECONDS,
    headers: {
      'Content-Type': 'application/json'
    },
    validateStatus: isHttpSuccess,
    paramsSerializer: params => {
      // Custom serialization to handle empty arrays properly
      const parts: string[] = [];

      Object.keys(params).forEach(key => {
        const value = params[key];

        if (Array.isArray(value)) {
          if (value.length === 0) {
            // For empty arrays, add the parameter with empty brackets
            parts.push(`${encodeURIComponent(key)}[]`);
          } else {
            // For non-empty arrays, add each element
            value.forEach(item => {
              parts.push(`${encodeURIComponent(key)}[]=${encodeURIComponent(String(item))}`);
            });
          }
        } else if (value !== undefined && value !== null) {
          // For non-array values
          parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`);
        }
      });

      return parts.join('&');
    }
  };

  Object.assign(axiosConfig, config);

  return axiosConfig;
}
