# StatsCard 统计卡片组件

一个可复用的单个统计卡片组件，用于显示数据统计信息，支持动画效果、自定义配色。可以单独使用或配合 StatsGrid 组件实现网格布局。

## 功能特性

- 🎨 **渐变背景**: 支持自定义渐变色彩
- 📊 **数字动画**: 内置 CountTo 动画效果
- 💰 **货币支持**: 支持前缀和后缀单位显示
- 🎭 **图标装饰**: 支持 SVG 图标和装饰性背景
- 🔧 **高度可复用**: 单个卡片设计，可灵活组合使用

## 基础用法

### 单个卡片

```vue
<script setup lang="tsx">
import StatsCard from '@/components/custom/stats-card.vue';
</script>

<template>
  <StatsCard
    title="总用户数"
    :value="1234"
    unit="人"
    :color="{ start: '#007aff', end: '#0056cc' }"
    icon="mdi:account-group"
  />
</template>
```

### 网格布局

```vue
<script setup lang="tsx">
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';
</script>

<template>
  <StatsGrid>
    <StatsCard
      title="总用户数"
      :value="1234"
      unit="人"
      :color="{ start: '#007aff', end: '#0056cc' }"
      icon="mdi:account-group"
    />
    <StatsCard
      title="活跃用户"
      :value="856"
      unit="人"
      :color="{ start: '#52c41a', end: '#389e0d' }"
      icon="mdi:account-check"
    />
  </StatsGrid>
</template>
```

## API 接口

### StatsCard Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | `string` | - | 卡片标题 |
| value | `number` | - | 显示的数值 |
| unit | `string` | - | 单位文本 |
| color | `{start: string, end: string}` | - | 渐变色配置 |
| icon | `string` | - | 图标名称 |
| unitAsPrefix | `boolean` | `false` | 是否将单位作为前缀显示 |
| duration | `number` | `2000` | 动画持续时间（毫秒） |
| useEasing | `boolean` | `true` | 是否使用缓动动画 |
| transition | `string` | `'easeOutExpo'` | 动画过渡效果 |

### StatsGrid Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| columns | `object` | `{default: 1, sm: 2, lg: 4}` | 网格列数配置 |
| gap | `number` | `4` | 网格间距 |

## 使用示例

### 1. 单个卡片

```vue
<StatsCard
  title="总用户数"
  :value="1234"
  unit="人"
  :color="{ start: '#007aff', end: '#0056cc' }"
  icon="mdi:account-group"
/>
```

### 2. 货币显示（前缀）

```vue
<StatsCard
  title="总收入"
  :value="123456"
  unit="$"
  :unit-as-prefix="true"
  :color="{ start: '#007aff', end: '#0056cc' }"
  icon="mdi:currency-usd"
/>
```

### 3. 网格布局

```vue
<StatsGrid :columns="{ default: 1, sm: 2, lg: 3 }">
  <StatsCard
    title="项目1"
    :value="100"
    unit="个"
    :color="{ start: '#007aff', end: '#0056cc' }"
    icon="mdi:cube"
  />
  <StatsCard
    title="项目2"
    :value="200"
    unit="个"
    :color="{ start: '#52c41a', end: '#389e0d' }"
    icon="mdi:cube-outline"
  />
</StatsGrid>
```

### 4. 自定义动画

```vue
<StatsCard
  title="快速动画"
  :value="999"
  unit="次"
  :color="{ start: '#722ed1', end: '#531dab' }"
  icon="mdi:star"
  :duration="1000"
  :use-easing="false"
  transition="linear"
/>
```

## 样式定制

组件使用 Tailwind CSS 类名，支持以下自定义：

- 卡片圆角：默认 `rounded-lg`
- 内边距：默认 `p-6`
- 文字颜色：默认 `text-white`
- 网格间距：通过 StatsGrid 的 `gap` 属性控制

## 迁移指南

如果你之前使用的是独立的统计卡片代码，可以按以下步骤迁移：

1. 导入新组件：
```vue
import StatsCard from '@/components/custom/stats-card.vue';
import StatsGrid from '@/components/custom/stats-grid.vue';
```

2. 替换模板：
```vue
<!-- 旧代码 -->
<div class="grid grid-cols-1 gap-4 lg:grid-cols-4 sm:grid-cols-2">
  <div v-for="item in statsData" :key="item.key" class="...">
    <!-- 复杂的卡片结构 -->
  </div>
</div>

<!-- 新代码 -->
<StatsGrid>
  <StatsCard
    v-for="item in statsData"
    :key="item.key"
    :title="item.title"
    :value="item.value"
    :unit="item.unit"
    :color="item.color"
    :icon="item.icon"
    :unit-as-prefix="item.unitAsPrefix"
  />
</StatsGrid>
```

3. 删除不需要的样式和导入。

## 组合使用

### 混合布局

```vue
<template>
  <div class="space-y-6">
    <!-- 主要统计 -->
    <StatsGrid>
      <StatsCard title="总用户" :value="1234" unit="人" :color="..." icon="..." />
      <StatsCard title="活跃用户" :value="856" unit="人" :color="..." icon="..." />
    </StatsGrid>

    <!-- 单独的重要指标 -->
    <div class="w-80">
      <StatsCard title="重要指标" :value="999" unit="%" :color="..." icon="..." />
    </div>
  </div>
</template>
```

## 注意事项

- 确保图标名称正确，使用 `mdi:` 前缀
- 颜色值使用十六进制格式
- 数值应为数字类型，组件会自动处理格式化
- 单位字符串支持多语言，建议使用 `$t()` 函数
