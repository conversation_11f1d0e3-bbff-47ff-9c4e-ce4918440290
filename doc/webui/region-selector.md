# RegionSelector 区域选择器组件

一个通用的区域选择器组件，支持单选和多选模式，带有区域标识图标显示。

## 功能特性

- ✅ 支持单选和多选模式
- ✅ 区域标识图标显示（使用 circle-flags 图标集）
- ✅ 多语言支持
- ✅ 可搜索/过滤
- ✅ 可清除选择
- ✅ 多种尺寸支持
- ✅ 禁用状态支持
- ✅ 自定义标签渲染
- ✅ 返回 ISO 2位字母区域代码

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `value` | `string \| string[] \| null` | - | 选中的值（支持 v-model） |
| `multiple` | `boolean` | `false` | 是否启用多选 |
| `clearable` | `boolean` | `true` | 是否可清除 |
| `placeholder` | `string` | - | 占位符文本（自动根据单选/多选模式设置） |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | 组件尺寸 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `filterable` | `boolean` | `true` | 是否可搜索/过滤 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:value` | `value: string \| string[] \| null` | 值变化时触发 |

## 使用示例

### 基础用法

```vue
<script setup lang="ts">
import { ref } from 'vue';
import RegionSelector from '@/components/custom/region-selector.vue';

const selectedRegion = ref<string | null>(null);
</script>

<template>
  <RegionSelector v-model:value="selectedRegion" />
</template>
```

### 多选模式

```vue
<script setup lang="ts">
import { ref } from 'vue';
import RegionSelector from '@/components/custom/region-selector.vue';

const selectedRegions = ref<string[]>([]);
</script>

<template>
  <RegionSelector
    v-model:value="selectedRegions"
    multiple
  />
</template>
```

### 自定义配置

```vue
<template>
  <RegionSelector
    v-model:value="selectedRegions"
    multiple
    size="small"
    :clearable="false"
    :filterable="false"
    placeholder="请选择区域"
    @update:value="handleChange"
  />
</template>
```

### 在表单中使用

```vue
<script setup lang="ts">
import { reactive } from 'vue';
import { NForm, NFormItem } from 'naive-ui';
import RegionSelector from '@/components/custom/region-selector.vue';

const formData = reactive({
  region: null as string | null,
  regions: [] as string[]
});
</script>

<template>
  <NForm :model="formData">
    <NFormItem label="区域" path="region">
      <RegionSelector v-model:value="formData.region" />
    </NFormItem>

    <NFormItem label="多个区域" path="regions">
      <RegionSelector
        v-model:value="formData.regions"
        multiple
      />
    </NFormItem>
  </NForm>
</template>
```

## 支持的区域

组件包含以下国家（按字母顺序）：

- 🇺🇸 United States (US)
- 🇨🇦 Canada (CA)
- 🇬🇧 United Kingdom (GB)
- 🇩🇪 Germany (DE)
- 🇫🇷 France (FR)
- 🇮🇹 Italy (IT)
- 🇪🇸 Spain (ES)
- 🇳🇱 Netherlands (NL)
- 🇧🇪 Belgium (BE)
- 🇨🇭 Switzerland (CH)
- 🇦🇹 Austria (AT)
- 🇸🇪 Sweden (SE)
- 🇳🇴 Norway (NO)
- 🇩🇰 Denmark (DK)
- 🇫🇮 Finland (FI)
- 🇵🇱 Poland (PL)
- 🇨🇿 Czech Republic (CZ)
- 🇭🇺 Hungary (HU)
- 🇷🇴 Romania (RO)
- 🇧🇬 Bulgaria (BG)
- 🇭🇷 Croatia (HR)
- 🇸🇮 Slovenia (SI)
- 🇸🇰 Slovakia (SK)
- 🇱🇹 Lithuania (LT)
- 🇱🇻 Latvia (LV)
- 🇪🇪 Estonia (EE)
- 🇮🇪 Ireland (IE)
- 🇵🇹 Portugal (PT)
- 🇬🇷 Greece (GR)
- 🇨🇾 Cyprus (CY)
- 🇲🇹 Malta (MT)
- 🇱🇺 Luxembourg (LU)
- 🇯🇵 Japan (JP)
- 🇰🇷 South Korea (KR)
- 🇨🇳 China (CN)
- 🇭🇰 Hong Kong (HK)
- 🇨🇳 Taiwan (TW)
- 🇸🇬 Singapore (SG)
- 🇲🇾 Malaysia (MY)
- 🇹🇭 Thailand (TH)
- 🇮🇩 Indonesia (ID)
- 🇵🇭 Philippines (PH)
- 🇻🇳 Vietnam (VN)
- 🇮🇳 India (IN)
- 🇦🇺 Australia (AU)
- 🇳🇿 New Zealand (NZ)
- 🇧🇷 Brazil (BR)
- 🇦🇷 Argentina (AR)
- 🇲🇽 Mexico (MX)
- 🇨🇱 Chile (CL)
- 🇨🇴 Colombia (CO)
- 🇵🇪 Peru (PE)
- 🇺🇾 Uruguay (UY)
- 🇿🇦 South Africa (ZA)
- 🇪🇬 Egypt (EG)
- 🇮🇱 Israel (IL)
- 🇦🇪 United Arab Emirates (AE)
- 🇸🇦 Saudi Arabia (SA)
- 🇹🇷 Turkey (TR)
- 🇷🇺 Russia (RU)
- 🇺🇦 Ukraine (UA)

## 注意事项

1. 组件返回的是 ISO 2位字母区域代码（如 'US', 'CN', 'GB'）
2. 图标使用 `circle-flags` 图标集，确保项目中已安装相关依赖
3. 多语言文本需要在语言文件中配置相应的翻译键
4. 组件基于 Naive UI 的 NSelect 组件构建
5. 台湾地区使用中国国旗图标显示

## 扩展

如需添加更多区域，请在 `countryData` 数组中添加相应的区域信息：

```typescript
{ code: 'XX', name: 'Region Name', icon: 'circle-flags:xx' }
```

其中 `code` 为 ISO 2位字母代码，`name` 为区域名称，`icon` 为对应的图标名称。
