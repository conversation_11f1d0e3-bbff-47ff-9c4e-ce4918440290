# JAST Partner API 文档

## 概述

JAST Partner API 是一个基于 Laravel 12 和 PHP 8.4 构建的 RESTful API，提供用户认证、健康检查等核心功能。

## 基础信息

- **基础URL**: `http://localhost` (开发环境)
- **API版本**: v1
- **认证方式**: <PERSON><PERSON> (Laravel Sanctum)
- **数据格式**: JSON

## 环境配置

### 开发环境
- **URL**: `http://localhost`
- **端口**: 80 (HTTP), 443 (HTTPS)

### 生产环境
- **URL**: `https://your-production-domain.com`

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": "2025-06-05T11:10:47.593773Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "errors": {
    // 详细错误信息
  },
  "timestamp": "2025-06-05T11:10:47.593773Z"
}
```

## API 端点

### 1. 健康检查和状态

#### 1.1 全局健康检查
- **端点**: `GET /api/health`
- **描述**: 检查API服务整体健康状态
- **认证**: 无需认证
- **响应示例**:
```json
{
  "status": "healthy",
  "checks": {
    "database": {
      "status": "ok",
      "message": "Database connection successful"
    },
    "cache": {
      "status": "ok", 
      "message": "Cache check completed"
    },
    "storage": {
      "status": "ok",
      "message": "Storage check completed"
    }
  },
  "timestamp": "2025-06-05T11:09:28.385058Z"
}
```

#### 1.2 API v1 健康检查
- **端点**: `GET /api/v1/health`
- **描述**: 检查API v1版本健康状态
- **认证**: 无需认证

#### 1.3 API v1 状态信息
- **端点**: `GET /api/v1/status`
- **描述**: 获取API v1详细状态信息
- **认证**: 无需认证

### 2. 用户认证与注册

#### 2.1 发送邮箱验证码
- **端点**: `POST /api/v1/users/send-verification-code`
- **描述**: 为用户注册发送邮箱验证码
- **认证**: 无需认证
- **请求体**:
```json
{
  "email": "<EMAIL>"
}
```
- **验证规则**:
  - `email`: 必填，有效邮箱格式，最大255字符
- **成功响应**:
```json
{
  "success": true,
  "message": "Verification code sent to your email address",
  "data": {
    "message": "Verification code sent successfully",
    "expires_in_seconds": 900
  },
  "timestamp": "2025-06-20T12:00:00.000000Z"
}
```
- **错误响应**:
  - **422 验证失败**: 验证码已发送，需要等待
  ```json
  {
    "success": false,
    "message": "A verification code has already been sent to this email address. Please wait before requesting a new one.",
    "errors": {
      "retry_after_seconds": 600
    },
    "timestamp": "2025-06-20T12:00:00.000000Z"
  }
  ```

#### 2.2 用户注册
- **端点**: `POST /api/v1/users/register`
- **描述**: 游客用户注册（无需认证）
- **认证**: 无需认证
- **请求体**:
```json
{
  "name": "New User",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "verification_code": "123456",
  "organisation_ids": [1, 2]
}
```
- **验证规则**:
  - `name`: 必填，字符串，最大255字符
  - `email`: 必填，有效邮箱格式，最大255字符，必须唯一
  - `password`: 必填，字符串，最少8个字符
  - `password_confirmation`: 必填，必须与password匹配
  - `verification_code`: 必填，6位数字验证码
  - `organisation_ids`: 可选，组织ID数组
- **成功响应**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "id": 2,
    "name": "New User",
    "email": "<EMAIL>",
    "email_verified_at": "2025-06-20T12:00:00.000000Z",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-20T12:00:00.000000Z",
    "updated_at": "2025-06-20T12:00:00.000000Z"
  },
  "timestamp": "2025-06-20T12:00:00.000000Z"
}
```

#### 2.3 用户登录
- **端点**: `POST /api/v1/auth/login`
- **描述**: 用户登录获取访问令牌
- **认证**: 无需认证
- **请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```
- **验证规则**:
  - `email`: 必填，有效邮箱格式，最大255字符
  - `password`: 必填，字符串，最少8个字符
- **成功响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "name": "Test User",
      "email": "<EMAIL>",
      "email_verified_at": null
    },
    "token": "3|7JHCY3kjoLZPlvGUGjhDZLrRXMnuH88o0WAvUNjc33285326",
    "token_type": "Bearer"
  },
  "timestamp": "2025-06-05T11:10:47.593773Z"
}
```

#### 2.4 用户退出登录
- **端点**: `POST /api/v1/auth/logout`
- **描述**: 撤销当前访问令牌
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "退出登录成功",
  "data": [],
  "timestamp": "2025-06-05T11:12:36.228620Z"
}
```

#### 2.5 撤销所有令牌
- **端点**: `POST /api/v1/auth/revoke-all-tokens`
- **描述**: 撤销用户的所有访问令牌
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "所有令牌已撤销",
  "data": [],
  "timestamp": "2025-06-05T11:12:36.228620Z"
}
```

### 3. 用户管理

#### 3.1 获取当前用户
- **端点**: `GET /api/v1/user`
- **描述**: 获取当前认证用户的基本信息，包括角色和产品级权限
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Test User",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      }
    ],
    "roles": {
      "system_roles": [
        {
          "id": 1,
          "name": "root",
          "guard_name": "system",
          "organisation_id": null
        }
      ],
      "organisation_roles": [
        {
          "id": 3,
          "name": "owner",
          "guard_name": "api",
          "organisation_id": 1,
          "organisation_name": "Tech Solutions Ltd"
        }
      ],
      "all_role_names": ["root", "owner"],
      "permissions": [
        {
          "name": "visitor",
          "product_id": 1001,
          "permission_type": "view-reports",
          "notes": "产品报表查看权限"
        },
        {
          "name": "visitor",
          "product_id": 1002,
          "permission_type": "export-reports",
          "notes": "产品报表导出权限"
        }
      ]
    },
    "created_at": "2025-06-05T11:08:36.000000Z",
    "updated_at": "2025-06-05T11:08:36.000000Z"
  },
  "timestamp": "2025-06-05T11:11:01.047925Z"
}
```

#### 3.2 获取用户列表
- **端点**: `GET /api/v1/users`
- **描述**: 获取用户列表，支持分页和组织筛选
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看所有用户
  - **组织所有者（Owner）**: 只能查看自己组织的用户
  - **组织成员（Member）**: 只能查看自己组织的用户
- **查询参数**:
  - `per_page` (可选): 每页数量，默认15
  - `organisation_id` (可选): 按组织ID筛选用户（组织用户只能筛选自己的组织）
  - `status` (可选): 按状态筛选用户
- **响应示例**:
```json
{
  "success": true,
  "message": "获取用户列表成功",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "email_verified_at": null,
        "organisations": [
          {
            "id": 1,
            "name": "Tech Solutions Ltd",
            "code": "TECH001",
            "status": "active"
          }
        ],
        "created_at": "2025-06-06T06:56:33.000000Z",
        "updated_at": "2025-06-06T06:56:33.000000Z"
      }
    ],
    "meta": {
      "total": 1,
      "per_page": 15,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 1
    }
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 3.3 创建用户
- **端点**: `POST /api/v1/users`
- **描述**: 创建新用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以在任何组织中创建用户
  - **组织所有者（Owner）**: 只能在自己的组织中创建用户
- **请求参数**:
  - `name` (必填): 用户名称
  - `email` (必填): 邮箱地址，必须唯一
  - `password` (必填): 密码，最少8个字符
  - `organisation_ids` (可选): 组织ID数组（组织所有者只能指定自己的组织）
- **请求示例**:
```json
{
  "name": "New User",
  "email": "<EMAIL>",
  "password": "password123",
  "organisation_ids": [1, 2]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "id": 2,
    "name": "New User",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T07:00:00.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.4 获取用户详情
- **端点**: `GET /api/v1/users/{id}`
- **描述**: 获取指定用户的详细信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看任何用户
  - **组织用户（Owner/Member）**: 只能查看同组织的用户
- **响应示例**:
```json
{
  "success": true,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 3.5 更新用户
- **端点**: `PUT /api/v1/users/{id}`
- **描述**: 更新用户信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以更新任何用户
  - **组织所有者（Owner）**: 只能更新同组织的用户
- **请求参数**: 所有参数都是可选的
  - `name`: 用户名称
  - `email`: 邮箱地址，必须唯一
  - `password`: 密码，最少8个字符
  - `organisation_ids`: 组织ID数组（组织所有者只能指定自己的组织）
- **请求示例**:
```json
{
  "name": "Updated Name",
  "organisation_ids": [2, 3]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "用户更新成功",
  "data": {
    "id": 1,
    "name": "Updated Name",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      },
      {
        "id": 3,
        "name": "Future Tech",
        "code": "FUTURE003",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.6 暂停用户
- **端点**: `POST /api/v1/users/{id}/suspend`
- **描述**: 暂停用户（撤销所有访问令牌）
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以暂停任何用户
  - **组织所有者（Owner）**: 只能暂停同组织的用户
- **响应示例**:
```json
{
  "success": true,
  "message": "用户暂停成功",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
ro'le
```

#### 3.7 激活用户
- **端点**: `POST /api/v1/users/{id}/activate`
- **描述**: 激活用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以激活任何用户
  - **组织所有者（Owner）**: 只能激活同组织的用户
- **响应示例**:
```json
{
  "success": true,
  "message": "用户激活成功",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

### 3.8 用户-组织关联管理

#### 3.8.1 添加用户到组织
- **端点**: `POST /api/v1/users/{userId}/organisations/{organisationId}`
- **描述**: 将用户添加到指定组织
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以将用户添加到任何组织
  - **组织所有者（Owner）**: 只能将用户添加到自己拥有的组织
- **响应示例**:
```json
{
  "success": true,
  "message": "用户已添加到组织",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.8.2 从组织中移除用户
- **端点**: `DELETE /api/v1/users/{userId}/organisations/{organisationId}`
- **描述**: 从指定组织中移除用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以从任何组织中移除用户
  - **组织所有者（Owner）**: 只能从自己拥有的组织中移除用户
- **响应示例**:
```json
{
  "success": true,
  "message": "用户已从组织中移除",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.8.3 同步用户组织关联
- **端点**: `PUT /api/v1/users/{userId}/organisations`
- **描述**: 同步用户的组织关联（替换现有关联）
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以同步用户到任何组织
  - **组织所有者（Owner）**: 只能同步用户到自己拥有的组织
- **请求参数**:
  - `organisation_ids` (必填): 组织ID数组（组织所有者只能指定自己拥有的组织）
- **请求示例**:
```json
{
  "organisation_ids": [1, 3]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "用户组织关联已同步",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 3,
        "name": "Future Tech",
        "code": "FUTURE003",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

### 4. 用户角色管理

#### 4.1 获取可分配角色列表
- **端点**: `GET /api/v1/users/assignable-roles`
- **描述**: 获取当前用户可以分配给其他用户的角色列表，返回结构化的角色信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以分配除Root外的所有角色，details为空数组
  - **组织所有者（Owner）**: 只能分配Member角色，details包含完整角色信息
- **响应结构说明**:
  - `roles`: 按guard_name分组的角色名称列表
  - `details`: 角色详细信息数组（Root/Admin用户为空数组，其他用户包含完整信息）

- **Root/Admin用户响应示例**:
```json
{
  "success": true,
  "message": "可赋予角色列表获取成功",
  "data": {
    "roles": {
      "system": ["admin"],
      "api": ["owner", "member", "visitor"]
    },
    "details": []
  },
  "timestamp": "2025-06-25T07:00:00.228620Z"
}
```

- **Owner用户响应示例**:
```json
{
  "success": true,
  "message": "可赋予角色列表获取成功",
  "data": {
    "roles": {
      "api": ["member"]
    },
    "details": [
      {
        "id": 4,
        "name": "member",
        "guard_name": "api",
        "organisation_id": 1,
        "created_at": "2025-06-25T00:51:23.000000Z",
        "updated_at": "2025-06-25T00:51:23.000000Z",
        "type": "organisation"
      }
    ]
  },
  "timestamp": "2025-06-25T07:00:00.228620Z"
}
```

#### 4.2 为用户分配角色
- **端点**: `POST /api/v1/users/{user}/roles`
- **描述**: 为指定用户分配角色
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以分配除Root外的所有角色
  - **组织所有者（Owner）**: 只能在自己的组织内分配Member角色
- **请求参数**:
  - `role_id` (必填): 角色ID（通过获取可分配角色接口获得）
- **请求示例**:
```json
{
  "role_id": 4
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "角色分配成功",
  "data": {
    "user_id": 2,
    "role": {
      "id": 4,
      "name": "member",
      "organisation_id": 1
    }
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 4.3 移除用户角色
- **端点**: `DELETE /api/v1/users/{user}/roles/{role}`
- **描述**: 移除用户的指定角色
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以移除除Root外的所有角色
  - **组织所有者（Owner）**: 只能移除自己组织内的Member角色
- **响应示例**:
```json
{
  "success": true,
  "message": "角色移除成功",
  "data": {
    "user_id": 2,
    "role_id": 4,
    "role_name": "member"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 4.4 获取用户角色信息
- **端点**: `GET /api/v1/users/{user}/roles`
- **描述**: 获取指定用户的详细角色信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看任何用户的角色
  - **组织用户（Owner/Member）**: 只能查看同组织用户的角色
- **响应示例**:
```json
{
  "success": true,
  "message": "用户角色信息获取成功",
  "data": {
    "user_id": 2,
    "user_name": "John Doe",
    "user_email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "roles": {
      "system_roles": [],
      "organisation_roles": [
        {
          "id": 4,
          "name": "member",
          "guard_name": "api",
          "organisation_id": 1,
          "organisation_name": "Tech Solutions Ltd"
        }
      ],
      "all_role_names": ["member"]
    }
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 4.5 转移所有者角色
- **端点**: `PUT /api/v1/users/{user}/transfer-owner`
- **描述**: 将组织所有者角色转移给指定用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以转移任何组织的所有者角色
  - **组织所有者（Owner）**: 只能转移自己拥有的组织的所有者角色
- **请求参数**:
  - `organisation_id` (必填): 要转移所有者角色的组织ID
- **请求示例**:
```json
{
  "organisation_id": 1
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "所有者角色转移成功",
  "data": {
    "new_owner": {
      "id": 2,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001"
    },
    "previous_owner": {
      "id": 1,
      "name": "Previous Owner",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

### 5. 组织管理

#### 5.1 获取组织列表
- **端点**: `GET /api/v1/organisations`
- **描述**: 获取组织列表，支持分页和状态筛选
- **认证**: 需要Bearer Token
- **查询参数**:
  - `per_page` (可选): 每页数量，默认15
  - `status` (可选): 状态筛选 (pending, active, suspended)
- **响应示例**:
```json
{
  "success": true,
  "message": "获取组织列表成功",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "details": {
          "industry": "Technology",
          "size": "Medium",
          "founded": 2020
        },
        "remarks": "Leading technology solutions provider",
        "status": "active",
        "is_active": true,
        "is_pending": false,
        "is_suspended": false,
        "users_count": 5,
        "created_at": "2025-06-06T06:56:33.000000Z",
        "updated_at": "2025-06-06T06:56:33.000000Z"
      }
    ],
    "meta": {
      "total": 1,
      "per_page": 15,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 1
    }
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 5.2 创建组织
- **端点**: `POST /api/v1/organisations`
- **描述**: 创建新组织
- **认证**: 需要Bearer Token
- **请求参数**:
  - `name` (必填): 组织名称
  - `code` (必填): 组织代码，必须唯一
  - `details` (可选): 详细信息，JSON格式
  - `remarks` (可选): 备注
  - `status` (可选): 状态 (pending, active, suspended)，默认为pending
- **响应示例**:
```json
{
  "success": true,
  "message": "组织创建成功",
  "data": {
    "id": 1,
    "name": "Test Organisation",
    "code": "TEST001",
    "details": {
      "industry": "Technology"
    },
    "remarks": "Test remarks",
    "status": "pending",
    "is_active": false,
    "is_pending": true,
    "is_suspended": false,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 5.3 获取组织详情
- **端点**: `GET /api/v1/organisations/{id}`
- **描述**: 获取指定组织的详细信息
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "获取组织详情成功",
  "data": {
    "id": 1,
    "name": "Tech Solutions Ltd",
    "code": "TECH001",
    "details": {
      "industry": "Technology",
      "size": "Medium",
      "founded": 2020
    },
    "remarks": "Leading technology solutions provider",
    "status": "active",
    "is_active": true,
    "is_pending": false,
    "is_suspended": false,
    "users_count": 5,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 5.4 更新组织
- **端点**: `PUT /api/v1/organisations/{id}`
- **描述**: 更新组织信息
- **认证**: 需要Bearer Token
- **请求参数**: 所有参数都是可选的
  - `name`: 组织名称
  - `code`: 组织代码，必须唯一
  - `details`: 详细信息，JSON格式
  - `remarks`: 备注
  - `status`: 状态 (pending, active, suspended)
- **响应示例**:
```json
{
  "success": true,
  "message": "组织更新成功",
  "data": {
    "id": 1,
    "name": "Updated Organisation Name",
    "code": "UPD001",
    "details": {
      "industry": "Technology",
      "size": "Large"
    },
    "remarks": "Updated remarks",
    "status": "active",
    "is_active": true,
    "is_pending": false,
    "is_suspended": false,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 5.5 暂停组织
- **端点**: `POST /api/v1/organisations/{id}/suspend`
- **描述**: 暂停组织（将状态设置为suspended）
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "组织暂停成功",
  "data": {
    "id": 1,
    "name": "Tech Solutions Ltd",
    "code": "TECH001",
    "status": "suspended",
    "is_active": false,
    "is_pending": false,
    "is_suspended": true,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 组织状态说明
- **pending**: 由系统管理员新建，给所有者邮箱发送了邀请邮件，但所有者尚未完成注册
- **active**: 所有者已经通过邮件中的邀请链接成功入驻，成为了该组织的所有者
- **suspended**: 该组织已被系统管理员停用，该组织所属的普通用户无法执行任何操作

### 6. 角色管理

#### 6.1 获取角色列表
- **端点**: `GET /api/v1/roles`
- **描述**: 获取当前用户组织的所有角色
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "root",
      "guard_name": "api",
      "organisation_id": 1,
      "created_at": "2025-06-06T06:56:33.000000Z",
      "updated_at": "2025-06-06T06:56:33.000000Z"
    },
    {
      "id": 2,
      "name": "admin",
      "guard_name": "api",
      "organisation_id": 1,
      "created_at": "2025-06-06T06:56:33.000000Z",
      "updated_at": "2025-06-06T06:56:33.000000Z"
    }
  ],
  "message": "Roles retrieved successfully",
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 6.2 创建角色
- **端点**: `POST /api/v1/roles`
- **描述**: 创建新角色
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **请求参数**:
  - `name` (必填): 角色名称
  - `guard_name` (可选): 守卫名称，默认为'api'
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 5,
    "name": "custom-role",
    "guard_name": "api",
    "organisation_id": 1,
    "created_at": "2025-06-06T07:00:00.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "message": "Role created successfully",
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 6.3 获取角色详情
- **端点**: `GET /api/v1/roles/{id}`
- **描述**: 获取指定角色的详细信息
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "root",
    "guard_name": "api",
    "organisation_id": 1,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "message": "Role retrieved successfully",
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 6.4 更新角色
- **端点**: `PUT /api/v1/roles/{id}`
- **描述**: 更新角色信息
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **请求参数**:
  - `name` (可选): 角色名称
  - `guard_name` (可选): 守卫名称
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "updated-role",
    "guard_name": "api",
    "organisation_id": 1,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "message": "Role updated successfully",
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 6.5 删除角色
- **端点**: `DELETE /api/v1/roles/{id}`
- **描述**: 删除指定角色
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **响应示例**:
```json
{
  "success": true,
  "message": "Role deleted successfully",
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 角色说明
系统预定义了以下四种角色：
- **Root**: 系统初始化时设置的第一个管理员账户，拥有最高权限
- **管理员 (Admin)**: 系统管理员，拥有管理权限
- **组织所有者 (Owner)**: 组织的所有者，拥有组织内的管理权限
- **成员 (Member)**: 普通用户，拥有基本权限

#### 权限模型说明

系统采用基于角色的访问控制（RBAC），不同角色对用户管理功能有不同的权限：

##### 系统管理员权限（Root/Admin）
- 可以访问和管理所有用户
- 可以在任何组织中创建、更新、暂停、激活用户
- 可以管理用户与组织的关联关系
- 不受组织边界限制

##### 组织所有者权限（Owner）
- 只能访问和管理自己组织内的用户
- 可以在自己的组织中创建新用户
- 可以更新、暂停、激活同组织的用户
- **可以管理用户与自己拥有的组织的关联关系（添加/移除用户）**
- 无法访问其他组织的用户

##### 组织成员权限（Member）
- 只能查看同组织的用户列表和详情
- 无法进行用户管理操作（创建、更新、暂停等）
- **无法管理用户与组织的关联关系（不能添加/移除用户）**

##### 权限验证机制
- 系统会自动验证当前用户的角色和权限
- 组织用户只能操作与自己有共同组织的用户
- 跨组织访问会被自动拒绝并返回403错误
- 权限不足时会返回422验证错误

### 7. 活动日志管理

活动日志管理功能允许系统管理员查看和监控系统中的各种操作活动，包括用户创建、更新、删除等操作的详细记录。

#### 7.1 获取活动日志列表
- **端点**: `GET /api/v1/activity-logs`
- **描述**: 获取系统活动日志列表，支持分页和多种筛选条件
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **查询参数**:
  - `page` (可选): 页码，整数，最小值1，默认1
  - `per_page` (可选): 每页数量，整数，范围1-100，默认15
  - `log_name` (可选): 日志名称筛选，字符串，最大255字符
  - `subject_type` (可选): 主题类型筛选，可选值：
    - `App\Models\User` - 用户相关操作
    - `App\Models\Organisation` - 组织相关操作
    - `App\Models\Invitation` - 邀请相关操作
    - `App\Models\Role` - 角色相关操作
    - `App\Models\ProductPermission` - 产品权限相关操作
    - `App\Models\SyncLog` - 同步日志相关操作
  - `causer_id` (可选): 操作者ID筛选，字符串，最大255字符
  - `event` (可选): 事件类型筛选，可选值：created, updated, deleted
  - `date_from` (可选): 开始日期筛选，格式：YYYY-MM-DD，必须早于或等于date_to
  - `date_to` (可选): 结束日期筛选，格式：YYYY-MM-DD，必须晚于或等于date_from，不能晚于今天
- **请求示例**:
```
GET /api/v1/activity-logs?subject_type=App\Models\User&event=created&date_from=2025-07-01&date_to=2025-07-13&per_page=20&page=1
```
- **响应示例**:
```json
{
  "success": true,
  "message": "活动日志列表获取成功",
  "data": {
    "data": [
      {
        "id": 1,
        "log_name": "jast_partner_activity",
        "description": "created",
        "subject_type": "App\\Models\\User",
        "subject_id": "123",
        "subject": {
          "id": "123",
          "type": "User",
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "causer_type": "App\\Models\\User",
        "causer_id": "1",
        "causer": {
          "id": "1",
          "name": "Admin User",
          "email": "<EMAIL>"
        },
        "properties": {
          "attributes": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "status": "active"
          }
        },
        "event": "created",
        "batch_uuid": null,
        "created_at": "2025-07-13T10:30:00.000000Z"
      }
    ],
    "meta": {
      "total": 150,
      "per_page": 20,
      "current_page": 1,
      "last_page": 8,
      "from": 1,
      "to": 20
    }
  },
  "timestamp": "2025-07-13T12:00:00.000000Z"
}
```

#### 活动日志字段说明
- **id**: 日志记录唯一标识符
- **log_name**: 日志名称，默认为 "jast_partner_activity"
- **description**: 操作描述，通常与event字段相同
- **subject_type**: 被操作对象的模型类型
- **subject_id**: 被操作对象的ID
- **subject**: 被操作对象的详细信息（如果对象仍存在）
- **causer_type**: 操作者的模型类型，通常为 "App\\Models\\User"
- **causer_id**: 操作者的用户ID
- **causer**: 操作者的详细信息
- **properties**: 操作相关的属性信息，包含操作前后的数据变化
- **event**: 事件类型（created, updated, deleted）
- **batch_uuid**: 批处理UUID，用于关联批量操作
- **created_at**: 日志创建时间

#### 权限控制说明
- **系统管理员权限（Root/Admin）**: 可以查看所有活动日志
- **其他用户**: 无权限访问活动日志功能
- **安全特性**: 通过AdminAccessMiddleware中间件确保只有系统管理员可以访问

### 8. 邀请管理

邀请功能允许组织管理员创建邀请链接，让用户通过链接加入组织并获得相应角色。

#### 8.1 获取邀请列表
- **端点**: `GET /api/v1/invitations`
- **描述**: 获取邀请列表，支持分页和筛选
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看所有邀请
  - **组织所有者（Owner）**: 只能查看自己创建的邀请
- **查询参数**:
  - `per_page` (可选): 每页数量，默认15，最大100
  - `model_type` (可选): 关联模型类型筛选
  - `model_id` (可选): 关联模型ID筛选
  - `role` (可选): 角色筛选
  - `status` (可选): 状态筛选
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitations retrieved successfully",
  "data": {
    "data": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "model_type": "App\\Models\\Organisation",
        "model_id": 1,
        "role": "member",
        "created_by_user_id": 1,
        "expires_at": "2025-06-25T12:00:00.000000Z",
        "max_uses": 5,
        "uses": 2,
        "email_restriction": null,
        "created_at": "2025-06-18T12:00:00.000000Z",
        "updated_at": "2025-06-18T12:00:00.000000Z",
        "model": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001",
          "status": "active"
        },
        "created_by": {
          "id": 1,
          "name": "Admin User",
          "email": "<EMAIL>"
        }
      }
    ],
    "meta": {
      "total": 1,
      "per_page": 15,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 1
    }
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```

#### 8.2 创建邀请
- **端点**: `POST /api/v1/invitations`
- **描述**: 创建新的邀请链接
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以为任何组织创建邀请
  - **组织所有者（Owner）**: 只能为自己拥有的组织创建邀请
- **请求参数**:
  - `model_type` (必填): 关联模型类型，目前支持 "App\Models\Organisation"
  - `model_id` (必填): 关联的组织ID
  - `role` (必填): 邀请角色，支持 "owner", "member"
  - `expires_at` (可选): 过期时间，必须是未来时间
  - `max_uses` (可选): 最大使用次数，1-100之间，默认为1
  - `email_restriction` (可选): 邮箱限制，只有指定邮箱才能使用邀请
- **请求示例**:
```json
{
  "model_type": "App\\Models\\Organisation",
  "model_id": 1,
  "role": "member",
  "expires_at": "2025-06-25T12:00:00Z",
  "max_uses": 10,
  "email_restriction": "<EMAIL>"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitation created successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "model_type": "App\\Models\\Organisation",
    "model_id": 1,
    "role": "member",
    "created_by_user_id": 1,
    "expires_at": "2025-06-25T12:00:00.000000Z",
    "max_uses": 10,
    "uses": 0,
    "email_restriction": "<EMAIL>",
    "created_at": "2025-06-18T12:00:00.000000Z",
    "updated_at": "2025-06-18T12:00:00.000000Z",
    "model": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "created_by": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```

#### 8.3 查看邀请详情
- **端点**: `GET /api/v1/invitations/{id}`
- **描述**: 查看邀请详细信息，无需认证即可访问
- **认证**: 无需认证（公开访问）
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitation information retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "model_type": "App\\Models\\Organisation",
    "model_id": 1,
    "role": "member",
    "created_by_user_id": 1,
    "expires_at": "2025-06-25T12:00:00.000000Z",
    "max_uses": 10,
    "uses": 2,
    "email_restriction": null,
    "created_at": "2025-06-18T12:00:00.000000Z",
    "updated_at": "2025-06-18T12:00:00.000000Z",
    "model": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "created_by": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```
- **错误响应**:
  - **410 Gone**: 邀请已过期或达到使用上限
  ```json
  {
    "success": false,
    "message": "Invitation link has expired",
    "timestamp": "2025-06-18T12:00:00.000000Z"
  }
  ```

#### 8.4 接受邀请
- **端点**: `POST /api/v1/invitations/{id}/accept`
- **描述**: 接受邀请并加入组织
- **认证**: 需要Bearer Token
- **权限要求**: 已认证用户
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitation accepted successfully, joined organization",
  "data": {
    "user": {
      "id": 2,
      "name": "New User",
      "email": "<EMAIL>",
      "email_verified_at": "2025-06-18T12:00:00.000000Z"
    },
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "role": "member"
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```
- **错误响应**:
  - **401 Unauthorized**: 用户未登录
  - **403 Forbidden**: 权限不足或邮箱不符合限制
  - **410 Gone**: 邀请已过期或达到使用上限
  - **422 Validation Error**: 邀请验证失败

#### 邀请使用流程

1. **管理员创建邀请**
   - 组织管理员或系统管理员调用创建邀请API
   - 系统生成UUID作为邀请ID
   - 返回邀请信息，包含邀请链接

2. **用户访问邀请链接**
   - 用户访问邀请页面（例如：`/join?id=xxx`）
   - 前端调用 `/api/v1/invitations/{id}` 获取邀请信息
   - 系统返回邀请详情（无需登录即可查看基本信息）

3. **用户接受邀请**
   - 用户点击接受邀请按钮（需要先登录）
   - 前端调用 `/api/v1/invitations/{id}/accept`
   - 系统验证邀请有效性
   - 将用户添加到组织并分配角色
   - 设置用户邮箱为已验证状态
   - 增加邀请使用次数
   - 返回成功信息和用户角色信息

#### 安全特性

1. **系统角色保护**: 不允许通过邀请链接分配系统角色（root, admin）
2. **邮箱限制**: 可以限制特定邮箱才能使用邀请
3. **过期时间**: 邀请有过期时间限制
4. **使用次数限制**: 可以限制邀请的最大使用次数
5. **权限验证**: 只有有权限的用户才能创建邀请

### 9. 数据同步管理

数据同步功能允许系统管理员管理与外部系统（如Sylius电商系统）的数据同步操作，包括查看同步日志、手动触发同步和重试失败的同步。

#### 9.1 获取同步日志列表
- **端点**: `GET /api/v1/sync/logs`
- **描述**: 获取同步日志列表，支持分页和筛选
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **查询参数**:
  - `per_page` (可选): 每页数量，默认15，最大100
  - `sync_type` (可选): 同步类型筛选
  - `status` (可选): 状态筛选（pending, running, completed, failed）
- **响应示例**:
```json
{
  "success": true,
  "message": "同步日志获取成功",
  "data": {
    "data": [
      {
        "id": 1,
        "sync_type": "product_sync",
        "batch_id": "batch_20250624_001",
        "status": "completed",
        "total_records": 1500,
        "success_records": 1450,
        "failed_records": 50,
        "progress_percentage": 100,
        "started_at": "2025-06-24T10:00:00.000000Z",
        "completed_at": "2025-06-24T10:15:30.000000Z",
        "created_at": "2025-06-24T10:00:00.000000Z",
        "updated_at": "2025-06-24T10:15:30.000000Z",
        "records": [
          {
            "id": 1,
            "status": "failed",
            "error_message": "Product variant not found",
            "data": {
              "product_id": 123,
              "variant_id": 456
            },
            "created_at": "2025-06-24T10:05:00.000000Z"
          }
        ]
      }
    ],
    "meta": {
      "total": 25,
      "per_page": 15,
      "current_page": 1,
      "last_page": 2,
      "from": 1,
      "to": 15
    }
  },
  "timestamp": "2025-06-24T12:00:00.000000Z"
}
```

#### 9.2 查看同步日志详情
- **端点**: `GET /api/v1/sync/logs/{id}`
- **描述**: 查看指定同步日志的详细信息
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **响应示例**:
```json
{
  "success": true,
  "message": "同步日志详情获取成功",
  "data": {
    "id": 1,
    "sync_type": "product_sync",
    "batch_id": "batch_20250624_001",
    "status": "completed",
    "status_label": "已完成",
    "total_records": 1500,
    "processed_records": 1500,
    "success_records": 1450,
    "failed_records": 50,
    "skipped_records": 0,
    "progress_percentage": 100,
    "started_at": "2025-06-24T10:00:00.000000Z",
    "completed_at": "2025-06-24T10:15:30.000000Z",
    "duration": "15.5m",
    "summary": {
      "total_processed": 1500,
      "success_rate": 96.67
    },
    "sync_config": {
      "incremental": true,
      "batch_size": 100
    },
    "validation_results": null,
    "error_message": null,
    "created_at": "2025-06-24T10:00:00.000000Z",
    "updated_at": "2025-06-24T10:15:30.000000Z"
  },
  "timestamp": "2025-06-24T12:00:00.000000Z"
}
```

#### 9.3 手动触发同步
- **端点**: `POST /api/v1/sync/trigger`
- **描述**: 手动触发新的数据同步操作
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **请求参数**:
  - `sync_type` (必填): 同步类型，可选值：product_sync, order_sync
  - `incremental` (可选): 是否增量同步，布尔值，默认false（全量同步）
  - `batch_size` (可选): 批处理大小，整数，范围1-1000，默认100
  - `timeout` (可选): 超时时间（秒），整数，范围60-3600，默认1800
- **请求示例**:

**产品同步示例**:
```json
{
  "sync_type": "product_sync",
  "incremental": false,
  "batch_size": 500,
  "timeout": 3600
}
```

**订单同步示例**:
```json
{
  "sync_type": "order_sync",
  "incremental": true,
  "batch_size": 200,
  "timeout": 1800
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "同步已触发",
  "data": {
    "batch_id": "batch_20250624_002",
    "sync_type": "product_sync",
    "status": "queued",
    "message": "同步操作已启动"
  },
  "timestamp": "2025-06-24T12:00:00.000000Z"
}
```
- **错误响应**:
  - **409 冲突**: 已有同步任务正在运行
  ```json
  {
    "success": false,
    "message": "已有同步任务正在运行",
    "errors": {
      "active_jobs": ["job_id_1", "job_id_2"]
    },
    "timestamp": "2025-06-24T12:00:00.000000Z"
  }
  ```
  - **422 验证失败**: 参数验证错误
  - **500 服务器错误**: 同步服务启动失败

#### 9.4 重试失败的同步
- **端点**: `POST /api/v1/sync/retry/{id}`
- **描述**: 重试指定的失败同步操作
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **响应示例**:
```json
{
  "success": true,
  "message": "同步重试已启动",
  "data": {
    "new_batch_id": "batch_20250624_003",
    "original_batch_id": "batch_20250624_001",
    "sync_type": "order_sync",
    "status": "queued",
    "message": "重试操作已启动"
  },
  "timestamp": "2025-06-24T12:00:00.000000Z"
}
```
- **错误响应**:
  - **409 冲突**: 已有同步任务正在运行
  ```json
  {
    "success": false,
    "message": "已有同步任务正在运行",
    "errors": {
      "active_jobs": ["job_id_1", "job_id_2"]
    },
    "timestamp": "2025-06-24T12:00:00.000000Z"
  }
  ```
  - **422 验证失败**: 同步状态不是failed，无法重试
  ```json
  {
    "success": false,
    "message": "只能重试失败的同步操作",
    "errors": {
      "current_status": "completed"
    },
    "timestamp": "2025-06-24T12:00:00.000000Z"
  }
  ```
  - **500 服务器错误**: 重试服务启动失败

#### 9.5 获取同步进度
- **端点**: `GET /api/v1/sync/progress`
- **描述**: 根据任务ID或批次ID获取同步进度信息
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **查询参数**:
  - `job_id` (可选): 任务ID，与batch_id二选一
  - `batch_id` (可选): 批次ID，与job_id二选一
- **状态说明**:
  - `pending`: 任务已创建，等待开始
  - `processing`: 数据同步进行中
  - `validating`: 数据同步完成，验证进行中
  - `completed`: 同步和验证都已完成
  - `failed`: 同步或验证失败
- **请求示例**:
```
GET /api/v1/sync/progress?batch_id=batch_20250624_002
```
- **响应示例**:

**同步进行中**:
```json
{
  "success": true,
  "message": "同步进度获取成功",
  "data": {
    "job_id": "job_12345",
    "batch_id": "batch_20250624_002",
    "status": "processing",
    "percentage": 65,
    "total_records": 1000,
    "processed_records": 650,
    "success_records": 620,
    "failed_records": 30,
    "started_at": "2025-06-24T14:00:00.000000Z",
    "updated_at": "2025-06-24T14:05:00.000000Z"
  },
  "timestamp": "2025-06-24T14:05:00.000000Z"
}
```

**验证进行中**:
```json
{
  "success": true,
  "message": "同步进度获取成功",
  "data": {
    "job_id": "job_12345",
    "batch_id": "batch_20250624_002",
    "status": "validating",
    "percentage": 95,
    "total_records": 1000,
    "processed_records": 1000,
    "success_records": 950,
    "failed_records": 50,
    "sync_log_id": 123,
    "started_at": "2025-06-24T14:00:00.000000Z",
    "sync_completed_at": "2025-06-24T14:08:00.000000Z",
    "validation_job_id": "validation_job_456",
    "validation_started_at": "2025-06-24T14:08:30.000000Z",
    "updated_at": "2025-06-24T14:09:00.000000Z"
  },
  "timestamp": "2025-06-24T14:09:00.000000Z"
}
```

**完成状态**:
```json
{
  "success": true,
  "message": "同步进度获取成功",
  "data": {
    "job_id": "job_12345",
    "batch_id": "batch_20250624_002",
    "status": "completed",
    "percentage": 100,
    "total_records": 1000,
    "processed_records": 1000,
    "success_records": 950,
    "failed_records": 50,
    "sync_log_id": 123,
    "started_at": "2025-06-24T14:00:00.000000Z",
    "sync_completed_at": "2025-06-24T14:08:00.000000Z",
    "validation_started_at": "2025-06-24T14:08:30.000000Z",
    "validation_results": {
      "overall_status": "passed",
      "data_integrity": {
        "status": "passed",
        "local_count": 1000,
        "remote_count": 1000
      },
      "sampling_validation": {
        "status": "passed",
        "sample_size": 10,
        "passed_samples": 10
      }
    },
    "completed_at": "2025-06-24T14:10:00.000000Z",
    "updated_at": "2025-06-24T14:10:00.000000Z"
  },
  "timestamp": "2025-06-24T14:10:00.000000Z"
}
```
- **错误响应**:
  - **404 未找到**: 进度信息不存在
  ```json
  {
    "success": false,
    "message": "同步进度信息未找到",
    "errors": {},
    "timestamp": "2025-06-24T14:05:00.000000Z"
  }
  ```
  - **422 验证失败**: 参数验证错误
  - **500 服务器错误**: 进度获取失败

#### 9.6 获取活跃同步任务
- **端点**: `GET /api/v1/sync/active-jobs`
- **描述**: 获取当前所有活跃的同步任务列表
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **响应示例**:
```json
{
  "success": true,
  "message": "活跃同步任务获取成功",
  "data": {
    "active_jobs": [
      {
        "job_id": "job_12345",
        "batch_id": "batch_20250624_002",
        "sync_type": "product_sync",
        "status": "running",
        "progress_percentage": 65,
        "started_at": "2025-06-24T14:00:00.000000Z"
      },
      {
        "job_id": "job_12346",
        "batch_id": "batch_20250624_003",
        "sync_type": "inventory_sync",
        "status": "pending",
        "progress_percentage": 0,
        "started_at": null
      }
    ],
    "count": 2
  },
  "timestamp": "2025-06-24T14:05:00.000000Z"
}
```
- **错误响应**:
  - **500 服务器错误**: 活跃任务获取失败

#### 9.7 清理卡住的同步任务
- **端点**: `POST /api/v1/sync/cleanup-job`
- **描述**: 清理卡住的同步任务，释放资源
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **请求参数**:
  - `job_id` (必填): 任务ID，UUID格式
- **请求示例**:
```json
{
  "job_id": "550e8400-e29b-41d4-a716-************"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "同步任务已清理",
  "data": {
    "job_id": "550e8400-e29b-41d4-a716-************",
    "batch_id": "batch_20250624_002",
    "original_status": "stuck",
    "message": "任务已成功清理"
  },
  "timestamp": "2025-06-24T15:00:00.000000Z"
}
```
- **错误响应**:
  - **404 未找到**: 任务不存在
  - **500 服务器错误**: 清理失败

#### 9.8 获取同步任务详情
- **端点**: `GET /api/v1/sync/job-details`
- **描述**: 获取指定同步任务的详细信息
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **查询参数**:
  - `job_id` (必填): 任务ID，UUID格式
- **请求示例**:
```
GET /api/v1/sync/job-details?job_id=550e8400-e29b-41d4-a716-************
```
- **响应示例**:
```json
{
  "success": true,
  "message": "同步任务详情获取成功",
  "data": {
    "job_details": {
      "job_id": "550e8400-e29b-41d4-a716-************",
      "batch_id": "batch_20250624_002",
      "sync_type": "order_sync",
      "status": "processing",
      "progress_percentage": 75,
      "started_at": "2025-06-24T14:00:00.000000Z",
      "updated_at": "2025-06-24T14:30:00.000000Z",
      "total_records": 1000,
      "processed_records": 750,
      "success_records": 720,
      "failed_records": 30
    }
  },
  "timestamp": "2025-06-24T14:30:00.000000Z"
}
```
- **错误响应**:
  - **404 未找到**: 任务不存在
  - **500 服务器错误**: 获取失败

#### 9.9 重新验证订单同步
- **端点**: `POST /api/v1/sync/revalidate/{id}`
- **描述**: 重新验证已完成但验证失败的订单同步
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **路径参数**:
  - `id`: 同步日志ID
- **条件要求**:
  - 同步类型必须是 `order_sync`
  - 同步状态必须是 `completed`
  - 验证结果必须存在且状态为 `failed` 或 `error`
  - 当前没有活跃的验证任务
- **响应示例**:
```json
{
  "success": true,
  "message": "重新验证已启动",
  "data": {
    "sync_log_id": 123,
    "batch_id": "batch_20250624_002",
    "validation_job_id": "validation_job_789",
    "status": "queued",
    "message": "验证任务已启动"
  },
  "timestamp": "2025-06-24T15:00:00.000000Z"
}
```
- **错误响应**:
  - **400 错误请求**: 同步类型不正确、状态不符合要求等
  - **409 冲突**: 已有验证任务正在运行
  - **500 服务器错误**: 验证启动失败

#### 9.10 批量重新验证订单同步
- **端点**: `POST /api/v1/sync/batch-revalidate`
- **描述**: 批量重新验证多个已完成但验证失败的订单同步
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **请求参数**:
  - `sync_log_ids` (必填): 同步日志ID数组，最少1个，最多50个
- **请求示例**:
```json
{
  "sync_log_ids": [123, 124, 125, 126]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "批量重新验证已完成",
  "data": {
    "successful": [
      {
        "sync_log_id": 123,
        "batch_id": "batch_20250624_002",
        "validation_job_id": "validation_job_789",
        "status": "queued"
      },
      {
        "sync_log_id": 124,
        "batch_id": "batch_20250624_003",
        "validation_job_id": "validation_job_790",
        "status": "queued"
      }
    ],
    "failed": [
      {
        "sync_log_id": 125,
        "batch_id": "batch_20250624_004",
        "reason": "validation_already_passed",
        "details": {
          "current_status": "passed"
        }
      }
    ],
    "total_requested": 3,
    "successful_count": 2,
    "failed_count": 1
  },
  "timestamp": "2025-06-24T15:00:00.000000Z"
}
```

#### 9.11 获取重新验证候选列表
- **端点**: `GET /api/v1/sync/revalidation-candidates`
- **描述**: 获取所有符合重新验证条件的同步日志列表
- **认证**: 需要Bearer Token
- **权限要求**: 仅限系统管理员（Root/Admin）访问
- **查询参数**:
  - `per_page` (可选): 每页数量，1-100之间，默认15
  - `page` (可选): 页码，默认1
- **响应示例**:
```json
{
  "success": true,
  "message": "重新验证候选列表获取成功",
  "data": {
    "sync_logs": {
      "data": [
        {
          "id": 123,
          "sync_type": "order_sync",
          "batch_id": "batch_20250624_002",
          "status": "completed",
          "completed_at": "2025-06-24T14:08:00.000000Z",
          "validation_results": {
            "overall_status": "failed",
            "failed_validations": ["data_integrity", "business_logic"]
          }
        }
      ],
      "links": {
        "first": "http://localhost/api/v1/sync/revalidation-candidates?page=1",
        "last": "http://localhost/api/v1/sync/revalidation-candidates?page=5",
        "prev": null,
        "next": "http://localhost/api/v1/sync/revalidation-candidates?page=2"
      }
    },
    "meta": {
      "current_page": 1,
      "per_page": 15,
      "total": 67,
      "last_page": 5,
      "from": 1,
      "to": 15
    },
    "summary": {
      "total_candidates": 67,
      "failed_validations": 45,
      "error_validations": 22
    }
  },
  "timestamp": "2025-06-24T15:00:00.000000Z"
}
```

#### 同步状态说明
- **pending**: 同步任务已创建，等待开始执行
- **running**: 同步任务正在执行中
- **completed**: 同步任务已完成（可能包含部分失败记录）
- **failed**: 同步任务执行失败，无法继续

#### 同步类型说明
- **product_sync**: 产品数据同步，从Sylius电商系统同步产品和变体信息
- **order_sync**: 订单数据同步，从Sylius电商系统同步订单信息，包含验证流程

### 10. 产品管理

产品管理功能提供对产品数据的只读访问。产品数据从外部系统（如Sylius电商系统）同步而来，不支持通过API进行编辑。访问权限基于组织成员身份和产品级权限控制。

#### 10.1 获取产品列表
- **端点**: `GET /api/v1/products`
- **描述**: 获取产品列表，支持分页和多种筛选条件
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看所有产品
  - **组织用户（Owner/Member）**: 只能查看自己组织的产品或有特定权限的产品
- **查询参数**:
  - `search` (可选): 搜索关键词，在产品代码、SKU和名称字段中搜索，字符串，最大255字符
  - `organisation_id` (可选): 组织ID筛选，整数，必须是用户所属的组织
  - `enabled` (可选): 启用状态筛选，可选值：true, false, 1, 0
  - `per_page` (可选): 每页数量，整数，范围1-100，默认15
  - `page` (可选): 页码，整数，最小值1，默认1
- **请求示例**:
```
GET /api/v1/products?search=热销&organisation_id=1&enabled=true&per_page=20&page=1
```
- **响应示例**:
```json
{
  "success": true,
  "message": "产品列表获取成功",
  "data": {
    "data": [
      {
        "id": 1001,
        "store_variant_id": "VAR_001",
        "code": "PROD001",
        "sku": "SKU001",
        "name": "热销产品A",
        "slug": "hot-product-a",
        "package": "标准包装",
        "enabled": true,
        "organisation": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001",
          "status": "active"
        },
        "created_at": "2025-07-10T12:00:00.000000Z",
        "updated_at": "2025-07-10T12:00:00.000000Z"
      }
    ],
    "meta": {
      "total": 150,
      "per_page": 20,
      "current_page": 1,
      "last_page": 8,
      "from": 1,
      "to": 20
    }
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 10.2 获取产品详情
- **端点**: `GET /api/v1/products/{id}`
- **描述**: 获取指定产品的详细信息，包括组织详情
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看任何产品
  - **组织用户（Owner/Member）**: 只能查看自己组织的产品或有特定权限的产品
- **路径参数**:
  - `id`: 产品ID，整数
- **响应示例**:
```json
{
  "success": true,
  "message": "产品详情获取成功",
  "data": {
    "id": 1001,
    "store_variant_id": "VAR_001",
    "code": "PROD001",
    "sku": "SKU001",
    "name": "热销产品A",
    "slug": "hot-product-a",
    "package": "标准包装",
    "enabled": true,
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active",
      "details": {
        "industry": "Technology",
        "size": "Medium"
      }
    },
    "created_at": "2025-07-10T12:00:00.000000Z",
    "updated_at": "2025-07-10T12:00:00.000000Z"
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 9.3 获取用户可访问的产品列表
- **端点**: `GET /api/v1/products/accessible`
- **描述**: 获取当前用户有权限访问的产品列表
- **认证**: 需要Bearer Token
- **权限要求**: 已认证用户
- **响应示例**:
```json
{
  "success": true,
  "message": "可访问产品列表获取成功",
  "data": {
    "products": [
      {
        "id": 1001,
        "name": "热销产品A",
        "slug": "hot-product-a",
        "organisation": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001"
        }
      },
      {
        "id": 1002,
        "name": "热销产品B",
        "slug": "hot-product-b",
        "organisation": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001"
        }
      }
    ]
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 产品访问权限说明
- **组织成员权限**: 用户可以访问其所属组织的所有产品
- **产品级权限**: 用户可以通过产品权限管理功能获得特定产品的访问权限
- **系统管理员权限**: Root和Admin角色可以访问所有产品
- **数据同步**: 产品数据从外部系统同步，不支持通过API修改

### 11. 邮件模板管理

邮件模板管理功能允许系统管理员管理邮件模板，包括创建、编辑、删除和预览邮件模板。支持变量替换功能，可以动态生成邮件内容。

#### 11.1 获取邮件模板列表
- **端点**: `GET /api/v1/email-templates`
- **描述**: 获取邮件模板列表，支持分页、搜索和状态筛选
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员（Root/Admin）
- **查询参数**:
  - `search` (可选): 搜索关键词，在名称、代码、主题和描述字段中搜索，字符串，最大255字符
  - `is_active` (可选): 激活状态筛选，布尔值
  - `per_page` (可选): 每页数量，整数，范围1-100，默认15
  - `page` (可选): 页码，整数，最小值1，默认1
- **请求示例**:
```
GET /api/v1/email-templates?search=验证&is_active=true&per_page=20&page=1
```
- **响应示例**:
```json
{
  "success": true,
  "message": "邮件模板列表获取成功",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "用户邮箱验证",
        "code": "user_email_verification",
        "subject": "请验证您的邮箱地址",
        "content": "您好 {user_name}，请点击以下链接验证您的邮箱：{verification_link}",
        "variables": ["user_name", "verification_link"],
        "is_active": true,
        "description": "用户注册时的邮箱验证模板",
        "created_at": "2025-07-16T10:00:00.000000Z",
        "updated_at": "2025-07-16T10:00:00.000000Z",
        "required_variables": ["user_name", "verification_link"],
        "is_valid": true,
        "variable_count": 2
      }
    ],
    "meta": {
      "total": 5,
      "per_page": 20,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 5
    }
  },
  "timestamp": "2025-07-16T10:00:00.000000Z"
}
```

#### 11.2 创建邮件模板
- **端点**: `POST /api/v1/email-templates`
- **描述**: 创建新的邮件模板
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员（Root/Admin）
- **请求参数**:
  - `name` (必填): 模板名称，字符串，最大255字符，最少1字符
  - `code` (必填): 模板代码，字符串，最大100字符，最少1字符，只能包含小写字母、数字和下划线，必须唯一
  - `subject` (必填): 邮件主题，字符串，最大500字符，最少1字符
  - `content` (必填): 邮件内容，字符串，最少1字符
  - `variables` (可选): 变量数组，每个变量只能包含小写字母、数字和下划线，最大255字符
  - `is_active` (可选): 是否激活，布尔值，默认true
  - `description` (可选): 描述，字符串，最大1000字符
- **请求示例**:
```json
{
  "name": "组织邀请邮件",
  "code": "organization_invitation",
  "subject": "邀请您加入 {organization_name}",
  "content": "您好 {user_name}，\n\n{inviter_name} 邀请您加入组织 {organization_name}。\n\n请点击以下链接接受邀请：\n{invitation_link}\n\n此邀请将在 {expires_at} 过期。",
  "variables": ["user_name", "organization_name", "inviter_name", "invitation_link", "expires_at"],
  "is_active": true,
  "description": "组织邀请用户加入的邮件模板"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "邮件模板创建成功",
  "data": {
    "id": 2,
    "name": "组织邀请邮件",
    "code": "organization_invitation",
    "subject": "邀请您加入 {organization_name}",
    "content": "您好 {user_name}，\n\n{inviter_name} 邀请您加入组织 {organization_name}。\n\n请点击以下链接接受邀请：\n{invitation_link}\n\n此邀请将在 {expires_at} 过期。",
    "variables": ["user_name", "organization_name", "inviter_name", "invitation_link", "expires_at"],
    "is_active": true,
    "description": "组织邀请用户加入的邮件模板",
    "created_at": "2025-07-16T10:05:00.000000Z",
    "updated_at": "2025-07-16T10:05:00.000000Z",
    "required_variables": ["user_name", "organization_name", "inviter_name", "invitation_link", "expires_at"],
    "is_valid": true,
    "variable_count": 5
  },
  "timestamp": "2025-07-16T10:05:00.000000Z"
}
```

#### 11.3 获取邮件模板详情
- **端点**: `GET /api/v1/email-templates/{id}`
- **描述**: 获取指定邮件模板的详细信息
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员（Root/Admin）
- **路径参数**:
  - `id`: 模板ID，整数
- **响应示例**:
```json
{
  "success": true,
  "message": "邮件模板详情获取成功",
  "data": {
    "id": 1,
    "name": "用户邮箱验证",
    "code": "user_email_verification",
    "subject": "请验证您的邮箱地址",
    "content": "您好 {user_name}，请点击以下链接验证您的邮箱：{verification_link}",
    "variables": ["user_name", "verification_link"],
    "is_active": true,
    "description": "用户注册时的邮箱验证模板",
    "created_at": "2025-07-16T10:00:00.000000Z",
    "updated_at": "2025-07-16T10:00:00.000000Z",
    "required_variables": ["user_name", "verification_link"],
    "is_valid": true,
    "variable_count": 2
  },
  "timestamp": "2025-07-16T10:00:00.000000Z"
}
```

#### 11.4 更新邮件模板
- **端点**: `PUT /api/v1/email-templates/{id}`
- **描述**: 更新邮件模板信息
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员（Root/Admin）
- **路径参数**:
  - `id`: 模板ID，整数
- **请求参数**: 所有参数都是可选的，验证规则与创建时相同
  - `name`: 模板名称
  - `code`: 模板代码（必须唯一）
  - `subject`: 邮件主题
  - `content`: 邮件内容
  - `variables`: 变量数组
  - `is_active`: 是否激活
  - `description`: 描述
- **请求示例**:
```json
{
  "subject": "请验证您的邮箱地址 - {organization_name}",
  "content": "您好 {user_name}，\n\n欢迎加入 {organization_name}！请点击以下链接验证您的邮箱：\n{verification_link}\n\n如有疑问，请联系我们。",
  "variables": ["user_name", "organization_name", "verification_link"],
  "description": "用户注册时的邮箱验证模板（包含组织信息）"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "邮件模板更新成功",
  "data": {
    "id": 1,
    "name": "用户邮箱验证",
    "code": "user_email_verification",
    "subject": "请验证您的邮箱地址 - {organization_name}",
    "content": "您好 {user_name}，\n\n欢迎加入 {organization_name}！请点击以下链接验证您的邮箱：\n{verification_link}\n\n如有疑问，请联系我们。",
    "variables": ["user_name", "organization_name", "verification_link"],
    "is_active": true,
    "description": "用户注册时的邮箱验证模板（包含组织信息）",
    "created_at": "2025-07-16T10:00:00.000000Z",
    "updated_at": "2025-07-16T10:10:00.000000Z",
    "required_variables": ["user_name", "organization_name", "verification_link"],
    "is_valid": true,
    "variable_count": 3
  },
  "timestamp": "2025-07-16T10:10:00.000000Z"
}
```

#### 11.5 删除邮件模板
- **端点**: `DELETE /api/v1/email-templates/{id}`
- **描述**: 删除指定的邮件模板
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员（Root/Admin）
- **路径参数**:
  - `id`: 模板ID，整数
- **响应示例**:
```json
{
  "success": true,
  "message": "邮件模板删除成功",
  "data": null,
  "timestamp": "2025-07-16T10:15:00.000000Z"
}
```

#### 11.6 预览邮件模板
- **端点**: `POST /api/v1/email-templates/{id}/preview`
- **描述**: 使用提供的变量预览邮件模板渲染结果
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员（Root/Admin）
- **路径参数**:
  - `id`: 模板ID，整数
- **请求参数**:
  - `variables` (可选): 变量键值对，对象格式，每个值必须是字符串
- **请求示例**:
```json
{
  "variables": {
    "user_name": "张三",
    "organization_name": "科技有限公司",
    "verification_link": "https://example.com/verify?token=abc123"
  }
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "邮件模板预览生成成功",
  "data": {
    "template": {
      "id": 1,
      "name": "用户邮箱验证",
      "code": "user_email_verification"
    },
    "rendered": {
      "subject": "请验证您的邮箱地址 - 科技有限公司",
      "content": "您好 张三，\n\n欢迎加入 科技有限公司！请点击以下链接验证您的邮箱：\nhttps://example.com/verify?token=abc123\n\n如有疑问，请联系我们。"
    },
    "variables_used": {
      "user_name": "张三",
      "organization_name": "科技有限公司",
      "verification_link": "https://example.com/verify?token=abc123"
    },
    "required_variables": ["user_name", "organization_name", "verification_link"]
  },
  "timestamp": "2025-07-16T10:20:00.000000Z"
}
```
- **错误响应**:
  - **422 验证失败**: 缺少必需变量或变量格式错误
  ```json
  {
    "success": false,
    "message": "邮件模板预览失败",
    "errors": {
      "error": "Missing required variables: user_name, verification_link"
    },
    "timestamp": "2025-07-16T10:20:00.000000Z"
  }
  ```
  - **500 服务器错误**: 模板渲染失败
  ```json
  {
    "success": false,
    "message": "邮件模板预览失败",
    "errors": {
      "error": "Template rendering failed"
    },
    "timestamp": "2025-07-16T10:20:00.000000Z"
  }
  ```

#### 邮件模板变量说明
- **变量格式**: 在模板内容和主题中使用 `{variable_name}` 格式
- **变量命名**: 只能包含小写字母、数字和下划线
- **变量验证**: 系统会自动验证模板中使用的变量是否在variables数组中声明
- **必需变量**: 模板中使用的所有变量都必须在预览时提供值

#### 常用邮件模板类型
- **user_email_verification**: 用户邮箱验证模板
- **organization_invitation**: 组织邀请模板
- **password_reset**: 密码重置模板
- **welcome_message**: 欢迎消息模板
- **notification**: 通知消息模板

### 12. 产品权限管理

产品权限管理功能允许系统管理员和组织所有者为用户分配特定产品的访问权限，实现细粒度的权限控制。支持查看报表、编辑报表、导出报表等不同级别的权限类型。

#### 权限类型说明
- **view-reports**: 查看报表权限，允许用户查看产品相关的报表数据
- **edit-reports**: 编辑报表权限，允许用户修改报表配置和参数
- **export-reports**: 导出报表权限，允许用户导出报表数据

#### 权限控制说明
- **系统管理员（Root/Admin）**: 可以管理所有产品的权限
- **组织所有者（Owner）**: 只能管理自己组织内产品的权限
- **组织成员（Member）**: 无权限管理权限，只能查看自己的权限

#### 12.1 为产品授予用户访问权限
- **端点**: `POST /api/v1/products/{product}/permissions`
- **描述**: 为指定用户授予特定产品的访问权限
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员或产品所属组织的所有者
- **路径参数**:
  - `product`: 产品ID
- **请求参数**:
  - `user_id` (必填): 用户ID，整数
  - `permission_type` (必填): 权限类型，可选值：view-reports, edit-reports, export-reports
  - `expires_at` (可选): 权限过期时间，ISO 8601格式，必须是未来时间
  - `notes` (可选): 权限备注，字符串，最大500字符
- **请求示例**:
```json
{
  "user_id": 123,
  "permission_type": "view-reports",
  "expires_at": "2025-12-31T23:59:59Z",
  "notes": "临时报表查看权限"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Product access granted successfully",
  "data": {
    "permission": {
      "id": 1,
      "user_id": 123,
      "product_id": 1001,
      "permission_type": "view-reports",
      "expires_at": "2025-12-31T23:59:59.000000Z",
      "granted_at": "2025-07-10T12:00:00.000000Z",
      "granted_by": 1,
      "notes": "临时报表查看权限"
    }
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 12.2 撤销产品用户访问权限
- **端点**: `DELETE /api/v1/products/{product}/permissions/{user}`
- **描述**: 撤销指定用户对特定产品的访问权限
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员或产品所属组织的所有者
- **路径参数**:
  - `product`: 产品ID
  - `user`: 用户ID
- **请求参数**:
  - `permission_type` (必填): 要撤销的权限类型，可选值：view-reports, edit-reports, export-reports
- **请求示例**:
```json
{
  "permission_type": "view-reports"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Product access revoked successfully",
  "data": null,
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```
- **错误响应**:
  - **404 Not Found**: 未找到要撤销的权限
  ```json
  {
    "success": false,
    "message": "No permission found to revoke",
    "errors": null,
    "timestamp": "2025-07-10T12:00:00.000000Z"
  }
  ```

#### 12.3 获取产品授权用户列表
- **端点**: `GET /api/v1/products/{product}/authorized-users`
- **描述**: 获取有权限访问指定产品的用户列表
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员或产品所属组织的所有者
- **路径参数**:
  - `product`: 产品ID
- **查询参数**:
  - `permission_type` (可选): 权限类型筛选，默认：view-reports
- **请求示例**:
```
GET /api/v1/products/1001/authorized-users?permission_type=view-reports
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Authorized users retrieved successfully",
  "data": {
    "users": [
      {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      {
        "id": 124,
        "name": "Jane Smith",
        "email": "<EMAIL>"
      }
    ]
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 10.4 获取用户可访问的产品列表
- **端点**: `GET /api/v1/products/accessible`
- **描述**: 获取当前用户有权限访问的产品列表
- **认证**: 需要Bearer Token
- **权限要求**: 已认证用户
- **响应示例**:
```json
{
  "success": true,
  "message": "Accessible products retrieved successfully",
  "data": {
    "products": [
      {
        "id": 1001,
        "name": "热销产品A",
        "slug": "hot-product-a",
        "organisation": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001"
        }
      },
      {
        "id": 1002,
        "name": "热销产品B",
        "slug": "hot-product-b",
        "organisation": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001"
        }
      }
    ]
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 10.5 获取用户产品权限详情
- **端点**: `GET /api/v1/users/{user}/product-permissions`
- **描述**: 获取指定用户的产品权限详细信息
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员或同组织用户
- **路径参数**:
  - `user`: 用户ID
- **查询参数**:
  - `organisation_id` (可选): 组织ID，筛选特定组织的产品权限
- **请求示例**:
```
GET /api/v1/users/123/product-permissions?organisation_id=1
```
- **响应示例**:
```json
{
  "success": true,
  "message": "User product permissions retrieved successfully",
  "data": {
    "permissions": [
      {
        "id": 1,
        "product": {
          "id": 1001,
          "name": "热销产品A",
          "slug": "hot-product-a",
          "organisation": {
            "id": 1,
            "name": "Tech Solutions Ltd"
          }
        },
        "permission_type": "view-reports",
        "expires_at": "2025-12-31T23:59:59.000000Z",
        "granted_at": "2025-07-10T12:00:00.000000Z",
        "granted_by": {
          "id": 1,
          "name": "Admin User"
        },
        "notes": "临时报表查看权限"
      }
    ]
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 11. 财务报表管理

财务报表管理功能允许组织所有者和系统管理员创建、查看和管理财务报表。支持月度、季度、年度和自定义时间范围的报表类型，包含报表状态管理和统计功能。

#### 报表类型说明
- **monthly**: 月度报表，按月统计财务数据
- **quarterly**: 季度报表，按季度统计财务数据
- **yearly**: 年度报表，按年统计财务数据
- **custom**: 自定义报表，支持任意时间范围

#### 报表状态说明
- **pending_audit**: 待审计状态，报表已创建但尚未发布
- **published**: 已发布状态，报表已审核并正式发布

#### 权限控制说明
- **系统管理员（Root/Admin）**: 可以管理所有组织的财务报表
- **组织所有者（Owner）**: 只能管理自己组织的财务报表
- **组织成员（Member）**: 无权限管理财务报表

#### 11.1 获取财务报表列表
- **端点**: `GET /api/v1/financial-statements`
- **描述**: 获取财务报表列表，支持分页和多种筛选条件
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看所有财务报表
  - **组织用户（Owner/Member）**: 只能查看自己组织的财务报表
- **查询参数**:
  - `per_page` (可选): 每页数量，整数，范围1-100，默认15
  - `status` (可选): 报表状态筛选，可选值：pending_audit, published
  - `report_type` (可选): 报表类型筛选，可选值：monthly, quarterly, yearly, custom
  - `organisation_id` (可选): 组织ID筛选，整数，必须是用户所属的组织
  - `start_date` (可选): 开始日期筛选，日期格式：YYYY-MM-DD
  - `end_date` (可选): 结束日期筛选，日期格式：YYYY-MM-DD
- **请求示例**:
```
GET /api/v1/financial-statements?status=pending_audit&report_type=monthly&organisation_id=1&start_date=2024-01-01&end_date=2024-12-31&per_page=20
```
- **响应示例**:
```json
{
  "success": true,
  "message": "财务报表列表获取成功",
  "data": {
    "data": [
      {
        "id": 1,
        "report_type": "monthly",
        "report_code": "TECH00120241m",
        "report_title": "Tech Solutions Ltd 2024年1月 销售报表",
        "start_date": "2024-01-01",
        "end_date": "2024-01-31",
        "organisation": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001",
          "status": "active"
        },
        "status": "pending_audit",
        "total_amount": 100000,
        "total_quantity": 50,
        "total_orders": 10,
        "remarks": "月度财务报表",
        "created_at": "2024-02-01T12:00:00.000000Z",
        "updated_at": "2024-02-01T12:00:00.000000Z"
      }
    ],
    "meta": {
      "total": 25,
      "per_page": 20,
      "current_page": 1,
      "last_page": 2,
      "from": 1,
      "to": 20
    }
  },
  "timestamp": "2024-02-01T12:00:00.000000Z"
}
```

#### 11.2 创建财务报表
- **端点**: `POST /api/v1/financial-statements`
- **描述**: 创建新的财务报表
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以为任何组织创建财务报表
  - **组织所有者（Owner）**: 只能为自己的组织创建财务报表
- **请求参数**:
  - `report_type` (必填): 报表类型，可选值：monthly, quarterly, yearly, custom
  - `start_date` (必填): 开始日期，日期格式：YYYY-MM-DD
  - `end_date` (必填): 结束日期，日期格式：YYYY-MM-DD，必须大于等于开始日期
  - `organisation_id` (必填): 组织ID，整数，必须是用户有权限管理的组织
  - `status` (可选): 报表状态，可选值：pending_audit, published，默认为pending_audit
  - `total_amount` (可选): 总金额，整数，单位为分，最小值0
  - `total_quantity` (可选): 总数量，整数，最小值0
  - `total_orders` (可选): 总订单数，整数，最小值0
  - `remarks` (可选): 备注，字符串，最大65535字符
  - `order_ids` (可选): 关联订单ID数组，整数数组，订单必须存在且不能被同类型报表重复使用
- **请求示例**:
```json
{
  "report_type": "monthly",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "organisation_id": 1,
  "total_amount": 100000,
  "total_quantity": 50,
  "total_orders": 10,
  "remarks": "2024年1月月度财务报表",
  "order_ids": [1, 2, 3, 4, 5]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "财务报表创建成功",
  "data": {
    "id": 1,
    "report_type": "monthly",
    "report_code": "TECH00120241m",
    "report_title": "Tech Solutions Ltd 2024年1月 销售报表",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "status": "pending_audit",
    "total_amount": 100000,
    "total_quantity": 50,
    "total_orders": 10,
    "remarks": "2024年1月月度财务报表",
    "created_at": "2024-02-01T12:00:00.000000Z",
    "updated_at": "2024-02-01T12:00:00.000000Z"
  },
  "timestamp": "2024-02-01T12:00:00.000000Z"
}
```

#### 11.3 获取财务报表详情
- **端点**: `GET /api/v1/financial-statements/{id}`
- **描述**: 获取指定财务报表的详细信息，包括组织详情和关联订单
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看任何财务报表
  - **组织用户（Owner/Member）**: 只能查看自己组织的财务报表
- **路径参数**:
  - `id`: 财务报表ID，整数
- **响应示例**:
```json
{
  "success": true,
  "message": "财务报表详情获取成功",
  "data": {
    "id": 1,
    "report_type": "monthly",
    "report_code": "TECH00120241m",
    "report_title": "Tech Solutions Ltd 2024年1月 销售报表",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active",
      "details": {
        "industry": "Technology",
        "size": "Medium"
      }
    },
    "status": "pending_audit",
    "total_amount": 100000,
    "total_quantity": 50,
    "total_orders": 10,
    "remarks": "2024年1月月度财务报表",
    "orders": [
      {
        "id": 1,
        "order_number": "ORD-2024-001",
        "total_amount": 20000,
        "created_at": "2024-01-15T10:00:00.000000Z"
      },
      {
        "id": 2,
        "order_number": "ORD-2024-002",
        "total_amount": 30000,
        "created_at": "2024-01-20T14:30:00.000000Z"
      }
    ],
    "created_at": "2024-02-01T12:00:00.000000Z",
    "updated_at": "2024-02-01T12:00:00.000000Z"
  },
  "timestamp": "2024-02-01T12:00:00.000000Z"
}
```

#### 11.4 更新财务报表
- **端点**: `PUT /api/v1/financial-statements/{id}`
- **描述**: 更新现有财务报表信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以更新任何财务报表
  - **组织所有者（Owner）**: 只能更新自己组织的财务报表
- **路径参数**:
  - `id`: 财务报表ID，整数
- **请求参数**: 所有参数都是可选的
  - `report_type`: 报表类型，可选值：monthly, quarterly, yearly, custom
  - `start_date`: 开始日期，日期格式：YYYY-MM-DD
  - `end_date`: 结束日期，日期格式：YYYY-MM-DD，必须大于等于开始日期
  - `organisation_id`: 组织ID，整数，必须是用户有权限管理的组织
  - `status`: 报表状态，可选值：pending_audit, published（注意：已发布的报表不能改回待审计状态）
  - `total_amount`: 总金额，整数，单位为分，最小值0
  - `total_quantity`: 总数量，整数，最小值0
  - `total_orders`: 总订单数，整数，最小值0
  - `remarks`: 备注，字符串，最大65535字符
  - `order_ids`: 关联订单ID数组，整数数组，订单必须存在且不能被同类型报表重复使用
- **请求示例**:
```json
{
  "total_amount": 120000,
  "total_quantity": 60,
  "total_orders": 12,
  "remarks": "更新后的2024年1月月度财务报表"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "财务报表更新成功",
  "data": {
    "id": 1,
    "report_type": "monthly",
    "report_code": "TECH00120241m",
    "report_title": "Tech Solutions Ltd 2024年1月 销售报表",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "status": "pending_audit",
    "total_amount": 120000,
    "total_quantity": 60,
    "total_orders": 12,
    "remarks": "更新后的2024年1月月度财务报表",
    "orders": [],
    "created_at": "2024-02-01T12:00:00.000000Z",
    "updated_at": "2024-02-01T13:00:00.000000Z"
  },
  "timestamp": "2024-02-01T13:00:00.000000Z"
}
```

#### 11.5 发布财务报表
- **端点**: `POST /api/v1/financial-statements/{id}/publish`
- **描述**: 发布财务报表，将状态从待审计改为已发布
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以发布任何财务报表
  - **组织所有者（Owner）**: 只能发布自己组织的财务报表
- **路径参数**:
  - `id`: 财务报表ID，整数
- **请求体**: 空对象 `{}`
- **响应示例**:
```json
{
  "success": true,
  "message": "财务报表发布成功",
  "data": {
    "id": 1,
    "report_type": "monthly",
    "report_code": "TECH00120241m",
    "report_title": "Tech Solutions Ltd 2024年1月 销售报表",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "status": "published",
    "total_amount": 120000,
    "total_quantity": 60,
    "total_orders": 12,
    "remarks": "更新后的2024年1月月度财务报表",
    "created_at": "2024-02-01T12:00:00.000000Z",
    "updated_at": "2024-02-01T14:00:00.000000Z"
  },
  "timestamp": "2024-02-01T14:00:00.000000Z"
}
```
- **错误响应**:
  - **422 Unprocessable Entity**: 报表已经发布
  ```json
  {
    "success": false,
    "message": "财务报表已经发布",
    "errors": null,
    "timestamp": "2024-02-01T14:00:00.000000Z"
  }
  ```

#### 11.6 按组织获取财务报表
- **端点**: `GET /api/v1/financial-statements/organisation/{organisationId}`
- **描述**: 获取指定组织的财务报表列表
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看任何组织的财务报表
  - **组织用户（Owner/Member）**: 只能查看自己组织的财务报表
- **路径参数**:
  - `organisationId`: 组织ID，整数
- **查询参数**:
  - `per_page` (可选): 每页数量，整数，范围1-100，默认15
  - `status` (可选): 报表状态筛选，可选值：pending_audit, published
  - `report_type` (可选): 报表类型筛选，可选值：monthly, quarterly, yearly, custom
- **请求示例**:
```
GET /api/v1/financial-statements/organisation/1?status=published&report_type=monthly&per_page=20
```
- **响应示例**:
```json
{
  "success": true,
  "message": "组织财务报表列表获取成功",
  "data": {
    "data": [
      {
        "id": 1,
        "report_type": "monthly",
        "report_code": "TECH00120241m",
        "report_title": "Tech Solutions Ltd 2024年1月 销售报表",
        "start_date": "2024-01-01",
        "end_date": "2024-01-31",
        "status": "published",
        "total_amount": 120000,
        "total_quantity": 60,
        "total_orders": 12,
        "created_at": "2024-02-01T12:00:00.000000Z",
        "updated_at": "2024-02-01T14:00:00.000000Z"
      }
    ],
    "meta": {
      "total": 5,
      "per_page": 20,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 5
    }
  },
  "timestamp": "2024-02-01T15:00:00.000000Z"
}
```

#### 11.7 获取财务报表统计
- **端点**: `GET /api/v1/financial-statements/statistics`
- **描述**: 获取财务报表统计信息，包括总数、状态分布等
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看所有组织的统计信息
  - **组织用户（Owner/Member）**: 只能查看自己组织的统计信息
- **查询参数**:
  - `organisation_id` (可选): 组织ID，整数，指定查看特定组织的统计信息
- **请求示例**:
```
GET /api/v1/financial-statements/statistics
GET /api/v1/financial-statements/statistics?organisation_id=1
```
- **响应示例**:
```json
{
  "success": true,
  "message": "财务报表统计信息获取成功",
  "data": {
    "total_statements": 25,
    "status_distribution": {
      "pending_audit": 15,
      "published": 10
    },
    "type_distribution": {
      "monthly": 12,
      "quarterly": 8,
      "yearly": 3,
      "custom": 2
    },
    "total_amount": 2500000,
    "total_quantity": 1250,
    "total_orders": 500,
    "recent_statements": [
      {
        "id": 25,
        "report_type": "monthly",
        "report_title": "Tech Solutions Ltd 2024年12月 销售报表",
        "status": "pending_audit",
        "total_amount": 150000,
        "created_at": "2024-12-31T12:00:00.000000Z"
      },
      {
        "id": 24,
        "report_type": "monthly",
        "report_title": "Tech Solutions Ltd 2024年11月 销售报表",
        "status": "published",
        "total_amount": 140000,
        "created_at": "2024-11-30T12:00:00.000000Z"
      }
    ]
  },
  "timestamp": "2024-12-31T15:00:00.000000Z"
}
```

#### 财务报表代码格式说明

财务报表代码（report_code）根据报表类型自动生成，格式如下：

- **月度报表**: `{组织代码}{年月}m`
  - 示例: `TECH001202401m` (Tech Solutions Ltd 2024年1月月度报表)

- **季度报表**: `{组织代码}{年}{季度}q`
  - 示例: `TECH001202401q` (Tech Solutions Ltd 2024年第1季度报表)

- **年度报表**: `{组织代码}{年}y`
  - 示例: `TECH0012024y` (Tech Solutions Ltd 2024年年度报表)

- **自定义报表**: `{组织代码}{开始日期}_{结束日期}c`
  - 示例: `TECH00120240101_20240331c` (Tech Solutions Ltd 2024年1月1日至3月31日自定义报表)

#### 财务报表标题格式说明

财务报表标题（report_title）根据报表类型和时间范围自动生成，格式如下：

- **月度报表**: `{组织名称} {年}年{月}月 销售报表`
- **季度报表**: `{组织名称} {年}年第{季度}季度 销售报表`
- **年度报表**: `{组织名称} {年}年 销售报表`
- **自定义报表**: `{组织名称} {开始日期}至{结束日期} 销售报表`

#### 订单关联规则

- 同一报表类型的财务报表不能关联相同的订单
- 不同报表类型的财务报表可以关联相同的订单
- 例如：月度报表A和月度报表B不能包含相同订单，但月度报表A和季度报表B可以包含相同订单

#### 状态变更规则

- 新创建的财务报表默认状态为 `pending_audit`（待审计）
- 只能从 `pending_audit` 状态变更为 `published`（已发布）
- 已发布的报表不能改回待审计状态
- 发布操作需要特定权限，通常由系统管理员或组织所有者执行

### 10.6 批量授予产品权限
- **端点**: `POST /api/v1/users/{user}/product-permissions/grant-multiple`
- **描述**: 为用户批量授予多个产品的访问权限
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员或产品所属组织的所有者
- **路径参数**:
  - `user`: 用户ID
- **请求参数**:
  - `user_id` (必填): 用户ID，整数
  - `product_ids` (必填): 产品ID数组，最少1个，最多100个
  - `permission_type` (必填): 权限类型，可选值：view-reports, edit-reports, export-reports
  - `expires_at` (可选): 权限过期时间，ISO 8601格式
  - `notes` (可选): 权限备注，字符串，最大500字符
- **请求示例**:
```json
{
  "user_id": 123,
  "product_ids": [1001, 1002, 1003],
  "permission_type": "view-reports",
  "expires_at": "2025-12-31T23:59:59Z",
  "notes": "批量授予报表查看权限"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Multiple product access granted successfully",
  "data": {
    "granted_count": 3,
    "permissions": [
      {
        "id": 1,
        "product_id": 1001,
        "permission_type": "view-reports",
        "expires_at": "2025-12-31T23:59:59.000000Z"
      },
      {
        "id": 2,
        "product_id": 1002,
        "permission_type": "view-reports",
        "expires_at": "2025-12-31T23:59:59.000000Z"
      },
      {
        "id": 3,
        "product_id": 1003,
        "permission_type": "view-reports",
        "expires_at": "2025-12-31T23:59:59.000000Z"
      }
    ]
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 10.7 批量撤销产品权限
- **端点**: `DELETE /api/v1/users/{user}/product-permissions/revoke-multiple`
- **描述**: 批量撤销用户对多个产品的访问权限
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员或产品所属组织的所有者
- **路径参数**:
  - `user`: 用户ID
- **请求参数**:
  - `product_ids` (必填): 产品ID数组，最少1个，最多100个
  - `permission_type` (可选): 权限类型，默认：view-reports
- **请求示例**:
```json
{
  "product_ids": [1001, 1002, 1003],
  "permission_type": "view-reports"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Multiple product access revoked successfully",
  "data": {
    "revoked_count": 3
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 10.8 获取组织产品权限概览
- **端点**: `GET /api/v1/product-permissions/organisation`
- **描述**: 获取组织内所有用户的产品权限概览，按用户分组显示
- **认证**: 需要Bearer Token
- **权限要求**: 系统管理员或组织所有者
- **查询参数**:
  - `organisation_id` (必填): 组织ID
  - `permission_type` (可选): 权限类型筛选
  - `include_expired` (可选): 是否包含已过期权限，布尔值，默认：false
- **请求示例**:
```
GET /api/v1/product-permissions/organisation?organisation_id=1&permission_type=view-reports&include_expired=false
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Organisation product permissions retrieved successfully",
  "data": {
    "organisation_id": 1,
    "users": [
      {
        "user": {
          "id": 123,
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "permissions": [
          {
            "id": 1,
            "product_id": 1001,
            "product_name": "热销产品A",
            "permission_type": "view-reports",
            "expires_at": "2025-12-31T23:59:59.000000Z",
            "granted_at": "2025-07-10T12:00:00.000000Z",
            "notes": "报表查看权限"
          }
        ],
        "permission_summary": {
          "total_products": 1,
          "by_type": {
            "view-reports": 1
          },
          "expiring_soon": 0
        }
      }
    ],
    "summary": {
      "total_users_with_permissions": 1,
      "total_permissions": 1,
      "permission_types_breakdown": {
        "view-reports": 1
      }
    }
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

### 13. 报表管理

报表管理功能提供销售、销量、退款和订单状态等各类数据报表的查看和导出功能。基于现有的四级权限系统实现角色访问控制，并增强了产品级权限支持。

#### 权限控制说明
- **系统管理员（Root/Admin）**: 可以访问所有组织的报表数据
- **组织成员**: 只能访问其所属组织的报表数据
- **产品级权限**: 支持访客角色访问特定产品的报表数据
- **权限优先级**: 产品权限优先于组织权限
- **跨组织防护**: 通过过滤机制防止跨组织数据访问

#### 参数要求
- **非系统用户**: 必须提供 `organisation_id` 或 `product_id` 参数（二选一）
- **系统管理员**: `organisation_id` 和 `product_id` 参数均为可选
- **产品过滤**: 当提供 `product_id` 时，会自动验证用户对该产品的访问权限

#### 12.1 获取销售报表
- **端点**: `GET /api/v1/reports/sales`
- **描述**: 获取销售金额统计数据，按时间段和地区分组，返回图表兼容格式
- **认证**: 需要Bearer Token
- **权限要求**: 需要销售报表查看权限，支持组织级和产品级权限验证（基于ReportPolicy策略的viewForOrganisationOrProducts方法）
- **查询参数**:
  - `start_date` (必填): 开始日期，格式：YYYY-MM-DD
  - `end_date` (必填): 结束日期，格式：YYYY-MM-DD，不能晚于今天
  - `group_by` (可选): 分组方式，可选值：day, week, month, quarter, year，默认：day
  - `countries` (可选): 国家代码数组，ISO 2位字母代码
  - `states` (可选): 订单状态数组，可选值：completed, cancelled, processing, pending
  - `payment_states` (可选): 支付状态数组，可选值：completed, pending, failed, cancelled
  - `organisation_id` (条件必填): 组织ID，非系统用户在未提供product_id时必填，必须是用户有权限访问的组织
  - `currency` (可选): 货币代码，ISO 3位字母代码，默认：USD
  - `timezone` (可选): 时区，默认：应用配置时区
  - `include_refunds` (可选): 是否包含退款，布尔值，默认：false
  - `refund_status` (可选): 退款状态，可选值：success, pending, failed
  - `product_id` (条件必填): 产品ID，非系统用户在未提供organisation_id时必填，用于筛选特定产品的销售数据，必须是用户有权限访问的产品
- **请求示例**:
```
GET /api/v1/reports/sales?start_date=2025-06-01&end_date=2025-06-30&group_by=month&countries[]=US&countries[]=CA&organisation_id=1&currency=USD&include_refunds=true&product_id=1001
```
- **响应示例**:
```json
{
  "success": true,
  "message": "销售报表数据获取成功",
  "data": {
    "daily_sales_chart": {
      "xAxis": {
        "data": ["06/01", "06/02", "06/03", "06/04", "06/05"]
      },
      "series": [
        {
          "data": [1250, 1380, 1420, 1150, 1680]
        }
      ]
    },
    "dual_axis_chart": {
      "xAxis": {
        "data": ["06/01", "06/02", "06/03", "06/04", "06/05"]
      },
      "series": [
        {
          "name": "订单金额",
          "yAxisIndex": 0,
          "data": [1250, 1380, 1420, 1150, 1680]
        },
        {
          "name": "订单数量",
          "yAxisIndex": 1,
          "data": [12, 14, 13, 11, 16]
        }
      ]
    },
    "regional_sales_amount_chart": {
      "series": [
        {
          "data": [
            {
              "name": "美国",
              "value": 87500
            },
            {
              "name": "加拿大",
              "value": 37500
            }
          ]
        }
      ]
    }
  },
  "timestamp": "2025-06-30T12:00:00.000000Z"
}
```

#### 10.2 获取销量报表
- **端点**: `GET /api/v1/reports/volume`
- **描述**: 获取销售数量统计数据，按时间段和地区分组，返回图表兼容格式
- **认证**: 需要Bearer Token
- **权限要求**: 需要销量报表查看权限，支持组织级和产品级权限验证（基于ReportPolicy策略的viewForOrganisationOrProducts方法）
- **查询参数**: 与销售报表相同，包括条件必填的 `organisation_id` 和 `product_id` 参数（非系统用户必须提供其中之一）
- **响应示例**:
```json
{
  "success": true,
  "message": "销量报表数据获取成功",
  "data": {
    "daily_quantity_chart": {
      "xAxis": {
        "data": ["06/01", "06/02", "06/03", "06/04", "06/05"]
      },
      "series": [
        {
          "data": [52, 58, 61, 48, 72]
        }
      ]
    },
    "regional_sales_quantity_chart": {
      "series": [
        {
          "data": [
            {
              "name": "美国",
              "value": 3675
            },
            {
              "name": "加拿大",
              "value": 1575
            }
          ]
        }
      ]
    }
  },
  "timestamp": "2025-06-30T12:00:00.000000Z"
}
```

#### 10.3 获取退款分析报表
- **端点**: `GET /api/v1/reports/refunds`
- **描述**: 获取退款统计和分析数据，返回图表兼容格式
- **认证**: 需要Bearer Token
- **权限要求**: 需要退款报表查看权限，支持组织级和产品级权限验证（基于ReportPolicy策略的viewForOrganisationOrProducts方法）
- **查询参数**: 与销售报表相同，包括条件必填的 `organisation_id` 和 `product_id` 参数（非系统用户必须提供其中之一），但 `include_refunds` 参数无效（始终包含退款数据）
- **响应示例**:
```json
{
  "success": true,
  "message": "退款报表数据获取成功",
  "data": {
    "refund_trend_chart": {
      "xAxis": {
        "data": ["06/01", "06/02", "06/03", "06/04", "06/05"]
      },
      "series": [
        {
          "name": "退款金额",
          "data": [125, 138, 142, 115, 168]
        },
        {
          "name": "退款订单数",
          "data": [5, 6, 7, 4, 8]
        }
      ]
    },
    "refund_reasons_chart": {
      "data": [
        {
          "name": "产品缺陷",
          "value": 50,
          "color": "#ff4d4f"
        },
        {
          "name": "客户不满意",
          "value": 45,
          "color": "#fa8c16"
        },
        {
          "name": "物流问题",
          "value": 30,
          "color": "#fadb14"
        }
      ]
    }
  },
  "timestamp": "2025-06-30T12:00:00.000000Z"
}
```

#### 10.4 获取订单状态报表
- **端点**: `GET /api/v1/reports/order-status`
- **描述**: 获取订单状态分布和统计数据，返回图表兼容格式
- **认证**: 需要Bearer Token
- **权限要求**: 需要订单状态报表查看权限，支持组织级和产品级权限验证（基于ReportPolicy策略的viewForOrganisationOrProducts方法）
- **查询参数**: 与销售报表相同，包括条件必填的 `organisation_id` 和 `product_id` 参数（非系统用户必须提供其中之一）
- **响应示例**:
```json
{
  "success": true,
  "message": "订单状态报表数据获取成功",
  "data": {
    "order_status_chart": {
      "data": [
        {
          "name": "已完成",
          "value": 1000,
          "color": "#52c41a"
        },
        {
          "name": "处理中",
          "value": 150,
          "color": "#fa8c16"
        },
        {
          "name": "待处理",
          "value": 75,
          "color": "#722ed1"
        },
        {
          "name": "已取消",
          "value": 25,
          "color": "#ff4d4f"
        }
      ]
    },
    "payment_status_chart": {
      "data": [
        {
          "name": "支付完成",
          "value": 1000,
          "color": "#52c41a"
        },
        {
          "name": "待支付",
          "value": 200,
          "color": "#fa8c16"
        },
        {
          "name": "支付失败",
          "value": 30,
          "color": "#ff4d4f"
        },
        {
          "name": "支付取消",
          "value": 20,
          "color": "#722ed1"
        }
      ]
    }
  },
  "timestamp": "2025-06-30T12:00:00.000000Z"
}
```

#### 10.5 获取产品排名报表
- **端点**: `GET /api/v1/reports/product-ranking`
- **描述**: 获取产品销售排名数据，同时提供按销售金额和销售数量的双重排名，返回图表兼容格式
- **认证**: 需要Bearer Token
- **权限要求**: 需要产品排名报表查看权限，支持组织级和产品级权限验证（基于ReportPolicy策略的viewForOrganisationOrProducts方法）
- **查询参数**:
  - `start_date` (必填): 开始日期，格式：YYYY-MM-DD
  - `end_date` (必填): 结束日期，格式：YYYY-MM-DD，不能晚于今天
  - `organisation_id` (必填): 组织ID，必须是用户有权限访问的组织
  - `limit` (可选): 返回产品数量限制，1-100之间，默认：10
  - `currency` (可选): 货币代码，ISO 3位字母代码，默认：USD
  - `timezone` (可选): 时区，默认：应用配置时区
  - `group_by` (可选): 分组方式，可选值：day, week, month, quarter, year
  - `countries` (可选): 国家代码数组，ISO 2位字母代码
  - `states` (可选): 订单状态数组，可选值：completed, cancelled, processing, pending
  - `payment_states` (可选): 支付状态数组，可选值：completed, pending, failed, cancelled
  - `include_refunds` (可选): 是否包含退款数据，布尔值，默认：false

- **请求示例**:
```
GET /api/v1/reports/product-ranking?start_date=2025-06-01&end_date=2025-06-30&organisation_id=1&limit=10&currency=USD
```
- **响应示例**:
```json
{
  "success": true,
  "message": "产品排名报表数据获取成功",
  "data": {
    "sales_rankings": [
      {
        "rank": 1,
        "store_variant_id": 1001,
        "product_name": "热销产品A",
        "product_slug": "hot-product-a",
        "product_package": "package-a.jpg",
        "total_quantity": 150,
        "total_sales": 375000,
        "total_sales_formatted": "$3,750.00"
      },
      {
        "rank": 2,
        "store_variant_id": 1002,
        "product_name": "热销产品B",
        "product_slug": "hot-product-b",
        "product_package": "package-b.jpg",
        "total_quantity": 120,
        "total_sales": 300000,
        "total_sales_formatted": "$3,000.00"
      }
    ],
    "quantity_rankings": [
      {
        "rank": 1,
        "store_variant_id": 1001,
        "product_name": "热销产品A",
        "product_slug": "hot-product-a",
        "product_package": "package-a.jpg",
        "total_quantity": 150,
        "total_sales": 375000,
        "total_sales_formatted": "$3,750.00"
      },
      {
        "rank": 2,
        "store_variant_id": 1003,
        "product_name": "热销产品C",
        "product_slug": "hot-product-c",
        "product_package": "package-c.jpg",
        "total_quantity": 135,
        "total_sales": 270000,
        "total_sales_formatted": "$2,700.00"
      }
    ],
    "sales_ranking_chart": {
      "xAxis": {
        "data": ["热销产品A", "热销产品B", "热销产品C"]
      },
      "series": [
        {
          "name": "销售金额",
          "data": [3750, 3000, 2500]
        }
      ]
    },
    "quantity_ranking_chart": {
      "xAxis": {
        "data": ["热销产品A", "热销产品C", "热销产品B"]
      },
      "series": [
        {
          "name": "销售数量",
          "data": [150, 135, 120]
        }
      ]
    },
    "summary": {
      "total_products": 25,
      "limit": 10,
      "period": {
        "start_date": "2025-06-01",
        "end_date": "2025-06-30",
        "days": 30
      }
    }
  },
  "timestamp": "2025-06-30T12:00:00.000000Z"
}
```

#### 10.6 获取每日销售概览
- **端点**: `GET /api/v1/reports/daily-sales-overview`
- **描述**: 获取今日销售概览数据，包含与昨日的对比分析。支持可选的product_id参数用于获取特定产品的概览数据
- **认证**: 需要Bearer Token
- **权限要求**: 需要销售报表查看权限，支持组织级和产品级权限验证（基于ReportPolicy策略的viewForOrganisationOrProducts方法）
- **查询参数**:
  - `organisation_id` (条件必填): 组织ID，非系统用户在未提供product_id时必填，必须是用户有权限访问的组织
  - `product_id` (条件必填): 产品ID，非系统用户在未提供organisation_id时必填，用于获取特定产品的概览数据，必须是用户有权限访问的产品
  - `timezone` (可选): 时区，用于日期计算，默认：应用配置时区

- **请求示例**:

**组织概览示例**:
```
GET /api/v1/reports/daily-sales-overview?organisation_id=1&timezone=UTC
```

**产品特定概览示例**:
```
GET /api/v1/reports/daily-sales-overview?product_id=1001&timezone=America/New_York
```

- **响应示例**:
```json
{
  "success": true,
  "message": "每日销售概览数据获取成功",
  "data": {
    "overview": {
      "sales_amount": {
        "today": {
          "value": 12500,
          "formatted": "$125.00",
          "currency": "USD"
        },
        "yesterday": {
          "value": 11800,
          "formatted": "$118.00",
          "currency": "USD"
        },
        "comparison": {
          "percentage_change": 5.93,
          "trend": "up",
          "difference": 700,
          "difference_formatted": "$7.00"
        }
      },
      "sales_quantity": {
        "today": {
          "value": 45
        },
        "yesterday": {
          "value": 42
        },
        "comparison": {
          "percentage_change": 7.14,
          "trend": "up",
          "difference": 3
        }
      },
      "new_customers": {
        "today": {
          "value": 8
        },
        "yesterday": {
          "value": 6
        }
      },
      "refunds": {
        "count": {
          "today": {
            "value": 2
          },
          "yesterday": {
            "value": 3
          },
          "comparison": {
            "percentage_change": -33.33,
            "trend": "down",
            "difference": -1
          }
        },
        "rate": {
          "today": {
            "value": 4.44,
            "formatted": "4.44%"
          },
          "yesterday": {
            "value": 7.14,
            "formatted": "7.14%"
          },
          "comparison": {
            "percentage_change": -37.82,
            "trend": "down",
            "difference": -2.7
          }
        }
      }
    },
    "additional_metrics": {
      "today": {
        "total_orders": 45,
        "unique_customers": 38,
        "total_refunds": {
          "value": 500,
          "formatted": "$5.00",
          "currency": "USD"
        }
      },
      "yesterday": {
        "total_orders": 42,
        "unique_customers": 35,
        "total_refunds": {
          "value": 840,
          "formatted": "$8.40",
          "currency": "USD"
        }
      }
    },
    "meta": {
      "date": "2025-07-10",
      "timezone": "UTC",
      "product_id": null,
      "is_product_specific": false,
      "generated_at": "2025-07-10T12:00:00.000000Z"
    }
  },
  "timestamp": "2025-07-10T12:00:00.000000Z"
}
```

#### 10.7 导出报表数据
- **端点**: `POST /api/v1/reports/export`
- **描述**: 生成并返回可下载的报表文件
- **认证**: 需要Bearer Token
- **权限要求**: 需要报表导出权限，支持组织级权限验证（基于ReportPolicy策略的exportForOrganisation方法）
- **请求参数**:
  - `report_type` (必填): 报表类型，可选值：sales, volume, refunds, order_status, product_ranking, daily_sales_overview
  - `format` (必填): 导出格式，可选值：xlsx, csv, pdf
  - `filename` (可选): 文件名，最大255字符，只能包含字母、数字、下划线、连字符和空格
  - `include_summary` (可选): 是否包含汇总信息，布尔值，默认：true
  - `include_charts` (可选): 是否包含图表，布尔值，默认：false
  - `max_records` (可选): 最大记录数，1-50000之间，默认：10000
  - `email_to` (可选): 发送邮件地址，如果提供则将文件发送到指定邮箱
  - `async` (可选): 是否异步处理，布尔值，默认：根据记录数自动判断（>5000条记录时自动异步）
  - 其他筛选参数与对应报表类型相同（start_date, end_date, group_by, organisation_id, product_id等）
- **请求示例**:

**销售报表导出示例**:
```json
{
  "report_type": "sales",
  "format": "xlsx",
  "filename": "sales_report_june_2025",
  "include_summary": true,
  "include_charts": true,
  "max_records": 5000,
  "email_to": "<EMAIL>",
  "start_date": "2025-06-01",
  "end_date": "2025-06-30",
  "group_by": "day",
  "countries": ["US", "CA"],
  "organisation_id": 1,
  "currency": "USD",
  "product_id": 1001
}
```

**产品排名报表导出示例**:
```json
{
  "report_type": "product_ranking",
  "format": "xlsx",
  "filename": "product_ranking_report_june_2025",
  "include_summary": true,
  "include_charts": true,
  "max_records": 1000,
  "start_date": "2025-06-01",
  "end_date": "2025-06-30",
  "organisation_id": 1,
  "limit": 50,
  "currency": "USD",
  "countries": ["US", "CA"],
  "states": ["completed"],
  "include_refunds": false,

}
```
- **同步处理响应示例**（小文件，立即返回）:
```json
{
  "success": true,
  "message": "报表导出完成",
  "data": {
    "export_id": "export_20250630_001",
    "filename": "sales_report_june_2025.xlsx",
    "file_size": 2048576,
    "download_url": "https://api.example.com/downloads/export_20250630_001",
    "expires_at": "2025-07-01T12:00:00.000000Z",
    "format": "xlsx",
    "report_type": "sales",
    "record_count": 1250,
    "processing_time": 3.5,
    "async": false
  },
  "timestamp": "2025-06-30T12:00:00.000000Z"
}
```
- **异步处理响应示例**（大文件，后台处理）:
```json
{
  "success": true,
  "message": "报表导出已启动",
  "data": {
    "export_id": "export_20250630_002",
    "filename": "sales_report_june_2025.xlsx",
    "status": "processing",
    "estimated_completion": "2025-06-30T12:05:00.000000Z",
    "format": "xlsx",
    "report_type": "sales",
    "estimated_record_count": 15000,
    "async": true,
    "progress_url": "/api/v1/reports/export/export_20250630_002/progress",
    "email_notification": "<EMAIL>"
  },
  "timestamp": "2025-06-30T12:00:00.000000Z"
}
```

#### 报表权限说明

报表功能基于现有的四级权限系统实现访问控制：

##### 系统管理员权限（Root/Admin）
- 可以查看所有组织的报表数据
- 可以导出任何组织的报表
- 可以访问任何组织的报表数据
- 不受组织边界限制

##### 组织所有者权限（Owner）
- 只能查看自己组织的报表数据
- 可以导出自己组织的报表
- `organisation_id` 参数只能是用户所属的组织
- 无法访问其他组织的报表数据

##### 组织成员权限（Member）
- 只能查看自己组织的报表数据
- 可以导出自己组织的报表
- `organisation_id` 参数只能是用户所属的组织
- 权限与组织所有者相同（在报表功能上）

##### 访客权限（Visitor）
- 无法访问任何报表功能
- 所有报表端点都会返回403权限不足错误

#### 报表数据筛选说明

**重要提示**: 从当前版本开始，`organisation_id` 参数已改为必填参数，所有报表请求都必须指定一个组织ID。

1. **时间筛选**: 所有报表都支持按日期范围筛选，支持多种分组方式
2. **地理筛选**: 支持按国家代码筛选数据
3. **状态筛选**: 支持按订单状态和支付状态筛选
4. **组织筛选**: 必须通过 `organisation_id` 参数指定组织，普通用户只能指定自己所属的组织
5. **货币筛选**: 支持按货币类型筛选和显示

#### 导出功能特性

1. **多格式支持**: 支持Excel (xlsx)、CSV和PDF格式导出
2. **异步处理**: 大数据量自动启用异步处理，避免请求超时
3. **邮件通知**: 支持将导出文件发送到指定邮箱
4. **文件管理**: 导出文件有过期时间，自动清理
5. **进度跟踪**: 异步导出支持进度查询

## 认证说明

### Bearer Token 认证
所有需要认证的端点都使用Bearer Token认证方式：

```bash
Authorization: Bearer YOUR_TOKEN_HERE
```

### 获取Token流程
1. 调用 `POST /api/v1/auth/login` 端点
2. 从响应中获取 `data.token` 字段
3. 在后续请求的Header中添加 `Authorization: Bearer {token}`

### Token管理
- Token默认永不过期（可在配置中修改）
- 可通过 `/api/v1/auth/logout` 撤销当前Token
- 可通过 `/api/v1/auth/revoke-all-tokens` 撤销所有Token

## 错误代码

| HTTP状态码 | 说明 |
|-----------|------|
| 200 | 请求成功 |
| 401 | 未认证或Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 验证失败 |
| 500 | 服务器内部错误 |

## 使用示例

### 完整认证流程示例

```bash
# 1. 登录获取Token
curl -X POST http://localhost/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 2. 使用Token访问受保护资源
curl -X GET http://localhost/api/v1/user \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"

# 3. 退出登录
curl -X POST http://localhost/api/v1/auth/logout \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"
```
