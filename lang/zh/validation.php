<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => ':attribute 必须接受。',
    'accepted_if' => '当 :other 为 :value 时，:attribute 必须接受。',
    'active_url' => ':attribute 不是一个有效的网址。',
    'after' => ':attribute 必须是一个在 :date 之后的日期。',
    'after_or_equal' => ':attribute 必须是一个在 :date 之后或相同的日期。',
    'alpha' => ':attribute 只能由字母组成。',
    'alpha_dash' => ':attribute 只能由字母、数字、短划线和下划线组成。',
    'alpha_num' => ':attribute 只能由字母和数字组成。',
    'array' => ':attribute 必须是一个数组。',
    'ascii' => ':attribute 必须只包含单字节字母数字字符和符号。',
    'before' => ':attribute 必须是一个在 :date 之前的日期。',
    'before_or_equal' => ':attribute 必须是一个在 :date 之前或相同的日期。',
    'between' => [
        'array' => ':attribute 必须只有 :min - :max 个单元。',
        'file' => ':attribute 必须介于 :min - :max KB 之间。',
        'numeric' => ':attribute 必须介于 :min - :max 之间。',
        'string' => ':attribute 必须介于 :min - :max 个字符之间。',
    ],
    'boolean' => ':attribute 字段必须为真或假。',
    'confirmed' => ':attribute 两次输入不一致。',
    'current_password' => '密码不正确。',
    'date' => ':attribute 不是一个有效的日期。',
    'date_equals' => ':attribute 必须是一个等于 :date 的日期。',
    'date_format' => ':attribute 的格式必须为 :format。',
    'decimal' => ':attribute 必须有 :decimal 位小数。',
    'declined' => ':attribute 必须拒绝。',
    'declined_if' => '当 :other 为 :value 时，:attribute 必须拒绝。',
    'different' => ':attribute 和 :other 必须不同。',
    'digits' => ':attribute 必须是 :digits 位的数字。',
    'digits_between' => ':attribute 必须是介于 :min 和 :max 位的数字。',
    'dimensions' => ':attribute 图片尺寸不正确。',
    'distinct' => ':attribute 已经存在。',
    'doesnt_end_with' => ':attribute 不能以下列之一结尾：:values。',
    'doesnt_start_with' => ':attribute 不能以下列之一开头：:values。',
    'email' => ':attribute 不是一个合法的邮箱。',
    'ends_with' => ':attribute 必须以下列之一结尾：:values。',
    'enum' => '选择的 :attribute 无效。',
    'exists' => '选择的 :attribute 无效。',
    'file' => ':attribute 必须是文件。',
    'filled' => ':attribute 不能为空。',
    'gt' => [
        'array' => ':attribute 必须多于 :value 个单元。',
        'file' => ':attribute 必须大于 :value KB。',
        'numeric' => ':attribute 必须大于 :value。',
        'string' => ':attribute 必须多于 :value 个字符。',
    ],
    'gte' => [
        'array' => ':attribute 必须多于或等于 :value 个单元。',
        'file' => ':attribute 必须大于或等于 :value KB。',
        'numeric' => ':attribute 必须大于或等于 :value。',
        'string' => ':attribute 必须多于或等于 :value 个字符。',
    ],
    'image' => ':attribute 必须是图片。',
    'in' => '选择的 :attribute 无效。',
    'in_array' => ':attribute 不存在于 :other 中。',
    'integer' => ':attribute 必须是整数。',
    'ip' => ':attribute 必须是有效的 IP 地址。',
    'ipv4' => ':attribute 必须是有效的 IPv4 地址。',
    'ipv6' => ':attribute 必须是有效的 IPv6 地址。',
    'json' => ':attribute 必须是正确的 JSON 格式。',
    'lowercase' => ':attribute 必须是小写。',
    'lt' => [
        'array' => ':attribute 必须少于 :value 个单元。',
        'file' => ':attribute 必须小于 :value KB。',
        'numeric' => ':attribute 必须小于 :value。',
        'string' => ':attribute 必须少于 :value 个字符。',
    ],
    'lte' => [
        'array' => ':attribute 必须少于或等于 :value 个单元。',
        'file' => ':attribute 必须小于或等于 :value KB。',
        'numeric' => ':attribute 必须小于或等于 :value。',
        'string' => ':attribute 必须少于或等于 :value 个字符。',
    ],
    'mac_address' => ':attribute 必须是有效的 MAC 地址。',
    'max' => [
        'array' => ':attribute 最多只有 :max 个单元。',
        'file' => ':attribute 不能大于 :max KB。',
        'numeric' => ':attribute 不能大于 :max。',
        'string' => ':attribute 不能大于 :max 个字符。',
    ],
    'max_digits' => ':attribute 不能超过 :max 位数字。',
    'mimes' => ':attribute 必须是一个 :values 类型的文件。',
    'mimetypes' => ':attribute 必须是一个 :values 类型的文件。',
    'min' => [
        'array' => ':attribute 至少有 :min 个单元。',
        'file' => ':attribute 大小不能小于 :min KB。',
        'numeric' => ':attribute 必须至少为 :min。',
        'string' => ':attribute 至少为 :min 个字符。',
    ],
    'min_digits' => ':attribute 必须至少有 :min 位数字。',
    'missing' => ':attribute 字段必须缺失。',
    'missing_if' => '当 :other 为 :value 时，:attribute 字段必须缺失。',
    'missing_unless' => '除非 :other 为 :value，否则 :attribute 字段必须缺失。',
    'missing_with' => '当 :values 存在时，:attribute 字段必须缺失。',
    'missing_with_all' => '当 :values 都存在时，:attribute 字段必须缺失。',
    'multiple_of' => ':attribute 必须是 :value 的倍数。',
    'not_in' => '选择的 :attribute 无效。',
    'not_regex' => ':attribute 的格式无效。',
    'numeric' => ':attribute 必须是一个数字。',
    'password' => '密码错误。',
    'present' => ':attribute 字段必须存在。',
    'prohibited' => ':attribute 字段被禁止。',
    'prohibited_if' => '当 :other 为 :value 时，:attribute 字段被禁止。',
    'prohibited_unless' => '除非 :other 在 :values 中，否则 :attribute 字段被禁止。',
    'prohibits' => ':attribute 字段禁止 :other 存在。',
    'regex' => ':attribute 格式无效。',
    'required' => ':attribute 不能为空。',
    'required_array_keys' => ':attribute 字段必须包含条目：:values。',
    'required_if' => '当 :other 为 :value 时 :attribute 不能为空。',
    'required_if_accepted' => '当 :other 被接受时，:attribute 字段是必需的。',
    'required_unless' => '当 :other 不为 :values 时 :attribute 不能为空。',
    'required_with' => '当 :values 存在时 :attribute 不能为空。',
    'required_with_all' => '当 :values 都存在时 :attribute 不能为空。',
    'required_without' => '当 :values 不存在时 :attribute 不能为空。',
    'required_without_all' => '当 :values 都不存在时 :attribute 不能为空。',
    'same' => ':attribute 和 :other 必须相同。',
    'size' => [
        'array' => ':attribute 必须为 :size 个单元。',
        'file' => ':attribute 大小必须为 :size KB。',
        'numeric' => ':attribute 大小必须为 :size。',
        'string' => ':attribute 必须是 :size 个字符。',
    ],
    'starts_with' => ':attribute 必须以 :values 为开头。',
    'string' => ':attribute 必须是字符串。',
    'timezone' => ':attribute 必须是一个合法的时区值。',
    'unique' => ':attribute 已经存在。',
    'uploaded' => ':attribute 上传失败。',
    'uppercase' => ':attribute 必须是大写。',
    'url' => ':attribute 格式无效。',
    'ulid' => ':attribute 必须是有效的 ULID。',
    'uuid' => ':attribute 必须是有效的 UUID。',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "rule.attribute" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'start_date' => '开始日期',
        'end_date' => '结束日期',
        'group_by' => '分组方式',
        'countries' => '国家',
        'states' => '州/省',
        'payment_states' => '支付状态',
        'organisation_id' => '组织ID',
        'currency' => '货币',
        'timezone' => '时区',
        'include_refunds' => '包含退款',
        'refund_status' => '退款状态',
        'limit' => '限制数量',
        'product_id' => '产品ID',
        'per_page' => '每页数量',
        'log_name' => '日志名称',
        'subject_type' => '主体类型',
        'causer_id' => '操作者ID',
        'event' => '事件',
        'date_from' => '开始日期',
        'date_to' => '结束日期',
        'report_type' => '报表类型',
        'report_title' => '报表标题',
        'status' => '报表状态',
        'total_amount' => '总金额',
        'total_quantity' => '总数量',
        'total_orders' => '订单总数',
        'remarks' => '备注',
        'order_ids' => '订单ID',
    ],

    /*
    |--------------------------------------------------------------------------
    | Report Validation Messages
    |--------------------------------------------------------------------------
    */
    'reports' => [
        'start_date_required' => '开始日期是必需的。',
        'end_date_required' => '结束日期是必需的。',
        'invalid_date_range' => '开始日期必须早于或等于结束日期。',
        'end_date_future' => '结束日期不能是未来日期。',
        'invalid_country_code' => '国家代码必须是有效的2位ISO代码。',
        'invalid_group_by' => '分组方式必须是以下之一：hour, day, week, month, quarter, year。',
        'invalid_state' => '状态必须是以下之一：completed, cancelled, processing, pending。',
        'invalid_payment_state' => '支付状态必须是以下之一：completed, pending, failed, cancelled。',
        'organisation_id_or_product_id_required' => '组织ID或产品ID必须提供其中之一。',
        'organisation_id_must_be_integer' => '组织ID必须是整数。',
        'organisation_not_found' => '指定的组织不存在。',
        'organisation_access_denied' => '您没有访问此组织的权限。',
        'invalid_currency_code' => '货币代码必须是有效的3位ISO代码。',
        'invalid_timezone' => '时区必须是有效的时区。',
        'limit_integer' => '限制数量必须是整数。',
        'limit_min' => '限制数量至少为1。',
        'limit_max' => '限制数量不能超过100。',
        'product_id_must_be_integer' => '产品ID必须是整数。',
        'product_not_found' => '指定的产品不存在。',
        'product_id_not_allowed_for_ranking' => '产品排名报告不允许使用产品ID。',
        'report_type_required' => '报告类型是必需的。',
        'invalid_report_type' => '报告类型无效。',
        'format_required' => '格式是必需的。',
        'invalid_format' => '格式无效。',
        'invalid_filename' => '文件名包含无效字符。',
        'filename_too_long' => '文件名太长。',
        'max_records_exceeded' => '超过最大记录数。',
        'min_records_required' => '需要最少记录数。',
        'invalid_email' => '邮箱地址无效。',
    ],

    // Activity log validation messages
    'activity_log' => [
        'per_page_max' => '每页数量不能超过100。',
        'subject_type_invalid' => '主体类型无效。',
        'event_invalid' => '事件类型必须是以下之一：created、updated、deleted。',
        'date_from_before_date_to' => '开始日期必须早于或等于结束日期。',
        'date_to_after_date_from' => '结束日期必须晚于或等于开始日期。',
        'date_to_not_future' => '结束日期不能是未来日期。',
    ],

    // Email template validation messages
    'email_template' => [
        'code_format' => '模板代码只能包含小写字母、数字和下划线。',
        'code_unique' => '模板代码已被使用。',
        'variable_format' => '变量名只能包含小写字母、数字和下划线。',
        'content_required' => '邮件内容是必需的。',
        'subject_required' => '邮件主题是必需的。',
        'undeclared_variables' => '以下变量在模板中使用但未声明：:variables',
        'unused_variables' => '以下声明的变量在模板中未使用：:variables',
    ],

    /*
    |--------------------------------------------------------------------------
    | Financial Statement Validation Messages
    |--------------------------------------------------------------------------
    */
    'financial_statements' => [
        'report_type' => [
            'required' => '报表类型为必填项',
            'in' => '报表类型必须是有效的类型',
        ],
        'report_title' => [
            'required' => '报表标题为必填项',
            'max' => '报表标题不能超过255个字符',
        ],
        'start_date' => [
            'required' => '开始日期为必填项',
            'date' => '开始日期格式不正确',
        ],
        'end_date' => [
            'required' => '结束日期为必填项',
            'date' => '结束日期格式不正确',
            'after_or_equal' => '结束日期必须大于或等于开始日期',
        ],
        'organisation_id' => [
            'required' => '机构ID为必填项',
            'exists' => '指定的机构不存在',
        ],
        'status' => [
            'in' => '报表状态必须是有效的状态',
        ],
        'total_amount' => [
            'integer' => '总金额必须是整数',
            'min' => '总金额不能为负数',
        ],
        'total_quantity' => [
            'integer' => '总数量必须是整数',
            'min' => '总数量不能为负数',
        ],
        'total_orders' => [
            'integer' => '订单总数必须是整数',
            'min' => '订单总数不能为负数',
        ],
        'remarks' => [
            'max' => '备注不能超过65535个字符',
        ],
        'order_ids' => [
            'array' => '订单ID必须是数组格式',
            'integer' => '订单ID必须是整数',
            'exists' => '指定的订单不存在',
        ],
    ],
];
