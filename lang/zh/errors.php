<?php

declare(strict_types=1);

return [
    // Standard error messages for different exception types
    'validation' => '验证失败',
    'authentication' => '需要身份验证',
    'authorization' => '访问被拒绝',
    'permission' => '权限不足',
    'not_found' => '资源未找到',
    'method_not_allowed' => '方法不被允许',
    'throttle' => '请求过于频繁',
    'bad_request' => '错误的请求',
    'server_error' => '内部服务器错误',
    'service_unavailable' => '服务暂时不可用',
    'generic' => '处理您的请求时发生错误',
    'bad_gateway' => '网关错误',

    // Business logic errors
    'business_logic_error' => '业务逻辑错误',
    'resource_state_conflict' => '资源状态冲突',
    'organisation_role_system_guard' => '组织角色不能使用系统守卫',

    // User role related errors
    'role_assignment_failed' => '角色分配失败',
    'role_removal_failed' => '角色移除失败',
    'role_transfer_failed' => '角色转移失败',

    // Organisation errors
    'organisation_already_suspended' => '组织已经是暂停状态',

    // Report errors
    'report_organisation_ids_required' => '组织ID是必需的',
    'report_multiple_organisations_not_allowed' => '非系统用户一次只能访问一个组织',
    'report_organisation_access_denied' => '拒绝访问指定的组织',
    'report_invalid_organisation_id' => '无效的组织ID',
    'report_product_access_denied' => '拒绝访问指定的产品',
    'report_product_requires_organisation' => '产品筛选需要有效的组织ID',

    // Invitation errors
    'invitation_expired' => '邀请已过期',
    'invitation_usage_limit_reached' => '邀请使用次数已达上限',
    'invitation_processing_error' => '邀请处理错误',

    // Email verification errors
    'verification_code_send_error' => '验证码发送失败',

    // Permission Service errors
    'permission_current_user_not_owner_or_admin' => '当前用户不是该组织的所有者或系统管理员。',
    'permission_new_owner_must_belong_to_organization' => '新所有者必须属于指定的组织。',
    'permission_current_user_must_belong_to_organization' => '当前用户必须属于指定的组织或为系统管理员。',
    'permission_user_already_has_role' => '用户已经被分配了此角色。',
    'permission_organization_already_has_owner' => '该组织已经有一个所有者，请先转移所有者角色。',
    'permission_owner_role_not_exist' => '组织所有者角色不存在。',

    // Invitation Service errors
    'invitation_no_permission_create' => '您没有权限为此组织创建邀请',
    'invitation_cannot_create_system_roles' => '无法为系统角色创建邀请',
    'invitation_email_not_allowed' => '您的邮箱地址不被允许接受此邀请',
    'invitation_resource_not_exist' => '邀请关联的资源不存在',
    'invitation_unsupported_type' => '不支持的邀请类型',
    'invitation_processing_error_prefix' => '处理邀请时出错',
    'invitation_role_not_exist' => '指定的角色不存在',
    'invitation_join_success' => '成功加入组织',
    'invitation_expired' => '邀请链接已过期',
    'invitation_usage_limit_reached' => '邀请链接已达使用次数上限',

    // User Service errors
    'user_no_access_organization' => '您没有访问此组织的权限。',
    'user_no_access_some_organizations' => '您没有访问某些指定组织的权限。',
    'user_cannot_remove_owner' => '无法移除组织所有者。必须先转移所有者角色。',
    'user_cannot_remove_owner_with_id' => '无法从组织ID :id 中移除用户。用户是所有者，必须先转移所有者角色。',
];
