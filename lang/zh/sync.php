<?php

return [
    'product_sync_started' => '产品同步已开始',
    'product_sync_completed' => '产品同步成功完成',
    'product_sync_failed' => '产品同步失败',
    'order_sync_started' => '订单同步已开始',
    'order_sync_completed' => '订单同步成功完成',
    'order_sync_failed' => '订单同步失败',
    'batch_processing' => '正在处理批次 :batch_id',
    'records_processed' => '已处理 :count 条记录',
    'sync_status' => [
        'pending' => '等待中',
        'processing' => '处理中',
        'completed' => '已完成',
        'failed' => '失败',
    ],
    'record_status' => [
        'pending' => '等待中',
        'success' => '成功',
        'failed' => '失败',
        'skipped' => '跳过',
    ],
    'logs_retrieved' => '同步日志获取成功',
    'log_not_found' => '同步日志未找到',
    'log_details_retrieved' => '同步日志详情获取成功',
    'sync_triggered' => '同步触发成功',
    'sync_trigger_failed' => '同步触发失败',
    'sync_queued' => '同步任务已加入队列',
    'sync_already_running' => '同步任务正在运行中',
    'retry_started' => '重试同步启动成功',
    'retry_failed' => '重试同步失败',
    'retry_queued' => '重试同步任务已加入队列',
    'retry_invalid_status' => '当前状态无法重试同步',
    'progress_retrieved' => '同步进度获取成功',
    'progress_not_found' => '同步进度未找到',
    'progress_retrieval_failed' => '同步进度获取失败',
    'active_jobs_retrieved' => '活跃同步任务获取成功',
    'active_jobs_retrieval_failed' => '活跃同步任务获取失败',
    'job_cleaned_up' => '同步任务清理成功',
    'job_cleanup_failed' => '同步任务清理失败',
    'job_not_found' => '同步任务未找到',
    'job_details_retrieved' => '同步任务详情获取成功',
    'job_details_retrieval_failed' => '获取同步任务详情失败',
    'invalid_sync_type' => '重新验证的同步类型无效',
    'sync_not_completed' => '同步必须完成后才能重新验证',
    'no_validation_results' => '此同步未找到验证结果',
    'validation_already_passed' => '验证已经通过',
    'validation_not_eligible_for_retry' => '验证不符合重试条件',
    'validation_already_running' => '此同步的验证已在运行中',
    'revalidation_queued' => '重新验证任务已加入队列',
    'revalidation_failed' => '重新验证任务加入队列失败',
    'batch_revalidation_completed' => '批量重新验证已完成',
    'batch_revalidation_all_failed' => '所有批量重新验证请求都失败了',
    'revalidation_candidates_retrieved' => '重新验证候选项获取成功',
];
