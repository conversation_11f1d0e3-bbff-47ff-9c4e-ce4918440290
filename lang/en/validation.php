<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'The :attribute must be accepted.',
    'accepted_if' => 'The :attribute must be accepted when :other is :value.',
    'active_url' => 'The :attribute is not a valid URL.',
    'after' => 'The :attribute must be a date after :date.',
    'after_or_equal' => 'The :attribute must be a date after or equal to :date.',
    'alpha' => 'The :attribute must only contain letters.',
    'alpha_dash' => 'The :attribute must only contain letters, numbers, dashes and underscores.',
    'alpha_num' => 'The :attribute must only contain letters and numbers.',
    'array' => 'The :attribute must be an array.',
    'ascii' => 'The :attribute must only contain single-byte alphanumeric characters and symbols.',
    'before' => 'The :attribute must be a date before :date.',
    'before_or_equal' => 'The :attribute must be a date before or equal to :date.',
    'between' => [
        'array' => 'The :attribute must have between :min and :max items.',
        'file' => 'The :attribute must be between :min and :max kilobytes.',
        'numeric' => 'The :attribute must be between :min and :max.',
        'string' => 'The :attribute must be between :min and :max characters.',
    ],
    'boolean' => 'The :attribute field must be true or false.',
    'confirmed' => 'The :attribute confirmation does not match.',
    'current_password' => 'The password is incorrect.',
    'date' => 'The :attribute is not a valid date.',
    'date_equals' => 'The :attribute must be a date equal to :date.',
    'date_format' => 'The :attribute does not match the format :format.',
    'decimal' => 'The :attribute must have :decimal decimal places.',
    'declined' => 'The :attribute must be declined.',
    'declined_if' => 'The :attribute must be declined when :other is :value.',
    'different' => 'The :attribute and :other must be different.',
    'digits' => 'The :attribute must be :digits digits.',
    'digits_between' => 'The :attribute must be between :min and :max digits.',
    'dimensions' => 'The :attribute has invalid image dimensions.',
    'distinct' => 'The :attribute field has a duplicate value.',
    'doesnt_end_with' => 'The :attribute may not end with one of the following: :values.',
    'doesnt_start_with' => 'The :attribute may not start with one of the following: :values.',
    'email' => 'The :attribute must be a valid email address.',
    'ends_with' => 'The :attribute must end with one of the following: :values.',
    'enum' => 'The selected :attribute is invalid.',
    'exists' => 'The selected :attribute is invalid.',
    'file' => 'The :attribute must be a file.',
    'filled' => 'The :attribute field must have a value.',
    'gt' => [
        'array' => 'The :attribute must have more than :value items.',
        'file' => 'The :attribute must be greater than :value kilobytes.',
        'numeric' => 'The :attribute must be greater than :value.',
        'string' => 'The :attribute must be greater than :value characters.',
    ],
    'gte' => [
        'array' => 'The :attribute must have :value items or more.',
        'file' => 'The :attribute must be greater than or equal to :value kilobytes.',
        'numeric' => 'The :attribute must be greater than or equal to :value.',
        'string' => 'The :attribute must be greater than or equal to :value characters.',
    ],
    'image' => 'The :attribute must be an image.',
    'in' => 'The selected :attribute is invalid.',
    'in_array' => 'The :attribute field does not exist in :other.',
    'integer' => 'The :attribute must be an integer.',
    'ip' => 'The :attribute must be a valid IP address.',
    'ipv4' => 'The :attribute must be a valid IPv4 address.',
    'ipv6' => 'The :attribute must be a valid IPv6 address.',
    'json' => 'The :attribute must be a valid JSON string.',
    'lowercase' => 'The :attribute must be lowercase.',
    'lt' => [
        'array' => 'The :attribute must have less than :value items.',
        'file' => 'The :attribute must be less than :value kilobytes.',
        'numeric' => 'The :attribute must be less than :value.',
        'string' => 'The :attribute must be less than :value characters.',
    ],
    'lte' => [
        'array' => 'The :attribute must not have more than :value items.',
        'file' => 'The :attribute must be less than or equal to :value kilobytes.',
        'numeric' => 'The :attribute must be less than or equal to :value.',
        'string' => 'The :attribute must be less than or equal to :value characters.',
    ],
    'mac_address' => 'The :attribute must be a valid MAC address.',
    'max' => [
        'array' => 'The :attribute must not have more than :max items.',
        'file' => 'The :attribute must not be greater than :max kilobytes.',
        'numeric' => 'The :attribute must not be greater than :max.',
        'string' => 'The :attribute must not be greater than :max characters.',
    ],
    'max_digits' => 'The :attribute must not have more than :max digits.',
    'mimes' => 'The :attribute must be a file of type: :values.',
    'mimetypes' => 'The :attribute must be a file of type: :values.',
    'min' => [
        'array' => 'The :attribute must have at least :min items.',
        'file' => 'The :attribute must be at least :min kilobytes.',
        'numeric' => 'The :attribute must be at least :min.',
        'string' => 'The :attribute must be at least :min characters.',
    ],
    'min_digits' => 'The :attribute must have at least :min digits.',
    'missing' => 'The :attribute field must be missing.',
    'missing_if' => 'The :attribute field must be missing when :other is :value.',
    'missing_unless' => 'The :attribute field must be missing unless :other is :value.',
    'missing_with' => 'The :attribute field must be missing when :values is present.',
    'missing_with_all' => 'The :attribute field must be missing when :values are present.',
    'multiple_of' => 'The :attribute must be a multiple of :value.',
    'not_in' => 'The selected :attribute is invalid.',
    'not_regex' => 'The :attribute format is invalid.',
    'numeric' => 'The :attribute must be a number.',
    'password' => 'The password is incorrect.',
    'present' => 'The :attribute field must be present.',
    'prohibited' => 'The :attribute field is prohibited.',
    'prohibited_if' => 'The :attribute field is prohibited when :other is :value.',
    'prohibited_unless' => 'The :attribute field is prohibited unless :other is in :values.',
    'prohibits' => 'The :attribute field prohibits :other from being present.',
    'regex' => 'The :attribute format is invalid.',
    'required' => 'The :attribute field is required.',
    'required_array_keys' => 'The :attribute field must contain entries for: :values.',
    'required_if' => 'The :attribute field is required when :other is :value.',
    'required_if_accepted' => 'The :attribute field is required when :other is accepted.',
    'required_unless' => 'The :attribute field is required unless :other is in :values.',
    'required_with' => 'The :attribute field is required when :values is present.',
    'required_with_all' => 'The :attribute field is required when :values are present.',
    'required_without' => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same' => 'The :attribute and :other must match.',
    'size' => [
        'array' => 'The :attribute must contain :size items.',
        'file' => 'The :attribute must be :size kilobytes.',
        'numeric' => 'The :attribute must be :size.',
        'string' => 'The :attribute must be :size characters.',
    ],
    'starts_with' => 'The :attribute must start with one of the following: :values.',
    'string' => 'The :attribute must be a string.',
    'timezone' => 'The :attribute must be a valid timezone.',
    'unique' => 'The :attribute has already been taken.',
    'uploaded' => 'The :attribute failed to upload.',
    'uppercase' => 'The :attribute must be uppercase.',
    'url' => 'The :attribute must be a valid URL.',
    'ulid' => 'The :attribute must be a valid ULID.',
    'uuid' => 'The :attribute must be a valid UUID.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "rule.attribute" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'group_by' => 'Group By',
        'countries' => 'Countries',
        'states' => 'States',
        'payment_states' => 'Payment States',
        'organisation_id' => 'Organisation ID',
        'currency' => 'Currency',
        'timezone' => 'Timezone',
        'include_refunds' => 'Include Refunds',
        'refund_status' => 'Refund Status',
        'limit' => 'Limit',
        'product_id' => 'Product ID',
        'per_page' => 'Per Page',
        'log_name' => 'Log Name',
        'subject_type' => 'Subject Type',
        'causer_id' => 'Causer ID',
        'event' => 'Event',
        'date_from' => 'Date From',
        'date_to' => 'Date To',
        'report_type' => 'Report Type',
        'report_title' => 'Report Title',
        'status' => 'Status',
        'total_amount' => 'Total Amount',
        'total_quantity' => 'Total Quantity',
        'total_orders' => 'Total Orders',
        'remarks' => 'Remarks',
        'order_ids' => 'Order IDs',
    ],

    /*
    |--------------------------------------------------------------------------
    | Report Validation Messages
    |--------------------------------------------------------------------------
    */
    'reports' => [
        'start_date_required' => 'The start date is required.',
        'end_date_required' => 'The end date is required.',
        'invalid_date_range' => 'The start date must be before or equal to the end date.',
        'end_date_future' => 'The end date cannot be in the future.',
        'invalid_country_code' => 'The country code must be a valid 2-letter ISO code.',
        'invalid_group_by' => 'The group by value must be one of: hour, day, week, month, quarter, year.',
        'invalid_state' => 'The state must be one of: completed, cancelled, processing, pending.',
        'invalid_payment_state' => 'The payment state must be one of: completed, pending, failed, cancelled.',
        'organisation_id_or_product_id_required' => 'Either organisation ID or product ID is required.',
        'organisation_id_must_be_integer' => 'The organisation ID must be an integer.',
        'organisation_not_found' => 'The specified organisation does not exist.',
        'organisation_access_denied' => 'You do not have access to this organisation.',
        'invalid_currency_code' => 'The currency code must be a valid 3-letter ISO code.',
        'invalid_timezone' => 'The timezone must be a valid timezone.',
        'limit_integer' => 'The limit must be an integer.',
        'limit_min' => 'The limit must be at least 1.',
        'limit_max' => 'The limit must not be greater than 100.',
        'product_id_must_be_integer' => 'The product ID must be an integer.',
        'product_not_found' => 'The specified product does not exist.',
        'product_id_not_allowed_for_ranking' => 'Product ID is not allowed for product ranking reports.',
        'report_type_required' => 'The report type is required.',
        'invalid_report_type' => 'The report type is invalid.',
        'format_required' => 'The format is required.',
        'invalid_format' => 'The format is invalid.',
        'invalid_filename' => 'The filename contains invalid characters.',
        'filename_too_long' => 'The filename is too long.',
        'max_records_exceeded' => 'The maximum number of records exceeded.',
        'min_records_required' => 'The minimum number of records is required.',
        'invalid_email' => 'The email address is invalid.',
    ],

    // Activity log validation messages
    'activity_log' => [
        'per_page_max' => 'The per page value must not be greater than 100.',
        'subject_type_invalid' => 'The subject type is invalid.',
        'event_invalid' => 'The event type must be one of: created, updated, deleted.',
        'date_from_before_date_to' => 'The start date must be before or equal to the end date.',
        'date_to_after_date_from' => 'The end date must be after or equal to the start date.',
        'date_to_not_future' => 'The end date cannot be in the future.',
    ],

    // Email template validation messages
    'email_template' => [
        'code_format' => 'The template code must only contain lowercase letters, numbers, and underscores.',
        'code_unique' => 'The template code has already been taken.',
        'variable_format' => 'Variable names must only contain lowercase letters, numbers, and underscores.',
        'content_required' => 'The email content is required.',
        'subject_required' => 'The email subject is required.',
        'undeclared_variables' => 'The following variables are used in the template but not declared: :variables',
        'unused_variables' => 'The following declared variables are not used in the template: :variables',
    ],

    /*
    |--------------------------------------------------------------------------
    | Financial Statement Validation Messages
    |--------------------------------------------------------------------------
    */
    'financial_statements' => [
        'report_type' => [
            'required' => 'The report type is required.',
            'in' => 'The report type must be a valid type',
        ],
        'report_title' => [
            'required' => 'The report title is required.',
            'max' => 'The report title may not be greater than 255 characters.',
        ],
        'start_date' => [
            'required' => 'The start date is required.',
            'date' => 'The start date is not a valid date.',
        ],
        'end_date' => [
            'required' => 'The end date is required.',
            'date' => 'The end date is not a valid date.',
            'after_or_equal' => 'The end date must be a date after or equal to start date.',
        ],
        'organisation_id' => [
            'required' => 'The organisation ID is required.',
            'exists' => 'The selected organisation does not exist.',
        ],
        'status' => [
            'in' => 'The status must be a valid status',
        ],
        'total_amount' => [
            'integer' => 'The total amount must be an integer.',
            'min' => 'The total amount must not be negative.',
        ],
        'total_quantity' => [
            'integer' => 'The total quantity must be an integer.',
            'min' => 'The total quantity must not be negative.',
        ],
        'total_orders' => [
            'integer' => 'The total orders must be an integer.',
            'min' => 'The total orders must not be negative.',
        ],
        'remarks' => [
            'max' => 'The remarks may not be greater than 65535 characters.',
        ],
        'order_ids' => [
            'array' => 'The order IDs must be an array.',
            'integer' => 'The order ID must be an integer.',
            'exists' => 'The selected order does not exist.',
        ],
    ],
];
