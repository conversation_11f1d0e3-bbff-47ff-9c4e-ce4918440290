<?php

return [
    'product_sync_started' => 'Product synchronization started',
    'product_sync_completed' => 'Product synchronization completed successfully',
    'product_sync_failed' => 'Product synchronization failed',
    'order_sync_started' => 'Order synchronization started',
    'order_sync_completed' => 'Order synchronization completed successfully',
    'order_sync_failed' => 'Order synchronization failed',
    'batch_processing' => 'Processing batch :batch_id',
    'records_processed' => ':count records processed',
    'sync_status' => [
        'pending' => 'Pending',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'failed' => 'Failed',
    ],
    'record_status' => [
        'pending' => 'Pending',
        'success' => 'Success',
        'failed' => 'Failed',
        'skipped' => 'Skipped',
    ],
    'logs_retrieved' => 'Sync logs retrieved successfully',
    'log_not_found' => 'Sync log not found',
    'log_details_retrieved' => 'Sync log details retrieved successfully',
    'sync_triggered' => 'Sync triggered successfully',
    'sync_trigger_failed' => 'Failed to trigger sync',
    'sync_queued' => 'Sync job queued successfully',
    'sync_already_running' => 'Sync job is already running',
    'retry_started' => 'Retry synchronization started successfully',
    'retry_failed' => 'Failed to retry synchronization',
    'retry_queued' => 'Retry sync job queued successfully',
    'retry_invalid_status' => 'Cannot retry synchronization with current status',
    'progress_retrieved' => 'Sync progress retrieved successfully',
    'progress_not_found' => 'Sync progress not found',
    'progress_retrieval_failed' => 'Failed to retrieve sync progress',
    'active_jobs_retrieved' => 'Active sync jobs retrieved successfully',
    'active_jobs_retrieval_failed' => 'Failed to retrieve active sync jobs',
    'job_cleaned_up' => 'Sync job cleaned up successfully',
    'job_cleanup_failed' => 'Failed to clean up sync job',
    'job_not_found' => 'Sync job not found',
    'job_details_retrieved' => 'Sync job details retrieved successfully',
    'job_details_retrieval_failed' => 'Failed to retrieve sync job details',
    'invalid_sync_type' => 'Invalid sync type for revalidation',
    'sync_not_completed' => 'Sync must be completed before revalidation',
    'no_validation_results' => 'No validation results found for this sync',
    'validation_already_passed' => 'Validation has already passed',
    'validation_not_eligible_for_retry' => 'Validation is not eligible for retry',
    'validation_already_running' => 'Validation is already running for this sync',
    'revalidation_queued' => 'Revalidation job queued successfully',
    'revalidation_failed' => 'Failed to queue revalidation job',
    'batch_revalidation_completed' => 'Batch revalidation completed',
    'batch_revalidation_all_failed' => 'All batch revalidation requests failed',
    'revalidation_candidates_retrieved' => 'Revalidation candidates retrieved successfully',
];
