<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Statement - Organization Name</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .report-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .organization-name {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-period {
            font-size: 14px;
            color: #666;
        }
        .report-code {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #1e40af;
            margin-bottom: 15px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .summary-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            text-align: center;
        }
        .summary-card.amount {
            border-left-color: #1e40af;
        }
        .summary-card.quantity {
            border-left-color: #ffc107;
        }

        .summary-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .summary-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: none;
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #dee2e6;
        }

        /* Modern table styles for country sales */
        .sales-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sales-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            padding: 16px 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .sales-table td {
            padding: 12px;
            font-size: 14px;
            border-bottom: 1px solid #f1f3f4;
        }
        .positive {
            color: #28a745;
        }
        .product-row {
            background-color: #fff8e1;
            font-weight: bold;
        }
        .region-row {
            background-color: #f9f9f9;
        }
        .total-row {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-published {
            background-color: #d4edda;
            color: #155724;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .note {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .signature-section {
            margin-top: 40px;
            text-align: right;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }

        .adjustment-row {
            background-color: #fff3cd;
        }
        .negative {
            color: #dc3545;
        }

        /* Modern collapsible styles */
        .product-row {
            background-color: white;
            cursor: pointer;
            user-select: none;
            transition: background-color 0.2s ease;
            position: relative;
        }

        .product-row:hover {
            background-color: #f8f9fa;
        }

        .product-row.expanded {
            background-color: #f8f9fa;
        }

        .expand-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            transition: transform 0.3s ease;
            font-size: 12px;
            color: #6c757d;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .country-details {
            background-color: #fafbfc;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .country-details.collapsed {
            max-height: 0;
        }

        .country-row {
            background-color: #fafbfc;
            border-left: 3px solid #e9ecef;
        }

        .country-row td {
            padding-left: 32px;
            color: #6c757d;
            font-size: 13px;
        }

        .country-row td:first-child {
            padding-left: 40px;
        }

        .action-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .action-btn:hover {
            background: #0056b3;
        }

        .progress-bar {
            width: 60px;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .adjustments-section {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
        }

        .adjustments-header {
            font-weight: bold;
            margin-bottom: 8px;
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 25px;
        }

        .adjustments-header::after {
            content: '▼';
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
            font-size: 12px;
        }

        .adjustments-header.collapsed::after {
            transform: translateY(-50%) rotate(-90deg);
        }

        .adjustments-content {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .adjustments-content.collapsed {
            max-height: 0;
        }

        .collapsible-content {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.collapsed {
            max-height: 0;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="header">
            <div class="organization-name">Organization Name</div>
            <div class="report-title">June 2024 - Sales Report</div>
            <div class="report-period">Report Period: June 1, 2024 - June 30, 2024</div>
            <div class="report-code">Report Code: ORG001-202406-m</div>
            <div class="report-period">
                Status: <span class="status-badge status-published">Published</span>
            </div>
        </div>

        <div class="section">
            <div class="section-title">I. Report Summary</div>
            <div class="summary-grid">
                <div class="summary-card amount">
                    <div class="summary-value">$1,500.00</div>
                    <div class="summary-label">Total Sales Amount</div>
                </div>
                <div class="summary-card quantity">
                    <div class="summary-value">250</div>
                    <div class="summary-label">Total Units Sold</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">II. Product Sales by Country</div>
            <table class="sales-table">
                <thead>
                    <tr>
                        <th style="width: 35%;">产品信息</th>
                        <th style="width: 10%; text-align: center;">总销量</th>
                        <th style="width: 12%; text-align: center;">总销售额</th>
                        <th style="width: 10%; text-align: center;">收入占比</th>
                        <th style="width: 18%; text-align: center;">主要市场</th>
                        <th style="width: 15%; text-align: center;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="product-row" data-target="product1-countries">
                        <td>
                            <span class="expand-icon">▶</span>
                            <strong>Grisaia Complete Liberation Romanesque: Estio Regia</strong><br>
                            <small style="color: #6c757d;">SKU: GCL15</small>
                        </td>
                        <td style="text-align: center;">200</td>
                        <td style="text-align: center;">$1,200</td>
                        <td style="text-align: center;">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 80%;"></div>
                            </div>
                            80.0%
                        </td>
                        <td style="text-align: center;">United States (65)</td>
                        <td style="text-align: center;">
                            <button class="action-btn">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div id="product1-countries" class="country-details collapsed">
                <table class="sales-table" style="margin-top: 0; box-shadow: none;">
                    <tbody>
                        <tr class="country-row">
                            <td style="width: 35%;">United States</td>
                            <td style="width: 10%; text-align: center;">65</td>
                            <td style="width: 12%; text-align: center;">$390</td>
                            <td style="width: 10%; text-align: center;">$273.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">Germany</td>
                            <td style="width: 10%; text-align: center;">35</td>
                            <td style="width: 12%; text-align: center;">$210</td>
                            <td style="width: 10%; text-align: center;">$147.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">United Kingdom</td>
                            <td style="width: 10%; text-align: center;">30</td>
                            <td style="width: 12%; text-align: center;">$180</td>
                            <td style="width: 10%; text-align: center;">$126.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">Japan</td>
                            <td style="width: 10%; text-align: center;">25</td>
                            <td style="width: 12%; text-align: center;">$150</td>
                            <td style="width: 10%; text-align: center;">$105.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">Canada</td>
                            <td style="width: 10%; text-align: center;">20</td>
                            <td style="width: 12%; text-align: center;">$120</td>
                            <td style="width: 10%; text-align: center;">$84.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">Others (15 countries)</td>
                            <td style="width: 10%; text-align: center;">25</td>
                            <td style="width: 12%; text-align: center;">$150</td>
                            <td style="width: 10%; text-align: center;">$105.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <table class="sales-table" style="margin-top: 10px;">
                <tbody>
                    <tr class="product-row" data-target="product2-countries">
                        <td>
                            <span class="expand-icon">▶</span>
                            <strong>Sonora</strong><br>
                            <small style="color: #6c757d;">SKU: S4417</small>
                        </td>
                        <td style="text-align: center;">50</td>
                        <td style="text-align: center;">$300</td>
                        <td style="text-align: center;">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 20%;"></div>
                            </div>
                            20.0%
                        </td>
                        <td style="text-align: center;">Germany (18)</td>
                        <td style="text-align: center;">
                            <button class="action-btn">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div id="product2-countries" class="country-details collapsed">
                <table class="sales-table" style="margin-top: 0; box-shadow: none;">
                    <tbody>
                        <tr class="country-row">
                            <td style="width: 35%;">Germany</td>
                            <td style="width: 10%; text-align: center;">18</td>
                            <td style="width: 12%; text-align: center;">$108</td>
                            <td style="width: 10%; text-align: center;">$75.60</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">United States</td>
                            <td style="width: 10%; text-align: center;">15</td>
                            <td style="width: 12%; text-align: center;">$90</td>
                            <td style="width: 10%; text-align: center;">$63.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">United Kingdom</td>
                            <td style="width: 10%; text-align: center;">10</td>
                            <td style="width: 12%; text-align: center;">$60</td>
                            <td style="width: 10%; text-align: center;">$42.00</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                        <tr class="country-row">
                            <td style="width: 35%;">Others (8 countries)</td>
                            <td style="width: 10%; text-align: center;">7</td>
                            <td style="width: 12%; text-align: center;">$42</td>
                            <td style="width: 10%; text-align: center;">$29.40</td>
                            <td style="width: 18%; text-align: center;">-</td>
                            <td style="width: 15%; text-align: center;">-</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <table class="sales-table" style="margin-top: 15px; background-color: #f8f9fa;">
                <tbody>
                    <tr style="font-weight: bold; background-color: #e9ecef;">
                        <td style="width: 35%;"><strong>TOTAL</strong></td>
                        <td style="text-align: center; width: 10%;"><strong>250</strong></td>
                        <td style="text-align: center; width: 12%;"><strong>$1,500.00</strong></td>
                        <td style="text-align: center; width: 10%;"><strong>100%</strong></td>
                        <td style="text-align: center; width: 18%;"><strong>Multi-Region</strong></td>
                        <td style="text-align: center; width: 15%;"><strong>-</strong></td>
                    </tr>
                </tbody>
            </table>

            <div class="note">
                <strong>Notes:</strong>
                <br>• Revenue Share calculated at 70% of Net Sales
                <br>• Countries with less than 5 units sold are grouped under "Others"
                <br>• All amounts are net of refunds and platform fees, in USD
                <br>• Data sorted by units sold within each product
            </div>
        </div>

        <div class="section">
            <div class="section-title">III. Product Performance & Changes</div>
            <table>
                <thead>
                    <tr>
                        <th style="width: 25%;">Product</th>
                        <th style="width: 20%;">Current Period</th>
                        <th style="width: 20%;">Previous Period</th>
                        <th style="width: 20%;">Change</th>
                        <th style="width: 15%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <strong>Game Title 1</strong><br>
                            <small style="color: #666;">game-title-1</small>
                        </td>
                        <td class="amount">
                            200 units<br>
                            <span class="positive">$1,200.00</span>
                        </td>
                        <td class="amount">
                            210 units<br>
                            <span class="positive">$1,260.00</span>
                        </td>
                        <td class="amount">
                            <span class="negative">-10 units</span><br>
                            <span class="negative">-$60.00</span><br>
                            <small>(-4.8%)</small>
                        </td>
                        <td style="text-align: center;">
                            <button type="button" class="adjustments-header collapsed" data-target="adjustments-game1" style="background: none; border: none; color: #1e40af; cursor: pointer; font-size: 12px; text-decoration: underline;">
                                View Details
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="5" style="padding: 0;">
                            <div id="adjustments-game1" class="adjustments-section collapsible-content collapsed" style="max-height: 0; overflow: hidden; transition: max-height 0.3s ease;">
                                <div style="font-weight: bold; margin-bottom: 8px; color: #333;">Recent Adjustments:</div>
                                <div class="adjustment-row" style="padding: 4px; margin: 2px 0; border-radius: 3px; font-size: 12px;">
                                    <strong>Mar 2024:</strong> Forced Refund<br>
                                    -15 units, -$90.00
                                </div>
                                <div class="adjustment-row" style="padding: 4px; margin: 2px 0; border-radius: 3px; font-size: 12px;">
                                    <strong>Mar 2024:</strong> Manual Adj.<br>
                                    +5 units, +$30.00
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>Game Title 2</strong><br>
                            <small style="color: #666;">game-title-2</small>
                        </td>
                        <td class="amount">
                            50 units<br>
                            <span class="positive">$300.00</span>
                        </td>
                        <td class="amount">
                            58 units<br>
                            <span class="positive">$348.00</span>
                        </td>
                        <td class="amount">
                            <span class="negative">-8 units</span><br>
                            <span class="negative">-$48.00</span><br>
                            <small>(-13.8%)</small>
                        </td>
                        <td style="text-align: center;">
                            <button type="button" class="adjustments-header collapsed" data-target="adjustments-game2" style="background: none; border: none; color: #1e40af; cursor: pointer; font-size: 12px; text-decoration: underline;">
                                View Details
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="5" style="padding: 0;">
                            <div id="adjustments-game2" class="adjustments-section collapsible-content collapsed" style="max-height: 0; overflow: hidden; transition: max-height 0.3s ease;">
                                <div style="font-weight: bold; margin-bottom: 8px; color: #333;">Recent Adjustments:</div>
                                <div class="adjustment-row" style="padding: 4px; margin: 2px 0; border-radius: 3px; font-size: 12px;">
                                    <strong>Feb 2024:</strong> Chargeback<br>
                                    -8 units, -$48.00
                                </div>
                                <div class="adjustment-row" style="padding: 4px; margin: 2px 0; border-radius: 3px; font-size: 12px;">
                                    <strong>Jan 2024:</strong> Price Adj.<br>
                                    0 units, -$25.00
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr class="total-row">
                        <td><strong>Total</strong></td>
                        <td class="amount">
                            <strong>250 units</strong><br>
                            <strong class="positive">$1,500.00</strong>
                        </td>
                        <td class="amount">
                            <strong>268 units</strong><br>
                            <strong class="positive">$1,608.00</strong>
                        </td>
                        <td class="amount">
                            <strong class="negative">-18 units</strong><br>
                            <strong class="negative">-$108.00</strong><br>
                            <small>(-6.7%)</small>
                        </td>
                        <td style="text-align: center;">
                            <strong style="font-size: 12px;">Summary</strong>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="5" style="padding: 0;">
                            <div class="adjustments-section" style="margin-top: 0;">
                                <div style="font-weight: bold; margin-bottom: 8px; color: #333;">Total Adjustments Summary:</div>
                                <div style="font-size: 12px; color: #666;">
                                    <strong>Total Impact:</strong> -18 units, -$133.00<br>
                                    <strong>Main Factors:</strong> Forced refunds, chargebacks, and price adjustments
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="note">
                <strong>Notes:</strong>
                <br>• Previous period refers to May 2024 data (before adjustments)
                <br>• Recent adjustments show historical corrections affecting current reporting
                <br>• Negative changes may be due to market conditions or historical adjustments
                <br>• All adjustments are retroactively applied to their original periods
                <br>• Click "View Details" to see specific adjustment information for each product
            </div>
        </div>

        <div class="signature-section">
            <p>Report Generated: July 1, 2024 10:30:00</p>
            <p>System Version: JAST Partner v1.0</p>
            <p>Data Source: Financial Statement System</p>
        </div>
    </div>

    <script>
        // Simulate data loading and dynamic updates
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Financial statement page loaded');

            // Initialize collapsible functionality
            initializeCollapsibles();
        });

        function initializeCollapsibles() {
            // Handle product row clicks for country details
            const productRows = document.querySelectorAll('.product-row[data-target]');
            productRows.forEach(row => {
                row.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const content = document.getElementById(targetId);
                    const icon = this.querySelector('.expand-icon');

                    if (content && icon) {
                        toggleProductDetails(this, content, icon);
                    }
                });
            });

            // Handle adjustment details collapsibles
            const adjustmentButtons = document.querySelectorAll('.adjustments-header[data-target]');
            adjustmentButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const content = document.getElementById(targetId);

                    if (content) {
                        toggleAdjustmentDetails(this, content);
                    }
                });
            });
        }

        function toggleProductDetails(row, content, icon) {
            const isExpanded = row.classList.contains('expanded');

            if (isExpanded) {
                // Collapse
                row.classList.remove('expanded');
                icon.classList.remove('expanded');
                content.classList.add('collapsed');
                content.style.maxHeight = '0';
            } else {
                // Expand
                row.classList.add('expanded');
                icon.classList.add('expanded');
                content.classList.remove('collapsed');
                content.style.maxHeight = content.scrollHeight + 'px';
            }
        }

        function toggleAdjustmentDetails(button, content) {
            const isCollapsed = button.classList.contains('collapsed');

            if (isCollapsed) {
                // Expand
                button.classList.remove('collapsed');
                button.textContent = 'Hide Details';
                content.classList.remove('collapsed');
                content.style.maxHeight = content.scrollHeight + 'px';
            } else {
                // Collapse
                button.classList.add('collapsed');
                button.textContent = 'View Details';
                content.classList.add('collapsed');
                content.style.maxHeight = '0';
            }
        }
    </script>
</body>
</html>
