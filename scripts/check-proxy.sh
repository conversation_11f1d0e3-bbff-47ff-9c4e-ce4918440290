#!/bin/bash

# Docker 构建代理检测脚本
# 用于检测和验证代理配置是否适用于 Docker 构建

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测是否在 WSL2 环境中
is_wsl2() {
    if [ -f /proc/version ] && grep -q "microsoft" /proc/version; then
        return 0
    fi
    return 1
}

# 获取宿主机 IP 地址
get_host_ip() {
    local host_ip=""

    if is_wsl2; then
        # WSL2 环境：优先使用默认路由获取网关 IP
        if command -v ip >/dev/null 2>&1; then
            host_ip=$(ip route show | grep default | awk '{print $3}' | head -n1)
        fi

        # 备用方法：检查是否有 Windows 宿主机的特殊 IP
        if [ -z "$host_ip" ]; then
            # 在某些 WSL2 配置中，可能需要使用特定的 IP 范围
            # 这里我们使用默认路由作为主要方法
            host_ip="***********"  # 从路由表中看到的网关
        fi
    else

        # 方法1: 通过默认路由获取网关
        if command -v ip >/dev/null 2>&1; then
            host_ip=$(ip route show default | awk '/default/ {print $3}' | head -n1)
        fi

        # 方法2: 通过 hostname 获取
        if [ -z "$host_ip" ] && command -v hostname >/dev/null 2>&1; then
            host_ip=$(hostname -I | awk '{print $1}')
        fi

        # 方法3: 通过 ifconfig 获取
        if [ -z "$host_ip" ] && command -v ifconfig >/dev/null 2>&1; then
            host_ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -n1)
        fi
    fi

    echo "$host_ip"
}



# 生成构建命令
generate_build_commands() {
    local proxy="$1"

    print_info "推荐的构建命令:"
    echo
    echo -e "${GREEN}# 使用代理构建${NC}"
    echo "make build HTTP_PROXY=http://$proxy HTTPS_PROXY=http://$proxy"
    echo
    echo -e "${GREEN}# 使用代理重新构建${NC}"
    echo "make rebuild HTTP_PROXY=http://$proxy HTTPS_PROXY=http://$proxy"
    echo
    echo -e "${GREEN}# 使用代理初始化项目${NC}"
    echo "make init HTTP_PROXY=http://$proxy HTTPS_PROXY=http://$proxy"
}

# 主函数
main() {
    echo "========================================"
    echo "    Docker 构建代理检测工具"
    echo "========================================"
    echo

    # 获取宿主机 IP
    print_info "获取宿主机 IP 地址..."
    host_ip=$(get_host_ip)

    if [ -z "$host_ip" ]; then
        print_error "无法获取宿主机 IP 地址"
        exit 1
    fi

    if is_wsl2; then
        print_success "宿主机 IP: $host_ip (WSL2 环境)"
    else
        print_success "宿主机 IP: $host_ip (原生 Linux 环境)"
    fi
    echo

    # 检测本地代理
    print_info "检测本地代理端口..."
    local_proxies=()
    ports=("7890" "10809" "1080" "8080" "3128")

    for port in "${ports[@]}"; do
        if nc -z "127.0.0.1" "$port" 2>/dev/null; then
            print_success "发现本地代理: 127.0.0.1:$port"
            local_proxies+=("127.0.0.1:$port")
        fi
    done

    if [ ${#local_proxies[@]} -eq 0 ]; then
        print_error "未发现本地代理服务"
        print_info "请确保代理软件正在运行"
        exit 1
    fi

    echo

    # 检测远程代理
    if is_wsl2; then
        print_info "检测远程代理端口 ($host_ip) - WSL2 环境..."
    else
        print_info "检测远程代理端口 ($host_ip)..."
    fi
    remote_proxies=()

    for port in "${ports[@]}"; do
        if nc -z "$host_ip" "$port" 2>/dev/null; then
            print_success "发现远程代理: $host_ip:$port"
            remote_proxies+=("$host_ip:$port")
        fi
    done

    if [ ${#remote_proxies[@]} -eq 0 ]; then
        echo
        print_warning "代理服务不允许外部访问"

        if is_wsl2; then
            print_info "检测到 WSL2 环境，需要特殊配置:"
            echo "  1. Clash for Windows: 开启 'Allow LAN'，设置 'Bind Address' 为 '0.0.0.0'"
            echo "  2. Windows 防火墙: 添加端口 7890 的入站规则"
            echo "  3. 重启代理软件"
            echo
            print_info "详细配置步骤请查看: scripts/wsl2-proxy-setup.md"
        else
            print_info "需要配置代理软件允许局域网访问:"
            echo "  - Clash: 设置 allow-lan: true, bind-address: \"*\""
            echo "  - V2Ray: 设置 listen: \"0.0.0.0\""
            echo "  - Shadowsocks: 设置 local_address: \"0.0.0.0\""
        fi

        echo
        print_info "配置完成后重新运行此脚本"
        exit 1
    fi

    echo

    # 测试第一个可用的代理
    best_proxy="${remote_proxies[0]}"
    print_info "测试代理连接: $best_proxy"

    if curl -x "http://$best_proxy" --connect-timeout 10 -s -I "http://www.google.com" >/dev/null 2>&1; then
        print_success "代理连接测试成功"
    else
        print_warning "代理连接测试失败，但端口是开放的"
        print_info "可能是代理配置问题，仍可尝试使用"
    fi

    echo
    generate_build_commands "$best_proxy"

    echo
    print_info "如需更多帮助，请查看: docs/CHINA_MIRRORS.md"
}

# 运行主函数
main "$@"
